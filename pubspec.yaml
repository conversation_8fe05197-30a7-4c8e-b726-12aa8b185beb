name: fandooq
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+26

environment:
  sdk: ^3.3.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # dart run build_runner build --delete-conflicting-outputs

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_svg: any
  cached_network_image: ^3.3.1
  carousel_slider: ^5.0.0
  phone_numbers_parser: ^9.0.3

  equatable: ^2.0.5
  elegant_notification: ^1.13.0
  font_awesome_flutter: ^10.5.0
  visibility_detector: ^0.4.0+2
  dio: ^5.3.3
  dartz: ^0.10.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  hive_generator: ^2.0.1
  logger: ^2.0.2+1
  flutter_spinkit: ^5.2.0

  hugeicons: any
  google_maps_flutter: ^2.9.0
  animate_do: any
  uuid: any
  intl: any

  freezed_annotation: ^2.0.0 # لتوفير التعليقات (annotations) لـ Freezed
  json_annotation: ^4.8.0 # لتوفير دعم JSON serialization

  extended_nested_scroll_view: ^6.2.1
  shimmer: ^3.0.0
  lottie: ^3.0.0
  smooth_page_indicator: ^1.1.0
  flutter_sizer: ^1.0.4
  dropdown_button2: ^2.3.9
  liquid_pull_to_refresh: ^3.0.1
  syncfusion_flutter_sliders: ^24.2.9

  geolocator: ^12.0.0
  permission_handler: ^10.2.0
  image_picker: ^1.0.4

  google_map_location_picker:
    path: ./packages/google_map_location_picker-master
  firebase_core: ^3.6.0
  firebase_messaging: any
  flutter_local_notifications: ^17.2.2

  awesome_dialog: ^3.2.0
  url_launcher: ^6.2.5
  lazy_load_scrollview: 1.3.0

  vy_string_utils: ^0.4.6

  share_plus: ^10.0.2
  uni_links: ^0.5.1
  flutter_date_range_picker:
    path: ./packages/flutter_date_range_picker-0.0.14

  package_info_plus: ^8.0.2

  string_similarity: ^2.0.0

  toastification: ^1.2.1

  #  flutter_i18n: ^0.35.0

  flutter_staggered_grid_view: ^0.7.0

  #  get: any
  get: ^4.6.6
  #  get:
  #    path: ./packages/get-5.0.0-release-candidate-5

  #  get: ^5.0.0-release-candidate-9.2.1

  #  connectivity_plus:
  #    path: ./packages/connectivity_plus-6.0.3

  shared_preferences: ^2.2.3

  restart_app: ^1.2.1

  flutter_localization: any

  oktoast: ^3.1.5

  dart_ipify: ^1.1.1

  salomon_bottom_bar: ^3.3.2

  dotted_line: ^3.2.2

  chips_choice: ^3.0.1

  #  stripe_sdk: ^5.0.0

  flutter_stripe: ^11.4.0

  credit_card_form: ^0.0.7

  #  mek_stripe_terminal: ^3.7.0

  dotenv: ^4.2.0

  expandable: ^5.0.1

  loading_animation_widget: ^1.3.0

  modal_bottom_sheet: ^3.0.0-pre

  country_ip: ^3.0.0

  web_socket_channel: ^3.0.1

  flutter_html: ^3.0.0-beta.2
  html_unescape: ^2.0.0

  #  alice:
  #    path: ./packages/alice-1.0.0-dev.9
  #  alice_dio:
  #    path: ./packages/alice_dio-1.0.4
  #

  path_provider: ^2.1.4

  #  fandooq_helper_package:
  #    git:
  #      url: https://github.com/abdelrahmantarek/fandooq-dart-helper.git
  #      ref: main

  fx_stepper:
    path: ./packages/fx_stepper-main
  #  fandooq_helper_package:
  #    path: ../fandooq-dart-helper

  #  core:
  #    path: ./modules/core

  build_runner: ^2.3.2
  dotted_border: ^2.0.0+1
  pinput: ^4.0.0
  measure_size: ^3.0.1
  flutter_map: ^5.0.0
  latlong2: ^0.9.0

  device_info_plus: any
  table_calendar: ^3.1.3

dependency_overrides:
  web: ^0.5.1
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  change_app_package_name: ^1.3.0
  build_runner: ^2.1.7 # أداة توليد الكود
  freezed: ^2.0.0 # مكتبة Freezed لتوليد الأصناف غير القابلة للتغيير
  json_serializable: ^6.1.5 # مكتبة توليد كود JSON

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  assets:
    - assets/
    - assets/icon/
    - assets/json/
    - assets/json/countries/
    - assets/icon/amenities/
    - assets/maps/
    - assets/images/
    - assets/payments/
    - assets/lottie/
    - assets/flags/
    - assets/i18n/
    - assets/facilities/
    - assets/i18n_namespace/

  fonts:
    - family: Tajawal
      fonts:
        - asset: assets/fonts/tajawal/Tajawal-Regular.ttf
        - asset: assets/fonts/tajawal/Tajawal-Bold.ttf
        - asset: assets/fonts/tajawal/Tajawal-ExtraBold.ttf
        - asset: assets/fonts/tajawal/Tajawal-Black.ttf
        - asset: assets/fonts/tajawal/Tajawal-Light.ttf
        - asset: assets/fonts/tajawal/Tajawal-ExtraLight.ttf
        - asset: assets/fonts/tajawal/Tajawal-Medium.ttf

    - family: Poppins
      fonts:
        - asset: assets/fonts/poppins/Poppins-Regular.ttf
        - asset: assets/fonts/poppins/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/poppins/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/poppins/Poppins-Black.ttf
          weight: 900
        - asset: assets/fonts/poppins/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/poppins/Poppins-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/poppins/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/poppins/Poppins-Thin.ttf
          weight: 100
