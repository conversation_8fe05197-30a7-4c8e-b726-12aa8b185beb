// ignore_for_file: depend_on_referenced_packages

import 'dart:developer';
import 'dart:io';
import 'dart:math';
import 'package:country_ip/country_ip.dart';
import 'package:fandooq/app-is-ready.dart';
import 'package:fandooq/core/components/currency/lib/models/currency.dart';
import 'package:fandooq/core/components/overly/overlay_view.dart';
import 'package:fandooq/core/components/pdf/pdf_creator.dart';
import 'package:fandooq/core/config/strip/strip-config.dart';
import 'package:fandooq/core/data_base/hive_helpers/hive_helper.dart';
import 'package:fandooq/core/fcm/fcm.dart';
import 'package:fandooq/core/log/log-handler.dart';
import 'package:fandooq/core/network_provider/web_socket.dart';
import 'package:fandooq/core/setting_app/setting_app.dart';
import 'package:fandooq/core/translate/translate_controller.dart';
import 'package:fandooq/feature_plus/home/<USER>';
import 'package:fandooq/feature_plus/splash/splash_plus.dart';
import 'package:fandooq/feature_plus/hotels/data/hotels_data_plus.dart';
import 'package:fandooq/repos/repos.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_map_location_picker/google_map_location_picker.dart';
import 'package:oktoast/oktoast.dart';
import 'package:toastification/toastification.dart';
import 'binding.dart';
import 'core/cache/preference_manager.dart';
import 'core/components-app/components.dart';
import 'core/core.dart';
import 'core/data_base/features/global_app/lang.dart';
import 'core/env.dart';
import 'core/lang/translation.dart';
import 'core/router/app_pages.dart';
import 'core/theme/app_themes.dart';
import 'core/utils/constant.dart';
import 'firebase_options.dart';

import 'package:flutter_localizations/flutter_localizations.dart';

// Production configuration
class AppConfig {
  static const bool enableDebugLogs = kDebugMode;
  static const bool enableLocalTesting = false;
  static const bool enableRateChecking = true;
}

void main() async {

  WidgetsFlutterBinding.ensureInitialized();

  LogHandler.init(
      "/Users/<USER>/Documents/workspace/clients/adnan-abdo/fandooq/mobile/fandooq-mobile-app/log");

  Env.init();

  await StripConfig.init();

  // TranslateController.downloadModel();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  Fcm.instance.init();

  await HiveHelpers.initSystemHive();

  await PreferenceManager.init();

  GooglePlaces.initGooglePlaces(
      apiKey: googleApiKey, language: DBLang.getCodeLang().name);

  HttpOverrides.global = MyHttpOverrides();

  await Core.setUp();

  HomeIntegration.initialize();

  // If you want to remove the leading hash ( # ) from the URL of your Flutter web app,
  // you can simply call setPathUrlStrategy in the main function of your app:
  // setPathUrlStrategy();

  // test some thing
  // please don't remove it
  // String result = generateConstants(facilities);

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    onAppReady();
    super.initState();
  }

  var router = Get.put(AppPages());

  @override
  Widget build(BuildContext context) {
    return OKToast(
        child: GestureDetector(
      behavior: HitTestBehavior.deferToChild,
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.focusedChild?.unfocus();
        }
      },
      child: MediaQuery(
        data: MediaQuery.of(context).copyWith(
          textScaler: const TextScaler.linear(1 /
              1), //so that the font size doesnot change depending on device font size
        ),
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Stack(
            children: [

              Positioned.fill(
                child: GetMaterialApp(
                  scaffoldMessengerKey: Core.rootScaffoldMessengerKey,
                  title: 'Fandooq',
                  navigatorKey: navigatorKey, // Using the navigator key
                  initialRoute: AppPages.INITIAL, // Set the initial route
                  getPages: AppPages.appRoutes, // Register the routes
                  translations: MyTranslations(), // Add GetX translations
                  textDirection: TextDirection.ltr,
                  supportedLocales: [
                    const Locale('en'), // English
                    const Locale('ar')
                    // const Locale('de'), // German, etc.
                  ],
                  localizationsDelegates: [
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ],
                  debugShowCheckedModeBanner: false,
                  // initialRoute: AppPages.INITIAL,
                  // getPages: AppPages.routes,
                  builder: (context, child) {
                    return ToastificationConfigProvider(
                      config: const ToastificationConfig(
                        alignment: Alignment.center,
                        itemWidth: 440,
                        animationDuration: Duration(milliseconds: 500),
                      ),
                      child: child ?? const SizedBox(),
                    );
                  },
                  // supportedLocales: SUPPORTED_LOCALES,
                  // localizationsDelegates: [CountryLocalizations.delegate],
                  theme: ThemeApp.light(),
                  // darkTheme: AppThemes.dark,
                  locale: DBLang.getLocalLang(),
                  themeMode: ThemeApp.themeMode(),
                ),
              ),
              // Splash screen will be handled by navigation
            ],
          ),
        ),
      ),
    ));
  }
}

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}

class ColorListPage extends StatelessWidget {
  const ColorListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة الألوان'),
      ),
      body: ListView.separated(
        itemCount: 200, // عدد العناصر في القائمة
        itemBuilder: (context, index) {
          // إنشاء لون عشوائي لكل عنصر
          final randomColor = Color.fromARGB(
            255,
            Random().nextInt(256),
            Random().nextInt(256),
            Random().nextInt(256),
          );
          return Container(
            height: 50,
            color: randomColor,
            child: Center(
              child: Text(
                'لون رقم $index',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          );
        },
        separatorBuilder: (s, w) {
          return const SizedBox();
        },
      ),
    );
  }
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
