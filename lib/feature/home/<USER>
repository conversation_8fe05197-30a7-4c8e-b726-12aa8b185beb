import 'dart:convert';
import 'dart:developer';
import 'dart:math';

import 'package:dartz/dartz.dart' as dartz;
import 'package:dotted_border/dotted_border.dart';
import 'package:fandooq/core/components/buttons/counter.dart';
import 'package:fandooq/core/components/column_row.dart';
import 'package:fandooq/core/components/fade_index.dart';
import 'package:fandooq/core/components/free_place_search/lib/models/address.dart';
import 'package:fandooq/core/components/free_place_search/lib/models/location_info.dart';
import 'package:fandooq/core/components/free_place_search/lib/place_search.dart';
import 'package:fandooq/core/components/helper_load_ui.dart';
import 'package:fandooq/core/components/messages.dart';
import 'package:fandooq/core/components/popup/custom_page_dialog.dart';
import 'package:fandooq/core/components/search/pick_location_result.dart';
import 'package:fandooq/core/components/search/pick_range_date.dart';
import 'package:fandooq/core/services/app_link_handler.dart';
import 'package:fandooq/feature_plus/home/<USER>/guests_search.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:fandooq/feature_plus/hotels/models/hotels_data_search.dart';
import 'package:fandooq/feature_plus/settings/views/settings_screen.dart'
    as settings_plus;
import 'package:fandooq/feature_plus/settings/controllers/settings_controller.dart';
import 'package:fandooq/feature_plus/settings/views/settings_screen.dart';
import 'package:fandooq/feature_plus/user/controllers/user_controller_plus.dart';
import 'package:fandooq/test_auth_persistence.dart';
import 'package:fandooq/test_user_controller_integration.dart';
import 'package:fandooq/core/components/search_text_box.dart';
import 'package:fandooq/core/components/ui/app_text.dart';
import 'package:fandooq/core/components/ui/custom_icon_container.dart';
import 'package:fandooq/core/components/ui/home_hotel_card.dart';
import 'package:fandooq/core/data_base/features/global_app/lang.dart';
import 'package:fandooq/core/enums/langs_enums.dart';
import 'package:fandooq/core/extensions/date.dart';
import 'package:fandooq/core/extensions/languages.dart';
import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/core/lang/app_strings.dart';
import 'package:fandooq/core/lang/src/en.dart';
import 'package:fandooq/core/lang/src/ar.dart';

import 'package:fandooq/core/models/availability/request/guest_date_request.dart' hide GuestDataRequest;
import 'package:fandooq/core/models/availability/request/hotel_request.dart';

import 'package:fandooq/core/models/hotels/hotels.dart';
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/core/responsive/app_size.dart';
import 'package:fandooq/core/responsive/screen_size.dart';
import 'package:fandooq/core/router/app_pages.dart';
import 'package:fandooq/core/setting_app/setting_app.dart';
import 'package:fandooq/core/theme/app_themes.dart';
import 'package:fandooq/core/translate/translate_controller.dart';
import 'package:fandooq/core/utils/base_classes/base_repository.dart';
import 'package:fandooq/core/utils/toast/toast_helper.dart';
import 'package:fandooq/core/extensions/assets_extension.dart';
import 'package:fandooq/core/const/assets.dart';
import 'package:fandooq/core/extensions/icons.dart';
import 'package:fandooq/widgets/app_bar/default_app_bar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show SystemUiOverlayStyle, rootBundle;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_sizer/flutter_sizer.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shimmer/shimmer.dart';

import '../../core/components/app_button.dart';
import '../../core/components/search/pick_guests_result.dart';
import '../../core/models/availability/request/geo_location_request.dart';
import '../../core/models/availability/request/room_request_model.dart';
import '../../core/models/availability/request/stay_request.dart';
import '../../core/models/availability/response/availability_response.dart';
import '../../core/responsive/responsive_ui.dart';
import '../../core/theme/color_manager.dart';
import '../../core/utils/base_classes/use_case.dart';
import '../../core/utils/check_internet.dart';
import '../../repos/hotels/data/hotels.dart';
import '../../widgets/close_back_button.dart';
import '../main/main.dart';
import '../main/presentation/widgets/custom_nav_bar.dart';
import '../../feature_plus/home/<USER>/guests_controller.dart';

//- Data
//* *************** Change Guests Info *****************//
//- Change Guests Info Controller

//* **************** Change Location *****************//
//- Change Location Controller

//- Change Location Screen
//*********************** Home  ***********************//
//- Home Controller
part 'pages/home/<USER>/home_binding.dart';
part 'pages/home/<USER>/home_controller.dart';
part 'pages/home/<USER>/home_state.dart';
//- Home Screen
part 'pages/home/<USER>/home_screen.dart';
part 'pages/home/<USER>/mobile/home_mobile_screen.dart';

part 'pages/home/<USER>/mobile/widget/home_app_bar.dart';
//- Use Cases
