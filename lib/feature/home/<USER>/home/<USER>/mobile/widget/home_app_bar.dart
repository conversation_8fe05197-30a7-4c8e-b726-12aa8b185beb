part of '../../../../../home.dart';

class HomeAppBar extends StatelessWidget {
  const HomeAppBar({super.key});
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 550,
      child: Stack(
        children: [
          // Positioned(
          //   top: 0,
          //   bottom: 150,
          //   right: 0,
          //   left: 0,
          //   child: Container(color: Theme.of(context).primaryColor),
          // ),
          Positioned(
            right: 0,
            left: 0,
            child: SvgPicture.asset(
              Assets.images_home_background_mask_svg,
              height: 400,
              width: MediaQuery.of(context).size.width,
              fit: BoxFit.fill,
            ),
          ),

          const SafeArea(
            child: Padding(
              padding: EdgeInsets.only(right: 15, left: 15, top: 20),
              child: _HeaderSection(),
            ),
          ),

          Positioned(
            bottom: 0,
            right: 15,
            left: 15,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Welcome message with user info
                GetBuilder<UserControllerPlus>(
                  builder: (userController) {
                    if (userController.isAuthenticated) {
                      return Text(
                        '${'home.welcomeBack'.tr} ${userController.displayName}',
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 18,
                          color: Colors.white,
                        ),
                      ).font;
                    }
                    return const SizedBox.shrink();
                  },
                ),
                const SizedBox(height: 10),
                InkWell(
                  onTap: () {
                    // AppLinkTester.testHotelLink();
                    AppLinkTester.testBookingLink();
                  },
                  child: Text(
                    'home.findBestPlace'.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 25,
                      color: Colors.white,
                    ),
                  ).font,
                ),
                const SizedBox(
                  height: 25,
                ),
                const _SearchCard(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _HeaderSection extends StatelessWidget {
  const _HeaderSection({super.key});
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Assets.icon_fandooq_svg.svg(height: 30, width: 30),
              const SizedBox(
                width: 10,
              ),
              Text(
                AppStrings.fandooq.capitalize ?? AppStrings.fandooq,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 28,
                  color: Colors.white,
                ),
              ).font
            ],
          ),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(
              width: 10,
            ),

            // Currency selector button
            Obx(() {
              final settingsController = Get.find<SettingsController>();
              final currentCurrency = settingsController.appSettings.currency;

              return InkWell(
                onTap: () {
                  settingsController.changeCurrency();
                },
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    currentCurrency,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            }),

            const SizedBox(
              width: 10,
            ),

            // Settings Button
            InkWell(
              onTap: () {
                Get.to(() => const SettingsScreen());
              },
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: const Icon(
                  Icons.settings,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _SearchCard extends StatelessWidget {
  const _SearchCard({super.key});
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: ColorManager.lightGrey.withAlpha(50),
        ),
      ),
      child: ColumnRow(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        expandedRow: true,
        maxHeight: 80,
        children: [
          ColumnRowWidget(
              flex: 3,
              child: GetBuilder<GuestsController>(
                id: GuestsController.locationBuilder,
                builder: (controller) {
                  return PickLocationResult(
                    value: controller.locationText,
                    showTitle: true,
                    onChanged: (location) {

                    },
                  );
                },
              )),

          ColumnRowWidget(
              flex: 3,
              child: GetBuilder<GuestsController>(
                id: GuestsController.dateBuilder,
                builder: (controller) {
                  return PickRangeDate(
                    oneIcon: true,
                    showTitle: true,
                    value: HotelsDataSearch.to.request!.stay!,
                    onChanged: (stay) {
                      GuestsController.to.onStayChanged(stay);
                    },
                  );
                },
              )),

          ColumnRowWidget(
              flex: 3,
              child: GetBuilder<GuestsController>(
                id: GuestsController.guestBuilder,
                builder: (controller) {
                  return PickGuestsResult(
                    rooms: HotelsDataSearch.to.request!.rooms,
                    showTitle: true,
                    onChanged: (rooms) {
                      GuestsController.to.onRoomsChanged(rooms);
                    },
                  );
                },
              )),

          // ColumnRowWidget(
          //     flex: 2,
          //     child: SearchTextBox(
          //       hint: "hotel ids like 48723,123123".tr,
          //       onChanged: GuestsController.to.onChangeHotelsIds,
          //     )
          // ),

          ColumnRowWidget(
              flex: 2,
              child: AppButton(
                title: "home.checkAvailability".tr,
                loading: false,
                icon: const Icon(
                  Icons.search,
                  size: 20,
                ),
                onPressed: () async {
                  GuestsController.to.onSubmit(context);
                },
              )),
        ],
      ),
    );
  }
}

class CustomTitleGuests extends StatelessWidget {
  final Widget? icon;
  final Widget title;
  final Function()? onTap;

  const CustomTitleGuests(
      {super.key, this.icon, required this.title, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(8),
        ),
      ),
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: InkWell(
        onTap: onTap,
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (icon != null) icon!,
              const SizedBox(width: 10),
              Expanded(child: title)
            ],
          ),
        ),
      ),
    );
  }
}

class CustomTitleGuestsOption2 extends StatelessWidget {
  final Widget title;
  final Function()? onTap;

  const CustomTitleGuestsOption2({super.key, required this.title, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(8),
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [const SizedBox(width: 15), Expanded(child: title)],
          ),
        ),
      ),
    );
  }
}
