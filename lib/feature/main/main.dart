import 'package:fandooq/core/components/fade_index.dart';
import 'package:fandooq/core/responsive/responsive_ui.dart';
import 'package:fandooq/core/services/confirmation_service.dart';
import 'package:fandooq/core/setting_app/setting_app.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import 'package:fandooq/feature/home/<USER>';
import 'package:fandooq/feature/main/presentation/widgets/custom_nav_bar.dart';
import 'package:fandooq/feature_plus/settings/views/settings_screen.dart';
import 'package:fandooq/feature_plus/user/controllers/user_controller_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';


part 'controller/main_bindings.dart';
part 'controller/main_controller.dart';
part 'controller/main_state.dart';
part 'presentation/main_view.dart';
part 'presentation/mobile/main_mobile.dart';
part 'presentation/tablet/main_tablet.dart';
