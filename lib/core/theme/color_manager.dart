import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';


class ColorManager {

  static const Color scaffold = Color(0xFFF6F7FB);
  static const Color backgroundColor = Color(0xFFF6F7FB);
  static const Color primary = Color(0xFF334760);//192551
  // old color -> 0xFF252B5C
  static  Color primaryLight = primary.withOpacity(0.4);

  static const Color blue = Color(0xFF2464CC);
  static const Color blueGray = Color(0xff454F63);

  static const Color darkGrey = Color(0xFF646C8C);
  static const Color grey = Color(0xff7A849C);
  static const Color lightGrey = Color(0xFF9494AC);


  static const Color disableBottom = Color(0xffE0E0E0);
  static const Color shadowGray = Color(0xffD2D5DA);
  static const Color primaryShadow = Color.fromRGBO(42, 140, 255, 0.1);

  //"path": "01/013777/013777a_hb_l_006.jpg",
  static const Color buttonColor = Color(0xffffc846);
  static const Color orange = Colors.orange;
  static const Color yellow = Color(0xFFFCD45D);
  static const Color green = Color(0xff3EB558);
  static const Color red = Color(0xffFF1D00);
  static const Color white = Color(0xffFFFFFF);
  static const Color black = Color(0xff111111);
  static const Color transparent = Colors.transparent;


  static const List<Color> linearColors = [
    // Color(0xFFFCD45D), // اللون الأول
    Color(0xffFF1D00), // اللون الثاني
    Color(0xffFF1D00), // اللون الثاني
  ];


  static const BoxShadow primaryBoxShadow = BoxShadow(
    color: primaryShadow,
    spreadRadius: 0,
    blurRadius: 10,
    offset: Offset(8, 8), // changes position of shadow
  );

  static const MaterialColor primarySwatch = MaterialColor(
    0xFF252B5C,
    <int, Color>{
      50: Color(0xFFE5E6EB),
      100: Color(0xFFBEBFCE),
      200: Color(0xFF9295AE),
      300: Color(0xFF666B8D),
      400: Color(0xFF464B74),
      500: Color(0xFF252B5C),
      600: Color(0xFF212654),
      700: Color(0xFF1B204A),
      800: Color(0xFF161A41),
      900: Color(0xFF0D1030),
    },
  );

  static var selectedColor = Colors.grey.shade300;

  static SystemUiOverlayStyle get statusBarPrimary {
    if(Platform.isIOS){
      return const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: ColorManager.scaffold,
        statusBarColor: ColorManager.primary,
      );
    }
   return SystemUiOverlayStyle.light.copyWith(
     statusBarIconBrightness: Brightness.light,
     statusBarBrightness: Brightness.light,
     systemNavigationBarIconBrightness: Brightness.dark,
     systemNavigationBarColor: ColorManager.scaffold,
     statusBarColor: ColorManager.primary,
   );
  }

  static SystemUiOverlayStyle get statusBarPrimaryFull{
    if(Platform.isIOS){
      return const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarColor: ColorManager.primary,
        statusBarColor: ColorManager.primary,
      );
    }
    return const SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.light,
      systemNavigationBarIconBrightness: Brightness.light,
      systemNavigationBarColor: ColorManager.primary,
      statusBarColor: ColorManager.primary,
    );
  }

  static SystemUiOverlayStyle get statusNormalPage{
    if(Platform.isIOS){
      return const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.light,
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarColor: ColorManager.primary,
        statusBarColor: ColorManager.primary,
      );
    }
    return const SystemUiOverlayStyle(
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
      systemNavigationBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: ColorManager.scaffold,
      statusBarColor: ColorManager.scaffold,
    );
  }

}
