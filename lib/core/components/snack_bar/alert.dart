import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:fandooq/core/router/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

success(String message, {String? title, required BuildContext context}) {
  AwesomeDialog(
    context: context,
    animType: AnimType.scale,
    dialogType: DialogType.success,
    title: title ?? 'alerts.success'.tr,
    headerAnimationLoop: false,
    desc: message,
    btnOkOnPress: () {},
    padding: const EdgeInsets.only(right: 10, left: 10),
  ).show();
}

//  Future<bool> promptExit({String? errorMessage}) async {
// late bool canExit;
// AwesomeDialog dlg = AwesomeDialog(
//     context: AppPages.context!,
//     // padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
//     // isDense: true,
//     // width: double.infinity,
//     dialogType: DialogType.noHeader,
//     animType: AnimType.bottomSlide,
//     title: "Confirmation".tr,
//     desc: errorMessage?.tr??"Are you sure you want to exit ?".tr,
//     dismissOnTouchOutside: true,
//     headerAnimationLoop: false,
//     btnCancelOnPress: () => canExit = false,
//     btnOkOnPress: ()=>exit(0),
//     btnOkColor: ColorManager.red,
//     btnCancelColor: ColorManager.primary,
//     barrierColor: ColorManager.textBlack.withOpacity(0.7),
//     dialogBackgroundColor: Get.find<ThemeManager>().darkColor,
//     titleTextStyle: Theme.of(AppPages.context!).textTheme.headline5,
//     descTextStyle: Theme.of(AppPages.context!).textTheme.headline6,
//     btnCancelText: 'Cancel'.tr,
//     btnOkText: 'Ok'.tr
// );
// await dlg.show();
// return Future.value(canExit);
// }
info(String? message,
    {String title = "Success",
    String? btnOkText,
    Function()? btnOkOnPress,
    bool dismissOnTouchOutside = true}) {
  AwesomeDialog(
    context: AppPages.context!,
    animType: AnimType.scale,
    dialogType: DialogType.noHeader,
    title: 'Info'.tr,
    headerAnimationLoop: false,
    desc: message,
    dismissOnTouchOutside: dismissOnTouchOutside,
    padding: const EdgeInsets.only(right: 10, left: 10, top: 10, bottom: 10),
    btnOkOnPress: btnOkOnPress,
    btnOkText: btnOkText,
    btnOkColor: Colors.grey,
  ).show();
}

error(String message, {String? title, required BuildContext context}) {
  AwesomeDialog(
    context: context,
    alignment: Alignment.center,
    animType: AnimType.bottomSlide,
    dialogType: DialogType.error,
    title: title ?? 'alerts.error'.tr,
    headerAnimationLoop: false,
    desc: message,
    //btnOkOnPress: () {},
    // btnOkColor: ColorManager.primary,
    padding: const EdgeInsets.only(right: 10, left: 10),
    btnCancelOnPress: () {},
    btnCancelText: "alerts.back".tr,
  ).show();
}
