import 'address.dart';
import 'geo_point.dart';

class LocationInfo {
  String? placeId;
  AddressPlace? address;
  GeoPoint? point;
  bool? isArea;
  bool? isHotel;
  DateTime? lastModification;

  LocationInfo({
    this.placeId,
    this.address,
    this.point,
    this.isArea,
    this.isHotel,
    this.lastModification,
  });

  factory LocationInfo.fromJson(Map<String, dynamic> json) {
    return LocationInfo(
      placeId: json['placeId'],
      address: json['address'] != null
          ? AddressPlace.fromJson(json['address'])
          : null,
      point: json['point'] != null ? GeoPoint.fromJson(json['point']) : null,
      isArea: json['isArea'],
      isHotel: json['isHotel'],
      lastModification: json['lastModification'] != null
          ? DateTime.parse(json['lastModification'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'placeId': placeId,
      'address': address?.toJson(),
      'point': point?.toJson(),
      'isArea': isArea,
      'isHotel': isHotel,
      'lastModification': lastModification?.toIso8601String(),
    };
  }

  LocationInfo copyWith({
    String? placeId,
    AddressPlace? address,
    GeoPoint? point,
    bool? isArea,
    bool? isHotel,
    DateTime? lastModification,
    double? latitude,
    double? longitude,
  }) {
    return LocationInfo(
      placeId: placeId ?? this.placeId,
      address: address ?? this.address,
      point: point ?? this.point,
      isArea: isArea ?? this.isArea,
      isHotel: isHotel ?? this.isHotel,
      lastModification: lastModification ?? this.lastModification,
    );
  }

  @override
  String toString() {
    return 'LocationInfo(placeId: $placeId, address: $address, point: $point, isArea: $isArea, isHotel: $isHotel)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationInfo &&
        other.placeId == placeId &&
        other.address == address &&
        other.point == point &&
        other.isArea == isArea &&
        other.isHotel == isHotel;
  }

  @override
  int get hashCode {
    return placeId.hashCode ^
        address.hashCode ^
        point.hashCode ^
        isArea.hashCode ^
        isHotel.hashCode;
  }

  /// Get display name for the location
  String get displayName {
    if (address?.name != null) {
      return address!.name!;
    }
    return placeId ?? 'Unknown Location';
  }

  /// Get formatted address string
  String get formattedAddress {
    if (address == null) return '';

    List<String> parts = [];
    if (address!.name != null) parts.add(address!.name!);
    if (address!.city != null && address!.city != address!.name) {
      parts.add(address!.city!);
    }
    if (address!.state != null) parts.add(address!.state!);
    if (address!.country != null) parts.add(address!.country!);

    return parts.join(', ');
  }

  /// Get coordinates from point or direct latitude/longitude
  double? get lat => point?.latitude;
  double? get lng => point?.longitude;
}
