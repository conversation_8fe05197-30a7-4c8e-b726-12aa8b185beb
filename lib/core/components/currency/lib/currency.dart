


import 'dart:async';

import 'package:fandooq/core/cache/preference_manager.dart';
import 'package:fandooq/core/components/currency/lib/models/currency.dart';
import 'package:fandooq/core/components/custom_scroll_bar.dart';
import 'package:fandooq/core/components/search_text_box.dart';
import 'package:fandooq/core/controllers/controllers.dart';
import 'package:fandooq/core/data_base/hive_helpers/tables.dart';
import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/core/lang/app_strings.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import 'package:fandooq/widgets/app_bar/default_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../focus_menu/lib/focused_menu.dart';

part 'controller/currency_controller.dart';
part 'view/currency_view.dart';