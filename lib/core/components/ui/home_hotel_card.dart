
import 'package:fandooq/core/extensions/assets_extension.dart';
import 'package:fandooq/core/const/assets.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:fandooq/feature_plus/hotels/models/hotels_data_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/color_manager.dart';
import '../custom_cache_image.dart';
import 'app_text.dart';
import 'custom_rating.dart';



class HotelHomeCard extends StatelessWidget {

  const HotelHomeCard({Key? key, required this.hotel,this.stay}) : super(key: key);
  final HotelData hotel;
  final HotelRequest? stay;

  @override
  Widget build(BuildContext context) {

    return Container(
      constraints: const BoxConstraints(
        maxHeight: 175
      ),
      child: GestureDetector(
        onTap: () {

        },
        child: SizedBox(
          child: Row(
            children: [

              Expanded(
                flex: 2,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Container(
                    height: 180,
                    width: 200,
                    constraints: const BoxConstraints(
                        maxWidth: 200
                    ),
                    color: Colors.grey[200],
                    child: CustomCacheImage(
                      url: hotel.images!.first ?? "",
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),

              Expanded(
                  flex: 3,
                  child: Container(
                    height: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 5),
                    margin: const EdgeInsets.symmetric( vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(20.0),
                        bottomRight: Radius.circular(20.0),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [

                        AppText.large(
                          hotel.name ?? "",
                          fontSize: 16,
                          textAlign: TextAlign.left,
                          maxLine: 2,
                          textOverflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 4),

                        Row(
                          children: [
                            Assets.icon_location_svg.svg(
                              color: ColorManager.darkGrey,
                              height: 15,
                            ),
                            const SizedBox(width: 8),
                            Flexible(
                                child: AppText.small(
                                  hotel.address ?? "",
                                  fontSize: 10,
                                )),
                          ],
                        ),

                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          child: CustomRating(rating: 4.5),
                        ),

                        const SizedBox(height: 10),
                        RichText(
                          text: TextSpan(
                            children: [
                              // AppTextSpan.large(hotel.getMinRate().toString().toMoney,
                              //     fontSize: 15, fontWeight: FontWeight.w800),
                              AppTextSpan.medium(' /night',
                                  color: Colors.blueGrey),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )),

            ],
          ),
        ),
      ),
    );
  }
}
