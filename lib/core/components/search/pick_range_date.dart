


import 'dart:developer';

import 'package:fandooq/core/components/focus_menu/lib/focused_menu.dart';
import 'package:fandooq/core/components/ui/app_text.dart';
import 'package:fandooq/core/extensions/date.dart';
import 'package:fandooq/core/lang/app_strings.dart';
import 'package:fandooq/core/models/availability/request/stay_request.dart';
import 'package:fandooq/core/responsive/responsive_ui.dart';
import 'package:fandooq/core/responsive/screen_size.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import 'package:fandooq/feature/home/<USER>';
import 'package:fandooq/core/extensions/assets_extension.dart';
import 'package:fandooq/core/const/assets.dart';
import 'package:fandooq/core/extensions/icons.dart';
import 'package:fandooq/feature_plus/home/<USER>/guests_search.dart';
import 'package:fandooq/feature_plus/hotels/views/widgets/enhanced_date_range_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_date_range_picker/flutter_date_range_picker.dart';
import 'package:flutter_sizer/flutter_sizer.dart';
import 'package:get/get.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../date_rang_picker.dart';
import '../range_date.dart';
import 'package:flutter_date_range_picker/flutter_date_range_picker.dart' as datePack;

//? **************** Change Range Date ************************//

Future<StayRequest?> selectRangeDate(
    BuildContext context,
    DateTime start,
    DateTime end,
    ) async {

  final DateTimeRange? picked = await showCustomDateRangePicker(
    context: context,
    initialDateRange: DateTimeRange(
      start: start,
      end: end,
    ),
    firstDate: DateTime.now(),
    lastDate: DateTime.now().add( const Duration(days: 365) ),
    builder: (context, child) {
      return Column(
        children: [
          ResponsiveWidthOption2(
            child: child!,
          )
        ],
      );
    },
  );

  if (picked != null) {
    final stay = StayRequest(
      checkIn: picked.start,
      checkOut: picked.end,
    );
    return stay;
  }

  return null;
}



DateRange selectedDateRange = DateRange(DateTime.now(), DateTime.now().add( const Duration(days: 5) ));


class PickRangeDate extends StatelessWidget {
  final Function(StayRequest) onChanged;
  final StayRequest value;
  final bool oneIcon;
  final bool showTitle;
  const PickRangeDate({super.key, required this.onChanged, required this.value, this.oneIcon = false, this.showTitle = true});

  @override
  Widget build(BuildContext context) {

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade200),
        color: Colors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(8),
        ),
      ),
      child: InkWell(
        onTap: () async {
          EnhancedDateRangePicker.show(
            context: Get.context!,
            initialCheckIn: value.checkIn,
            initialCheckOut: value.checkOut,
            title: 'اختر تواريخ الإقامة',
            onDateRangeSelected: (checkIn, checkOut) {
              onChanged(StayRequest(
                checkIn: checkIn,
                checkOut: checkOut,
              ));
            },
          );

        },
        child: Row(
          children: [

            Expanded(
              child: CustomTitleGuests(
                  onTap: null,
                  icon: HugeIcon(icon: HugeIcons.strokeRoundedDateTime, color: Colors.black87),
                  title: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if(showTitle)...{
                        Opacity(
                          opacity: 0.5,
                          child: AppText.small(AppStrings.from.tr),
                        ),
                        const SizedBox(height: 5),
                      },
                      Text(value.checkIn!.day_month_year_option1,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: Theme.of(context).textTheme.titleSmall!.copyWith(
                            fontSize: 14
                        ),)
                    ],
                  )
              ),
            ),

            if(!oneIcon) const VerticalDivider(
              endIndent: 10,
              indent: 10,
            ),

            Expanded(
              child: CustomTitleGuests(
                  onTap: null,
                  icon: oneIcon ? null : Assets.icon_calendar_svg.svg(color: ColorManager.grey,width: 25),
                  title: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if(showTitle)...{
                        Opacity(
                          opacity: 0.5,
                          child: AppText.small(AppStrings.to.tr),
                        ),
                        const SizedBox(height: 5),
                      },
                      Text(value.checkOut!.day_month_year_option1,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: Theme.of(context).textTheme.titleSmall!.copyWith(
                            fontSize: 14
                        ),)
                    ],
                  )
              ),
            ),

          ],
        ),
      ),
    );

  }


}
