import 'package:fandooq/core/components/ui/app_text.dart';
import 'package:fandooq/core/lang/app_strings.dart';
import 'package:fandooq/feature/home/<USER>';
import 'package:fandooq/feature_plus/home/<USER>/guests_search.dart';
import 'package:fandooq/feature_plus/home/<USER>/change_guests_info_screen.dart';
import 'package:fandooq/feature_plus/hotels/models/hotels_data_search.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hugeicons/hugeicons.dart';


class PickGuestsResult extends StatelessWidget {
  final List<RoomRequestModel> rooms;
  final Function(List<RoomRequestModel>) onChanged;
  final bool enableTap;
  final bool showTitle;
  const PickGuestsResult({super.key, required this.rooms, required this.onChanged, this.enableTap = true, this.showTitle = true});
  @override
  Widget build(BuildContext context) {

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade200),
        color: Colors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(8),
        ),
      ),
      child: CustomTitleGuests(
        onTap: () async {

          final result = await showModalBottomSheet(
            context: Get.context!,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (context) => DraggableScrollableSheet(
              initialChildSize: rooms.length > 1 ? 0.9 : 0.7,
              minChildSize: 0.5,
              maxChildSize: 0.9,
              builder: (context, scrollController) => Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: ChangeGuestsInfoScreen(
                  rooms: rooms,
                  scrollController: scrollController,
                ),
              ),
            ),
          );

          if (result != null) {
            onChanged((result as List<RoomRequestModel>));
          }

        },
        icon: HugeIcon(icon: HugeIcons.strokeRoundedUserGroup, color: Colors.black87),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if(showTitle)...{
              Opacity(
                opacity: 0.5,
                child: AppText.small(AppStrings.guestsInformation.tr),
              ),
              const SizedBox(height: 5),
            },
            if(rooms.isNotEmpty) Flexible(
              child: Text(
                rooms.info,
                maxLines: 2,
                overflow: TextOverflow.fade,
                style: Theme.of(context)
                    .textTheme
                    .titleSmall!
                    .copyWith(fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
