import 'package:fandooq/core/components/ui/app_text.dart';
import 'package:fandooq/core/extensions/date.dart';

import 'package:fandooq/core/extensions/assets_extension.dart';
import 'package:fandooq/core/const/assets.dart';
import 'package:fandooq/core/extensions/icons.dart';
import 'package:flutter/material.dart';

import 'custom_divider_1.dart';

class CheckInCheckOut {
  static Widget option1({
    required DateTime checkIn,
    required DateTime checkOut,
    String? userName,
  }) {
    int totalNight() {
      return checkOut
          .copyWith(hour: 0)
          .difference(checkIn.copyWith(hour: 0))
          .inDays;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          border: Border.all(width: 0.1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            children: [
              Assets.icon_bed_svg.svg(width: 15),
              const SizedBox(
                width: 10,
              ),
              AppText.small("${checkOut.difference(checkIn).inDays} days"),
            ],
          ),
          const Divider(
            thickness: 0.2,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircleIcon(),
                      const SizedBox(
                        height: 5,
                      ),
                      Container(
                        width: 1,
                        height: 25,
                        child: CustomPaint(
                          painter: VerticalDashedLinePainter(
                              dashHeight: 10, dashSpace: 5, color: Colors.grey),
                        ),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      const CircleIcon(),
                    ],
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          AppText.small(
                            "Check-In   : ",
                            textAlign: TextAlign.left,
                            maxLine: 1,
                            color: Colors.grey,
                            textOverflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          AppText.small(
                            checkIn.formate1,
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          AppText.small(
                            "Check-out : ",
                            textAlign: TextAlign.left,
                            maxLine: 1,
                            color: Colors.grey,
                            textOverflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          AppText.small(
                            checkOut.formate1,
                          ),
                        ],
                      )
                    ],
                  ),
                ],
              ),
            ],
          ),
          const Divider(
            thickness: 0.2,
          ),
          Row(
            children: [
              Assets.icon_users_svg.svg(
                width: 15,
              ),
              const SizedBox(
                width: 10,
              ),
              AppText.small(userName ?? ""),
            ],
          ),
        ],
      ),
    );
  }

  static Widget option2({
    required DateTime checkIn,
    required DateTime checkOut,
    String? info,
  }) {
    int totalNights() {
      // Ensure the check-in and check-out times are set to midnight
      DateTime adjustedCheckIn =
          DateTime(checkIn.year, checkIn.month, checkIn.day);
      DateTime adjustedCheckOut =
          DateTime(checkOut.year, checkOut.month, checkOut.day);
      // Calculate the difference in days
      int nights = adjustedCheckOut.difference(adjustedCheckIn).inDays;
      return nights;
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
          border: Border.all(
            color: Colors.black12,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(8))),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      // margin: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          AppText.mediumPlus(
            "Your booking details",
            color: Colors.black,
          ),
          const SizedBox(
            height: 20,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: 9,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const CircleIcon(),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: SizedBox(
                        width: double.infinity,
                        child: CustomPaint(
                          painter: DashedLinePainter(
                              dashWidth: 10, dashSpace: 20, color: Colors.grey),
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    const CircleIcon(),
                  ],
                ),
              ),
              const Expanded(flex: 2, child: SizedBox()),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          IntrinsicHeight(
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        AppText.small(
                          "Check-In",
                          textAlign: TextAlign.left,
                          maxLine: 1,
                          textOverflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        AppText.medium(
                          checkIn.formate4,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        AppText.small(
                          "From ${checkIn.hourMinute}",
                          textAlign: TextAlign.left,
                          maxLine: 1,
                          textOverflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    AppText.small("Total length of stay:"),
                    const SizedBox(
                      height: 10,
                    ),
                    AppText.medium([totalNights(), "nights"].join(" ")),
                  ],
                ),
                const Expanded(
                    child: SizedBox(
                  width: 20,
                )),
                const VerticalDivider(),
                const Expanded(
                    child: SizedBox(
                  width: 20,
                )),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        AppText.small(
                          "Check-Out",
                          textAlign: TextAlign.left,
                          maxLine: 1,
                          textOverflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        AppText.medium(
                          checkOut.formate4,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        AppText.small(
                          "Until ${checkOut.hourMinute}",
                          textAlign: TextAlign.left,
                          maxLine: 1,
                          textOverflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (info != null) ...{
            const Divider(
              height: 40,
            ),
            AppText.small("You selected"),
            const SizedBox(
              height: 10,
            ),
            AppText.medium(info),
          },
          const SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }
}
