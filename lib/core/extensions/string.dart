import 'package:fandooq/core/cache/preference_manager.dart';
import 'package:fandooq/core/resources/font_manager.dart';
import 'package:fandooq/feature_plus/settings/controllers/settings_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

// Note: trParams is now provided by GetX natively, so we don't need a custom extension

extension Super on String? {
  //Todo :: toCamelCase() as mazen => Mazen
  String? toCamelCase() {
    if (this == null || this!.isEmpty) return null;
    return this![0].toUpperCase() + this!.substring(1);
  }
}

enum CurrencyResultType {
  STRING,
  NUMBER,
}

/// Currency symbols mapping
class CurrencySymbols {
  static const Map<String, String> symbols = {
    'AED': 'AED',
    'AUD': r'A$',
    'BHD': 'BHD',
    'CAD': r'C$',
    'CHF': 'CHF',
    'CNY': '¥',
    'EGP': 'EGP',
    'EUR': '€',
    'GBP': '£',
    'INR': '₹',
    'IRR': 'IRR',
    'JOD': 'JOD',
    'JPY': '¥',
    'KWD': 'KWD',
    'LBP': 'LBP',
    'MAD': 'MAD',
    'OMR': 'OMR',
    'PKR': 'PKR',
    'QAR': 'QAR',
    'SAR': 'SAR',
    'TRY': 'TRY',
    'USD': '\$',
  };

  /// Get currency symbol for a given currency code
  static String getSymbol(String currencyCode) {
    return symbols[currencyCode.toUpperCase()] ?? currencyCode;
  }
}

extension Super2 on String {
  String toMoneyAppCurrency({int decimalDigits = 0}) {
    var currency = PreferenceManager.instance.getCurrency();
    var format = NumberFormat.currency(
            locale: null, decimalDigits: decimalDigits, symbol: "")
        .format(num.parse(this))
        .toString();
    return [
      format,
      currency.currency,
    ].join(" ");
  }

  dynamic toMoney(
      {bool enableSymbol = true,
      bool enableSymbolWord = true,
      bool enableAmount = true,
      int decimalDigits = 2,
      CurrencyResultType resultType = CurrencyResultType.STRING,
      String? hotelCurrency}) {
    if (num.parse(this) == 0) {
      if (resultType == CurrencyResultType.NUMBER) {
        return 0.0;
      }
      return "0.0";
    }

    var currency =
        hotelCurrency ?? PreferenceManager.instance.getCurrency().currency;

    // var symbol = Currency.sign[currency.currency]!;

    // تحويل قيمة العملة باستخدام معدل التحويل
    var amount = num.parse(this);

    if (amount > 1000) {
      decimalDigits = 0;
    }

    var format = NumberFormat.currency(
            locale: null, decimalDigits: decimalDigits, symbol: "")
        .format(amount)
        .toString();

    if (resultType == CurrencyResultType.NUMBER) {
      return amount;
    }

    return [
      if (enableAmount) format,
      if (enableSymbol) currency,
    ].join(" ");
  }

  /// Convert price to formatted string with currency symbol
  String toMoneyWithSymbol({
    int decimalDigits = 2,
    String? currencyCode,
  }) {
    double amount = double.tryParse(this) ?? 0;

    if (amount == 0) {
      return "0.0";
    }

    String currency = currencyCode ?? _getCurrentCurrency();

    if (amount > 1000) {
      decimalDigits = 0;
    }

    final formatter = NumberFormat.currency(
      locale: 'en_US',
      decimalDigits: decimalDigits,
      symbol: "",
    );

    // ✅ نأخذ النتيجة من الفورماتر ونرجع نقربها
    String formatted = formatter.format(amount);
    double rounded = double.parse(formatted.replaceAll(",", ""));

    // ✅ إعادة التنسيق بدقة عشرية ثابتة
    String finalFormatted = rounded.toStringAsFixed(decimalDigits);

    var symbol = CurrencySymbols.getSymbol(currency);
    return '$symbol $finalFormatted';
  }

  /// Get current currency from settings controller or fallback to preference manager
  String _getCurrentCurrency() {
    try {
      // Try to get currency from SettingsController first
      if (Get.isRegistered<SettingsController>()) {
        final settingsController = Get.find<SettingsController>();
        return settingsController.appSettings.currency;
      }
    } catch (e) {
      // Fallback to PreferenceManager if SettingsController is not available
      try {
        return PreferenceManager.instance.getCurrency().currency;
      } catch (e2) {
        // Ultimate fallback
        return 'USD';
      }
    }
    return 'USD';
  }

  // String toMoney({bool showSymbol = true, String? customSymbol}) {

  // String toMoney({bool showSymbol = true, String? customSymbol}) {
  //
  //   if (num.parse(this) == 0) {
  //     return "0.0";
  //   }
  //
  //   var amount = num.parse(getCurrencySymbol());
  //
  //   var symbol = showSymbol
  //       ? (customSymbol ?? Currency.sign[currency.currency])
  //       : ""; // استخدام الرمز الافتراضي أو رمز مخصص إذا تم تحديده.
  //
  //   return [
  //     symbol,
  //     " ",
  //     NumberFormat.currency(locale: null, decimalDigits: 0, symbol: "").format(amount).toString(),
  //   ].join("");
  // }
  //
  //
  // String toMoneyDetails({bool showSymbol = true, String? customSymbol}) {
  //   if (num.parse(this) == 0) {
  //     return "0.0";
  //   }
  //
  //   var currency = PreferenceManager.instance.getCurrency();
  //
  //   var exchange = Get.find<HotelsRepo>().getExchangeRate("EUR");
  //
  //   var amount = exchange.convertCurrency(num.parse(this), currency.currency);
  //
  //   var symbol = showSymbol
  //       ? (customSymbol ?? Currency.sign[currency.currency])
  //       : ""; // استخدام الرمز الافتراضي أو رمز مخصص إذا تم تحديده.
  //
  //   return [
  //     symbol,
  //     " ",
  //     NumberFormat.currency(locale: null, decimalDigits: 2, symbol: "").format(amount).toString(),
  //   ].join("");
  // }

  // String get toMoney{
  //
  //   if(num.parse(this) == 0){
  //     return "0.0";
  //   }
  //
  //   var currency = PreferenceManager.instance.getCurrency();
  //
  //   var exchange = Get.find<HotelsRepo>().getExchangeRate("EUR");
  //
  //   var amount = exchange.convertCurrency(num.parse(this), currency.currency);
  //
  //   return [
  //     Currency.sign[currency.currency],
  //     " ",
  //     NumberFormat.currency(locale: null,decimalDigits: 0,symbol:"").format(amount).toString(),
  //   ].join("");
  //
  // }
  //
  // String get toMoneyDetails{
  //
  //   if(num.parse(this) == 0){
  //     return "0.0";
  //   }
  //
  //   var currency = PreferenceManager.instance.getCurrency();
  //
  //   var exchange = Get.find<HotelsRepo>().getExchangeRate("EUR");
  //
  //   var amount = exchange.convertCurrency(num.parse(this), currency.currency);
  //
  //   return [
  //     Currency.sign[currency.currency],
  //     " ",
  //     NumberFormat.currency(locale: null,decimalDigits: 2,symbol:"").format(amount).toString(),
  //   ].join("");
  //
  // }

  void copy() {
    // Clipboard.setData(ClipboardData(text: this));
    // // showToast("Copied",position: ToastPosition.bottom);
    // SnackBar(content: Text('Copied to clipboard'));

    Clipboard.setData(ClipboardData(text: toString()));
    ScaffoldMessenger.of(Get.context!).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  void callNumber() async {
    final Uri url = Uri(scheme: 'tel', path: this);
    await launchUrl(url);
  }

  void sendEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: this,
    );
    await launchUrl(emailUri);
  }

  String get removeDuplicateWordsPreserveOrder {
    // This pattern matches Arabic words and commas.
    // The Arabic words are matched based on the range of Arabic Unicode characters.
    RegExp pattern = RegExp(r'([\u0600-\u06FF]+|\,)');
    Set<String> wordSet = {};
    List<String> uniqueWords = [];

    // Find all matches according to the pattern.
    Iterable<RegExpMatch> matches = pattern.allMatches(this);

    for (final match in matches) {
      String word = match.group(0)!;
      // Check if the word is a comma or a new unique word.
      if (word == ',' || !wordSet.contains(word)) {
        uniqueWords.add(word);
        if (word != ',') {
          wordSet.add(word);
        }
      }
    }

    // Rebuild the string. This ensures not to insert spaces before commas.
    StringBuffer buffer = StringBuffer();
    for (var i = 0; i < uniqueWords.length; i++) {
      if (uniqueWords[i] != ',' && i != 0 && uniqueWords[i - 1] != ',') {
        buffer.write(' ');
      }
      buffer.write(uniqueWords[i]);
    }

    return buffer.toString();
  }

  bool get isArabic {
    final arabicRegex = RegExp(r'[\u0600-\u06FF]');
    return arabicRegex.hasMatch(this);
  }

  bool get isEnglish {
    final englishRegex = RegExp(r'[A-Za-z]');
    return englishRegex.hasMatch(this);
  }

  String get font {
    return isArabic ? FontConstants.tajawal : FontConstants.poppins;
  }

  String get tag1 => "${this}tag1";
  String get tag2 => "${this}tag2";
  String get tag3 => "${this}tag3";
}

extension SuperList on List {
  // TODO: toCamelCase()
}

extension StringNull on String? {
  double toDouble() {
    if (this == null) {
      return 0.0;
    }
    return double.parse(this!);
  }
}

extension TextEx on Text {
  Text get font {
    var font = (data ?? "").font;
    return Text(
      data ?? '',
      style: style?.copyWith(fontFamily: font) ?? TextStyle(fontFamily: font),
      key: key,
      textAlign: textAlign,
      textDirection: textDirection,
      locale: locale,
      softWrap: softWrap,
      overflow: overflow,
      textScaleFactor: textScaleFactor,
      maxLines: maxLines,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
    );
  }

  // Widget get adaptive {
  //   var _font = (data ?? "").font;
  //   return
  //     Html(
  //       data: """
  //   <div>
  //     <h1>عنوان بصيغة </h1>
  //   </div>
  // """,
  //       // يمكنك أيضًا تحديد الأنماط بشكل مباشر
  //       style: {
  //       "h1": Style(
  //       fontSize: FontSize.large,
  //       fontWeight: FontWeight.bold,
  //       ),
  //       "p": Style(fontSize: FontSize.medium),
  //       },
  //     );
  // }
  //
}

extension StringEx on String {
  num get beNum {
    if (toString().isEmpty) {
      return 0;
    }
    return num.parse(this);
  }
}

extension StringE on dynamic {
  String join(String text) {
    return this + text;
  }

  num get beNum {
    if (this == null) {
      return 0;
    }

    if (this is String) {
      if (toString().isEmpty) {
        return 0;
      }

      return num.parse(this);
    }

    return this;
  }
}

extension SSS on TextStyle {
  TextStyle get withZoomFix => copyWith(wordSpacing: 0);
}

/// Extension for double to format with currency symbols
extension DoubleMoneyExtension on double {
  /// Convert double to formatted string with currency symbol
  String toMoneyWithSymbol({
    int decimalDigits = 2,
    String? currencyCode,
  }) {
    if (this == 0) {
      return "0.0";
    }

    String currency = currencyCode ?? _getCurrentCurrencyForDouble();
    var amount = this;

    if (amount > 1000) {
      decimalDigits = 0;
    }

    var format = NumberFormat.currency(
      locale: null,
      decimalDigits: decimalDigits,
      symbol: "",
    ).format(amount).toString();

    var symbol = CurrencySymbols.getSymbol(currency);

    return '$symbol $format';
  }

  /// Get current currency from settings controller or fallback to preference manager
  String _getCurrentCurrencyForDouble() {
    try {
      // Try to get currency from SettingsController first
      if (Get.isRegistered<SettingsController>()) {
        final settingsController = Get.find<SettingsController>();
        return settingsController.appSettings.currency;
      }
    } catch (e) {
      // Fallback to PreferenceManager if SettingsController is not available
      try {
        return PreferenceManager.instance.getCurrency().currency;
      } catch (e2) {
        // Ultimate fallback
        return 'USD';
      }
    }
    return 'USD';
  }

  /// Convert double to formatted string with currency code
  String toMoneyWithCode({
    int decimalDigits = 2,
    String? currencyCode,
  }) {
    if (this == 0) {
      return "0.0";
    }

    String currency = currencyCode ?? _getCurrentCurrencyForDouble();
    var amount = this;

    if (amount > 1000) {
      decimalDigits = 0;
    }

    var format = NumberFormat.currency(
      locale: null,
      decimalDigits: decimalDigits,
      symbol: "",
    ).format(amount).toString();

    return '$format $currency';
  }
}
