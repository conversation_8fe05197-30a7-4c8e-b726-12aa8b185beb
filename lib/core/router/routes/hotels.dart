part of '../app_pages.dart';

// Hotels Plus Routes
final List<GetPage> hotelsRoutes = [
  // Hotels List Screen
  GetPage(
    name: Routes.HOTELS_PLUS,
    page: () => const HotelsScreenPlus(),
    binding: HotelsBindingPlus(),
    transition: Transition.cupertino,
  ),

  // Hotel Details Screen
  GetPage(
    name: Routes.HOTEL_DETAILS_PLUS,
    page: () => const HotelDetailsScreenPlus(),
    binding: HotelDetailsBindingPlus(),
    transition: Transition.cupertino,
  ),

  // Pre-booking Screen
  GetPage(
    name: Routes.PRE_BOOKING_PLUS,
    page: () => const PreBookingScreenPlus(),
    binding: PreBookingBindingPlus(),
    transition: Transition.cupertino,
  ),

  // Guest Details Screen
  GetPage(
    name: Routes.GUEST_DETAILS_PLUS,
    page: () => const GuestDetailsScreenPlus(),
    binding: GuestDetailsBindingPlus(),
    transition: Transition.cupertino,
  ),

  // Payment Screen
  GetPage(
    name: Routes.PAYMENT_PLUS,
    page: () => const PaymentScreenPlus(),
    binding: PaymentBindingPlus(),
    transition: Transition.cupertino,
  ),



];
