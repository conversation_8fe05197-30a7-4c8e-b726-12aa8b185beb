part of 'app_pages.dart';

abstract class Routes {
  static const LOGIN = '/login';
  static const OTP = '$LOGIN/otp';

  // Feature Plus Authentication
  static const SPLASH_SCREEN = '/splash';

  //? Home
  // static const CHANGE_GUESTS_INFO = '/change_guests_info';
  // static const CHANGE_LOCATION = '/change_location';

  static const HOTELS = '/hotels';
  static const HOTEL = '$HOTELS/hotel';
  static const CHECK_RATE = '$HOTEL/checkRate';
  static const BOOKING = '$CHECK_RATE/booking';

  static const MAINTAIN_PAGE = '/maintain';

  static const PAYMENT_CONFIRMED = '$BOOKING/confirmed';

  static const PAYMENT_SCREEN = '$BOOKING/payment';

  static const USER_BOOKINGS = '/bookings';
  static const BOOKDetails = '/book_details';

  // static const GUESTS_DETAILS = "/guests_details";

  static const PRIVACY = '/privcy';
  static const TERMS = '/terms';
  static const COMPANY_INFO = '/company_info';

  //? Profile
  static const EDIT_PROFILE = '/edit_profile';
  static const EMAIL_ADDRESS = '/email_address';
  static const MOBILE_NUMBER = '/mobile_number';

  static const PAYMENT_METHODS = '/payment-methods';

  // Feature Plus Routes
  static const FAVORITES = '/favorites';
  static const CONTACT_US = '/contact-us';

  // Hotels Plus Routes
  static const HOTELS_PLUS = '/hotels-plus';
  static const HOTEL_DETAILS_PLUS = '/hotel-details-plus';
  static const PRE_BOOKING_PLUS = '/pre-booking-plus';
  static const GUEST_DETAILS_PLUS = '/guest-details-plus';
  static const PAYMENT_PLUS = '/payment-plus';
  static const BOOKING_CONFIRMATION_PLUS = '/booking-confirmation-plus';
  static const BOOKING_SUCCESS_PLUS = '/booking-success-plus';

  // Bookings Plus Routes
  static const MY_BOOKINGS_PLUS = '/my-bookings-plus';
  static const BOOKING_DETAILS_PLUS = '/booking-details-plus';


  static const SETTINGS = '/settings';
  static const DELETE_ACCOUNT = '/delete-account';
  static const VERIFY_OTP = '/verify-otp';

  static void pop(BuildContext context) {
    // if(kIsWeb){
    //   window.history.back();
    // }
    Navigator.pop(context);
    log(currentPage.toString());
  }

  static String get currentPage => Get.currentRoute;

  static void popUntilHotel() {
    var routes = Get.currentRoute.split("/").reversed;
    for (var route in routes) {
      if (route == "hotel") {
        return;
      }
      Get.back();
    }
  }

  static Future<void> popUntilMain() async {
    var routes = Get.currentRoute.split("/").reversed;
    for (var route in routes) {
      if (route.isEmpty) {
        return;
      }
      Get.back();
    }
    return;
  }

  static route({
    required String page,
    dynamic arguments,
  }) {
    return Get.toNamed(
      page,
      arguments: arguments,
    );
  }

  static offAllNamed({
    required String page,
    dynamic arguments,
  }) {
    return Get.offAllNamed(
      page,
      arguments: arguments,
    );
  }

  static void popUntilPath(String ancestorPath) {
    // Get.offNamedUntil(ancestorPath,(c)=> false);
  }

  static Future<void> reset() {
    return Routes.popUntilMain();
  }
}

extension RoutesEx on String {
  String addQueryParams(Map<String, dynamic> data) {
    return "$this?${Uri(queryParameters: data).query}";
  }

  route({dynamic data, bool isMobile = false, bool newTab = false}) {
    if (kIsWeb) {
      // if(newTab){
      //   js.context.callMethod('open', [this]);
      //   return;
      // }
    }
    return Get.toNamed(
      this,
      arguments: data,
    );
  }

  void pushReplace({Map<String, dynamic>? data}) {
    Get.toNamed(this, arguments: data);
  }

  routeWeb({dynamic data, bool newTab = false}) {
    return Get.toNamed(this, arguments: data);
  }

  static navigator({required Widget child, dynamic arguments}) {
    return Navigator.push(
        navigatorKey.currentContext!,
        MaterialPageRoute(
          builder: (context) => child,
        ));
  }
}
