part of '../country_code_picker.dart';

extension ContEx on String {
  CountryCode get countryCodeByCode {
    return CountryCode.fromCountryCode(this);
  }

  CountryCode get countryCodeByDial {
    return CountryCode.fromDialCode(this);
  }
}

class CountryCodeController extends GetxController {
  static CountryCodeController get to => Get.find<CountryCodeController>();

  Rxn<CountryCode> countryCode = Rxn<CountryCode>();
  RxList<CountryCode> filteredElements = <CountryCode>[].obs;
  RxList<CountryCode> elements = <CountryCode>[].obs;

  final TextEditingController textSearchController = TextEditingController();
  final RxBool isLoading = false.obs;

  late CountryCodeOptions options;

  bool get hasSearch => textSearchController.text.isNotEmpty;
  bool get emptySearchResult => hasSearch && filteredElements.isEmpty;

  static String searchBuilder = "searchBuilder";

  void setOptions(CountryCodeOptions newOptions) {
    options = newOptions;
  }

  @override
  void onInit() {
    init();
    super.onInit();
  }

  Future<void> init() async {
    isLoading.value = true;
    final codesJson = await getAllCodes();
    final codes = codesJson.map((e) => CountryCode.fromJson(e)).toList();
    elements.value = codes;
    countryCode.value = Core.preferenceManager.getCountryCode;
    if (countryCode.value == null) {
      getCountryCodeByIp();
    }
    filterElements(textSearchController.text);
    isLoading.value = false;
  }

  void getCountryCodeByIp() async {
    CountryApi().getIpCountryCode("SA").then((code) {
      countryCode.value = CountryCode.fromCountryCode(code);
    });
  }

  Future<List<dynamic>> getAllCodes() async {
    final contents =
        await rootBundle.loadString('assets/json/countries/countries.json');
    return jsonDecode(contents) as List<dynamic>;
  }

  void onSelectCountryCode(CountryCode code) {
    countryCode.value = code;
    Core.preferenceManager.saveCountryCode(code);
    Navigator.pop(Get.context!, code);
  }

  void filterElements(String search) {
    search = search.toUpperCase();
    filteredElements.value = elements
        .where((e) =>
            e.code!.contains(search) ||
            e.dialCode!.contains(search) ||
            e.name!.toUpperCase().contains(search) ||
            e.nameAr!.toUpperCase().contains(search))
        .toList();
  }
}
