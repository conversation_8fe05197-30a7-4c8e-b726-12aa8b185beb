import 'dart:convert';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:fandooq/core/const/app.dart';
import 'package:fandooq/core/controllers/user-app/user_controller.dart';
import 'package:fandooq/core/core.dart' hide Response;
import 'package:fandooq/core/utils/snak_bar/snak_bar_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../cache/preference_manager.dart';
import '../components/logger.dart';

part 'app_apis.dart';
part 'dio_client.dart';
part 'error_response.dart';
part 'network_handler.dart';
part 'network_response.dart';
part 'network_state_enum.dart';
part 'network_mappers.dart';
