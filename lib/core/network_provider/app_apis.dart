part of 'networking.dart';

class AppApis {
  AppApis._();

  static String hotelsBaseUrl = 'https://api.test.hotelbeds.com/';

  // static String hotelsBaseUrlSecure = 'https://api-secure.test.hotelbeds.com';

  ///*************** Hotel-API  ***************///

  static const String _hotelApi = "/hotel-api/1.0";

  // //TODO: Home
  // static const String availableHotels = "$_hotelApi/hotels";

  //TODO: Booking
  // static const String bookings = "$_hotelApi/bookings";

  ///*************** Hotel-Content-API  ***************///
  // static const String _hotelContentApi = "/hotel-content-api/1.0";

  //TODO: Home
  // static const String hotels = "$_hotelContentApi/hotels";

  ///************************************************************************
  ///------------------------------------------------------------------------
//*************************************************************************

  static String get _localBaseUrlPlatform {
    if (Core.isRealDevice) {
      return 'http://192.168.1.108';
    }
    if (Platform.isAndroid) {
      return 'http://10.0.2.2';
    } else {
      return 'http://127.0.0.1';
    }
  }

  static String get apiBaseUrl {
    return '$baseUrl/api/';
  }

  static String get baseUrl {
    String baseUrl = "";
    if (AppConsts.localApi) {
      baseUrl = "$_localBaseUrlPlatform:3000";
    } else {
      // baseUrl = 'http://82.112.254.203';
      baseUrl = 'https://api.fandooq.com/';
    }
    return baseUrl;
  }

  static String get webSocketUrl {
    if (AppConsts.localApi) {
      return "${baseUrl.replaceAll("https", "wss").replaceAll("http", "ws")}/fandooq-web-socket";
    } else {
      return "${baseUrl.replaceAll("https", "wss").replaceAll("http", "ws")}/fandooq-web-socket";
    }
  }

  // static String get apiBaseUrl =>
  //     AppConsts.localApi ? (Platform.isIOS ? 'http://127.0.0.1:8080/api/' : 'http://10.0.2.2:8080/api/') : 'https://fandooq.com:8080/api/';

  static String googleBaseUrl = "https://maps.googleapis.com/";

  ///*************** Authentication APIs ***************///
  static const String login = "auth/sign-in-by-phone";
  static const String verifyOtp = "auth/verify-phone";

  ///*************** User Profile APIs ***************///
  static const String profile = 'users/profile';
  static const String currencies = 'users/currencies';

  ///*************** Search & Location APIs ***************///
  static const String autocompletePlaces = 'locations/autocomplete-places';
  // Legacy Google Places APIs (for backward compatibility)
  static const String autoComplete = 'place/autocomplete';
  static const String details = 'place/details';

  ///*************** Hotel Search APIs ***************///
  static const String hotelSearchByLocation = 'hotels/search/geolocation';
  static const String hotelSearchGeolocationAvailability =
      'hotels/search/geolocation-availability';
  // Legacy city-based search (deprecated)
  static const String hotelSearchByCity = 'hotels/search/city-availability';

  ///*************** Hotel Booking APIs ***************///
  static const String preBookHotel = 'hotels/booking/pre-book';
  static const String bookHotel = 'hotels/booking/book';
  static const String myBookings = 'hotels/my-bookings';
  static const String bookingDetails = 'hotels/my-bookings'; // + /{id}
  static const String cancelBooking = 'hotels/my-bookings/cancel';

  ///*************** Payment APIs ***************///
  static const String createPaymentIntent =
      'hotels/booking/payment/create-intent';
  static const String confirmPayment = 'hotels/booking/payment/confirm';
  // Legacy payment APIs (for backward compatibility)
  static const String create_setup_intent = 'create-setup-intent';
  static const String charge_card_off_session = 'charge-card-off-session';
  static const String get_user_list_payments = 'get-user-list-payments';
  static const String remove_payments_card = 'remove-payments-card';
  static const String create_payment_intent = 'create-payment-intent';
  static const String get_payment_method = 'get-payment-method';
  static const String attach_payment_method = 'attach-payment-method';

  ///*************** Currency APIs ***************///
  static const String convertCurrency = 'settings/currency/convert';

  ///*************** Legacy APIs (to be removed) ***************///
  @Deprecated('Use hotelSearchByCity instead')
  static const String hotels = 'hotels/available';
  @Deprecated('Use hotelSearchByCity instead')
  static const String searchHotels = 'hotels/availability';
  @Deprecated('Use preBookHotel instead')
  static const String checkrates = 'hotels/prebook';
  @Deprecated('Use myBookings instead')
  static const String bookings = 'hotels/bookings';

  // Legacy hotel content APIs (for backward compatibility)
  static const String hotels_contents = 'hotel-content-api/1.0';
  static const String hotelsContent = 'hotel-content-api/1.0/hotels';
  static const String hotelsContentFilter =
      'hotel-content-api/1.0/hotels-codes-filter';
  static const String hotelsContentNearBy =
      'hotel-content-api/1.0/hotels-nearby';
  static const String hotels_and_content = 'hotel-api/1.0/hotels_and_content';

  ///*************** Favorites APIs ***************///
  static const String favorites = 'favorites';
  static const String favoritesToggle = 'favorites/toggle';
  static const String favoritesCheck = 'favorites/check';
  static const String favoritesCount = 'favorites/count';

  ///*************** Terms, Privacy & About APIs ***************///
  static const String termsApi = 'terms';

  ///*************** Old APIs (kept for backward compatibility) ***************///
  static const String exchangerate = 'settings/getAllExchange';
  @Deprecated('Use favorites instead')
  static const String favorite = 'favorite';
  static const String requestToDeleteAccount = 'profile/requestToDeleteAccount';
  static const String settings = 'settings/getSettings';
  static const String ip = 'ip';
  static const String facilities = 'facilities';
  static const String profileUpdate = 'profileUpdate';

  //TODO: Travellers
  static const String travellers = 'travellers';
  static const String traveller = 'traveller';
}
