
import 'package:fandooq/core/utils/hotels/distance.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:fandooq/feature_plus/hotels/models/hotels_data_search.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../utils/location.dart';

extension HotelsFilter on Iterable<HotelData> {
  Iterable<HotelData> whereHasFreeCancellation() {
    return where((hotel) {
      final rooms = hotel.availability?.rooms ?? [];
      return rooms
          .any((room) => room.rates.any((rate) => rate.hasFreeCancellation));
    });
  }

  HotelData? findOneAvailablePrice(String hotelCode) {
    var hotels = where((e) => e.code == hotelCode);
    if (hotels.isNotEmpty) {
      var hotel = hotels.first;
      if (hotel.availability != null) {
        return hotel;
      }
    }
    return null;
  }

  HotelData? findOne(String hotelCode) {
    var hotels = where((e) => e.code == hotelCode);
    if (hotels.isNotEmpty) {
      var hotel = hotels.first;
      return hotel;
    }
    return null;
  }

  Iterable<HotelData> whereHasRoomOnly() {
    return where((hotel) {
      final rooms = hotel.availability?.rooms ?? [];
      return rooms.any((room) => room.rates.any((rate) => rate.hasRoomOnly));
    });
  }

  Iterable<HotelData> whereHasHalfBoard() {
    return where((hotel) {
      final rooms = hotel.availability?.rooms ?? [];
      return rooms.any((room) => room.rates.any((rate) => rate.hasHalfBoard));
    });
  }

  Iterable<HotelData> whereHasBreakfast() {
    return where((hotel) {
      final rooms = hotel.availability?.rooms ?? [];
      return rooms.any((room) => room.rates.any((rate) => rate.hasBreakfast));
    });
  }

  HotelRequest get request => HotelsDataSearch.to.request!;



  // List<HotelData> filterHotelsByDistance(
  //     GeoLocationRequest center, double maxDistanceKm) {
  //   double maxDistanceMeters =
  //       maxDistanceKm * 1000; // Convert kilometers to meters
  //   return where((hotel) {
  //     if (hotel.mapLatitude != null) {
  //       double hotelLat = hotel.mapLatitude!;
  //       double hotelLon = hotel.mapLongitude!;
  //       double distance = LocationUtils.calculateDistanceInMeters(
  //           center.latitude!, center.longitude!, hotelLat, hotelLon);
  //       return distance <= maxDistanceMeters;
  //     }
  //     return false;
  //   }).toList();
  // }
  //



  num get maxPrice {
    if (isEmpty) return 0.0;
    return map((hotel) => hotel.minRate).reduce((a, b) => a > b ? a : b);
  }

  List<String> get codes {
    return map((e) => e.code).toList();
  }

  double maxDistanceFromLocation({
    required LatLng point,
  }) {
    if (isEmpty) return 0.0;
    var farthestHotel = first;
    double maxDistance = calculateDistance(
        point, LatLng(farthestHotel.mapLatitude!, farthestHotel.mapLongitude!));
    for (var hotel in this) {
      double distance = calculateDistance(
          point, LatLng(hotel.mapLatitude!, hotel.mapLongitude!));
      if (distance > maxDistance) {
        maxDistance = distance;
        farthestHotel = hotel;
      }
    }
    return maxDistance;
  }

  int countFreeCancellation() {
    return where((e) => e.availability != null)
        .where((hotel) => hotel.availability!.containsFreeCancellation)
        .length;
  }

  int countIncludeBreakfast() {
    return where((e) => e.availability != null)
        .where((hotel) => hotel.availability!.containsBreakfastInclude)
        .length;
  }

  Iterable<HotelData> filterRangePrice({
    required num value,
    required HotelRequest request,
  }) {
    return where((hotel) {
      // Calculate the rate based on the price option
      var hotelRate = hotel.minRate;
      // Check if hotel rate is within the min and max rate inclusive.
      return hotelRate <= value;
    });
  }
}
