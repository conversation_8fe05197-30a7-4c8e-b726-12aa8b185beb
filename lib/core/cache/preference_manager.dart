import 'dart:convert';
import 'dart:ui';

import 'package:fandooq/core/components-state/pages/country/country_code_picker.dart';
import 'package:fandooq/core/models/app/ip_info.dart';
import 'package:fandooq/core/models/app/settings_app.dart';
import 'package:fandooq/core/models/user/default_booking_data.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:fandooq/core/components/currency/lib/models/currency.dart';

import '../../feature_plus/auth/models/user_model.dart';
import 'caching_keys.dart';

class PreferenceManager {
  PreferenceManager._();

  static late SharedPreferences _prefs;

  static PreferenceManager get instance => PreferenceManager._();

  static init() async {
    _prefs = await SharedPreferences.getInstance();
    return;
  }

  //todo**************************** Caching ***************************//
  Future<void> saveValue(String cachingKey, String value) async {
    final prefs = _prefs;
    await prefs.setString(cachingKey, value);
  }

  Future<String?> getValue(String cachingKey) async {
    final prefs = _prefs;
    return prefs.getString(cachingKey);
  }

  //todo**************************** Token ***************************//
  Future<void> saveFCMToken(String fcmToken) async =>
      await saveValue(CachingKey.FCM_TOKEN, fcmToken);

  String fcmToken() {
    final prefs = _prefs;
    return prefs.getString(CachingKey.FCM_TOKEN) ?? "";
  }

  Future<void> removeFCMToken() async {
    final prefs = _prefs;
    await prefs.remove(CachingKey.FCM_TOKEN);
  }

  //todo**************************** OnBoarding ***************************//
  Future<void> saveIsFirstTime(bool isFirstTime) async {
    final prefs = _prefs;
    await prefs.setBool(CachingKey.IS_FIRST_TIME, isFirstTime);
  }

  Future<bool> isFirstTime() async {
    final prefs = _prefs;
    return prefs.getBool(CachingKey.IS_FIRST_TIME) ?? true;
  }

  //todo******************** IsLoggedIn Or Guest *************************//
  Future<void> saveIsLoggedIn(bool isLoggedIn) async {
    final prefs = _prefs;
    await prefs.setBool(CachingKey.IS_LOGGED_IN, isLoggedIn);
  }

  bool isLoggedIn() {
    final prefs = _prefs;
    return prefs.getBool(CachingKey.IS_LOGGED_IN) ?? false;
  }

  //todo**************************** Remember Me ***************************//
  Future<void> saveRememberMe(bool rememberMe) async {
    final prefs = _prefs;
    await prefs.setBool(CachingKey.REMEMBER_ME, rememberMe);
  }

  Future<bool> rememberMe() async {
    final prefs = _prefs;
    return prefs.getBool(CachingKey.REMEMBER_ME) ?? false;
  }

  //todo**************************** Favorite ***************************//
  Future<void> saveFavoriteState(bool isRequestSuccessFirstTime) async {
    final prefs = _prefs;
    await prefs.setBool(CachingKey.FAVORITE, isRequestSuccessFirstTime);
  }

  bool isFavoriteHasRequestedOneTime() {
    final prefs = _prefs;
    return prefs.getBool(CachingKey.FAVORITE) ?? false;
  }

  Future<void> saveCurrency(Currency currency) async {
    final prefs = _prefs;
    await prefs.setString(CachingKey.CURRENCY, jsonEncode(currency.toJson()));
  }

  Currency getCurrency() {
    final prefs = _prefs;
    final json = prefs.getString(CachingKey.CURRENCY);
    if (json != null) {
      return Currency.fromJson(jsonDecode(json));
    }
    return Currency(currency: "EUR", currencySign: "€");
  }

  //todo**************************** AuthToken ***************************//
  Future<void> saveAuthToken(String? authToken) async =>
      await saveValue(CachingKey.AUTH_TOKEN, authToken ?? '');

  String get authToken {
    final prefs = _prefs;
    return prefs.getString(CachingKey.AUTH_TOKEN) ?? "";
  }

  Future<void> removeAuthToken() async {
    final prefs = _prefs;
    await prefs.remove(CachingKey.AUTH_TOKEN);
  }

  //todo**************************** AuthUser ***************************//
  Future<void> saveUserModel(UserModel userModel) async {
    final prefs = _prefs;
    await prefs.setString(CachingKey.USER_MODEL, jsonEncode(userModel.toMap()));
  }

  UserModel? userData() {
    final prefs = _prefs;
    final json = prefs.getString(CachingKey.USER_MODEL);
    if (json != null) {
      return UserModel.fromJson(jsonDecode(json));
    }
    return null;
  }

  Future<UserModel?> get userModel async => userData();

  int? uId() {
    final user = userData();
    return user?.id;
  }

  Future<bool> isMe(String email) async {
    final user = userData();
    return user?.email == email;
  }

  Future<void> removeUserModel() async {
    final prefs = _prefs;
    await prefs.remove(CachingKey.USER_MODEL);
  }

  //todo**************************** Locale ***************************//
  Future<void> saveLocal(String code) async {
    final prefs = _prefs;
    await prefs.setString(CachingKey.LANGUAGE, code);
  }

  Locale get getLocale {
    final prefs = _prefs;
    return Locale(prefs.getString(CachingKey.LANGUAGE) ?? "en");
  }

  //todo**************************** Country ***************************//
  Future<void> saveCountryCode(CountryCode code) async {
    final prefs = _prefs;
    await prefs.setString(CachingKey.COUNTRY_CODE, jsonEncode(code.toJson()));
  }

  CountryCode? get getCountryCode {
    final prefs = _prefs;
    if (prefs.containsKey(CachingKey.COUNTRY_CODE)) {
      return CountryCode.fromJson(
          jsonDecode(prefs.getString(CachingKey.COUNTRY_CODE)!));
    }
    return null;
  }

  //todo*********************** Clear All Caching ***********************//
  Future<void> logout() async {
    await removeUserModel();
    await removeAuthToken();
    await removeFCMToken();
    await saveIsLoggedIn(false);
    await saveFavoriteState(false);
    // HiveHelpers.clearHive();
  }

  //todo******************** Clear All Caching GUEST ***********************//
  Future<void> logoutAsAGuest() async {
    await removeUserModel();
    await removeAuthToken();
    await saveIsLoggedIn(false);
    await removeFCMToken();
    // HiveHelpers.clearHive();
  }

  Future<void> login({required UserModel user}) async {
    await saveAuthToken(user.token);
    await saveUserModel(user);
    await saveIsLoggedIn(true);
  }

  Future<void> saveAppSettings(SettingsAppResponse response) async {
    final prefs = _prefs;
    await prefs.setString(CachingKey.SETTINGS_APP,
        jsonEncode(response.copyWith(lastUpdate: DateTime.now()).toJson()));
  }

  Future<void> saveIp(IPInfoData ip) async {
    final prefs = _prefs;
    await prefs.setString(CachingKey.IP, jsonEncode(ip.toJson()));
  }

  IPInfoData? getIp() {
    final prefs = _prefs;
    final json = prefs.getString(CachingKey.IP);
    if (json != null) {
      return IPInfoData.fromJson(jsonDecode(json));
    }
    return null;
  }

  SettingsAppResponse? getSettingsApp() {
    final prefs = _prefs;
    final json = prefs.getString(CachingKey.SETTINGS_APP);
    if (json != null) {
      return SettingsAppResponse.fromJson(jsonDecode(json));
    }
    return null;
  }

  Future<void> updateDefaultBookingUserData(DefaultBookingData response) async {
    final prefs = _prefs;
    await prefs.setString(
        CachingKey.DEFAULT_BOOKING_USER_DETAILS, jsonEncode(response.toJson()));
  }

  DefaultBookingData getDefaultBookingUserData() {
    final prefs = _prefs;
    final json = prefs.getString(CachingKey.DEFAULT_BOOKING_USER_DETAILS);
    if (json != null) {
      return DefaultBookingData.fromJson(jsonDecode(json));
    }
    return DefaultBookingData();
  }

  // List<String> subscribedChannels() {
  //   final prefs =  _prefs;
  //   return prefs.getStringList(CachingKey.CHANNELS) ?? [];
  // }
  //
  // Future<void> _saveStringList(List<String> ) async {
  //   final SharedPreferences prefs = await SharedPreferences.getInstance();
  //   await prefs.setStringList(CachingKey.CHANNELS, stringList);
  // }
}
