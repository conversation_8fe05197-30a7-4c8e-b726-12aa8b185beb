import 'package:fandooq/core/models/hotels/src/hotel_data.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:flutter/cupertino.dart';
import 'package:hive/hive.dart';

import '../../hive_helpers/tables.dart';
import 'favorites_db.dart';

class FavoriteDBImp implements IFavoriteHotelsDB {

  @override
  bool clearFavorites() {
    bool success = false;
    try {
      final _box = Hive.box<Map>(HiveBoxTablesNames.FAVORITES_BOX);
      _box.clear();
      debugPrint("Clear Data Base Table ::  ${HiveBoxTablesNames.FAVORITES_BOX} ::: ");
      success = true;
    } catch (e, s) {
      debugPrint("CATCH_ERROR  $e , $s");
      success = false;
    }
    return success;
  }

  @override
  bool deleteFavorite(HotelData model) {
    bool success = false;
    try {
      final _box = Hive.box<Map>(HiveBoxTablesNames.FAVORITES_BOX);
      _box.delete(model.code);
      debugPrint("DELETE_FAVORITES>>  $success");
      success = true;
    } catch (e, s) {
      debugPrint("Catch_error $e , $s");
      success = false;
    }
    return success;
  }

  @override
  List<HotelData> gatAllFavorites() {
    List<HotelData> list = [];
    try {
      final _box = Hive.box<Map>(HiveBoxTablesNames.FAVORITES_BOX);
      list = _box.values.map((e) => HotelData.fromJson(e)).toList();
      debugPrint("GET_ALL_FAVORITES>>  ${list.length}");
    } catch (e, s) {
      debugPrint("CATCH_ERROR  $e , $s");
    }
    return list;
  }

  @override
  int getNumbersOfFavorites() {
    int length = 0;
    try {
      final _box = Hive.box<Map>(HiveBoxTablesNames.FAVORITES_BOX);
      length = _box.length;
      debugPrint("GET_LENGTH_FAVORITES>>  $length");
    } catch (e, s) {
      debugPrint("CATCH_ERROR  $e , $s");
    }
    return length;
  }

  @override
  bool saveFavorite(HotelData model) {
    bool success = false;
    try {
      final _box = Hive.box<Map>(HiveBoxTablesNames.FAVORITES_BOX);
      _box.put(model.code, model.toJson());
      debugPrint("SAVE_FAVORITES>>  $success");
      success = true;
    } catch (e, s) {
      debugPrint("CATCH_ERROR  $e , $s");
      success = false;
    }
    return success;
  }

}
