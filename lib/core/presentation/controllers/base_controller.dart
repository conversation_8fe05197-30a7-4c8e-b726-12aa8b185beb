import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../domain/failures/failure.dart';

/// Base controller that provides common functionality for all controllers
abstract class BaseController extends GetxController {
  /// Loading state
  final RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;
  
  /// Error state
  final Rx<String?> _error = Rx<String?>(null);
  String? get error => _error.value;
  
  /// Success message state
  final Rx<String?> _successMessage = Rx<String?>(null);
  String? get successMessage => _successMessage.value;
  

  /// Set loading state
  void setLoading(bool loading) {
    _isLoading.value = loading;
  }

  /// Set error message
  void setError(String? error) {
    _error.value = error;
    if (error != null) {
      _successMessage.value = null; // Clear success message when error occurs
    }
  }

  /// Set success message
  void setSuccessMessage(String? message) {
    _successMessage.value = message;
    if (message != null) {
      _error.value = null; // Clear error when success occurs
    }
  }

  /// Clear all messages
  void clearMessages() {
    _error.value = null;
    _successMessage.value = null;
  }

  /// Handle failure and convert to user-friendly message
  void handleFailure(Failure failure) {
    String message;
    
    switch (failure.runtimeType) {
      case NetworkFailure:
        message = 'network_error'.tr;
        break;
      case ServerFailure:
        message = 'server_error'.tr;
        break;
      case AuthenticationFailure:
        message = 'authentication_error'.tr;
        break;
      case ValidationFailure:
        message = failure.message; // Validation messages are usually user-friendly
        break;
      case NotFoundFailure:
        message = 'not_found_error'.tr;
        break;
      default:
        message = 'unknown_error'.tr;
    }
    
    setError(message);
  }

  /// Execute an async operation with loading state and error handling
  Future<T?> executeWithLoading<T>(
    Future<T> Function() operation, {
    String? successMessage,
    bool showLoading = true,
  }) async {
    try {
      if (showLoading) setLoading(true);
      clearMessages();
      
      final result = await operation();
      
      if (successMessage != null) {
        setSuccessMessage(successMessage);
      }
      
      return result;
    } catch (e) {
      if (e is Failure) {
        handleFailure(e);
      } else {
        setError('unknown_error'.tr);
      }
      return null;
    } finally {
      if (showLoading) setLoading(false);
    }
  }

  /// Show snackbar with message
  void showSnackBar(String message, {bool isError = false}) {
    Get.snackbar(
      isError ? 'error'.tr : 'info'.tr,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: isError ? Get.theme.colorScheme.error : Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      duration: const Duration(seconds: 3),
    );
  }

  /// Show error snackbar
  void showErrorSnackBar(String message) {
    showSnackBar(message, isError: true);
  }

  /// Show success snackbar
  void showSuccessSnackBar(String message) {
    showSnackBar(message, isError: false);
  }

  @override
  void onInit() {
    super.onInit();
    
    // Listen to error changes and show snackbar
    ever(_error, (String? error) {
      if (error != null && error.isNotEmpty) {
        showErrorSnackBar(error);
      }
    });
    
    // Listen to success message changes and show snackbar
    ever(_successMessage, (String? message) {
      if (message != null && message.isNotEmpty) {
        showSuccessSnackBar(message);
      }
    });
  }
}

/// Base controller for paginated data
abstract class BasePaginatedController<T> extends BaseController {
  /// Current page
  final RxInt _currentPage = 1.obs;
  int get currentPage => _currentPage.value;
  
  /// Items per page
  final int itemsPerPage;
  
  /// Total items count
  final RxInt _totalItems = 0.obs;
  int get totalItems => _totalItems.value;
  
  /// Items list
  final RxList<T> _items = <T>[].obs;
  List<T> get items => _items;
  
  /// Has more items to load
  bool get hasMore => _currentPage.value * itemsPerPage < _totalItems.value;
  
  /// Is loading more items
  final RxBool _isLoadingMore = false.obs;
  bool get isLoadingMore => _isLoadingMore.value;

  BasePaginatedController({this.itemsPerPage = 10});

  /// Load first page
  Future<void> loadFirstPage() async {
    _currentPage.value = 1;
    _items.clear();
    await loadPage();
  }

  /// Load next page
  Future<void> loadNextPage() async {
    if (!hasMore || _isLoadingMore.value) return;
    
    _currentPage.value++;
    _isLoadingMore.value = true;
    
    try {
      await loadPage();
    } finally {
      _isLoadingMore.value = false;
    }
  }

  /// Refresh data
  Future<void> refresh() async {
    await loadFirstPage();
  }

  /// Abstract method to load page data
  Future<void> loadPage();

  /// Update items and total count
  void updatePaginatedData(List<T> newItems, int totalCount) {
    if (_currentPage.value == 1) {
      _items.assignAll(newItems);
    } else {
      _items.addAll(newItems);
    }
    _totalItems.value = totalCount;
  }
}
