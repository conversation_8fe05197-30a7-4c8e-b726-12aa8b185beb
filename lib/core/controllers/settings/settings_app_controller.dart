
import 'package:equatable/equatable.dart';
import 'package:fandooq/core/components/currency/lib/currency.dart';
import 'package:fandooq/core/components/currency/lib/models/currency.dart';
import 'package:fandooq/core/core.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../../models/app/settings_app.dart';
import 'package:get/get.dart';

part 'settings_app_state.dart';

class SettingsAppController extends GetxController{

  static SettingsAppController get to => Get.find<SettingsAppController>();


  var state = Rx<SettingsState>(SettingsInitialState());

  SettingsAppResponse? get settingsAppResponse{
    return (state.value as SettingsLoadedState).settings;
  }

  String? get country{
    return settingsAppResponse?.country ?? "EG";
  }

  Currency? get currency{
    return Core.preferenceManager.getCurrency();
  }

  @override
  void onInit() {
    listener();
    super.onInit();
  }

  void listener(){
    ever(state, (state){
      if(state is SettingsLoadedState){

      }
    });
  }

}