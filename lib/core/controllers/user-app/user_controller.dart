import 'package:fandooq/core/cache/preference_manager.dart';
import 'package:fandooq/core/components/snack_bar/toast.dart';
import 'package:fandooq/core/lang/app_strings.dart';
import 'package:fandooq/core/models/hotels/hotels.dart';
import 'package:fandooq/feature_plus/auth/controllers/auth_controller.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:fandooq/repos/user/user.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../feature_plus/auth/models/user_model.dart' show UserModel;

class UserController extends GetxController {
  static String favoriteBuilder = "favoriteBuilder";

  static String userUpdate = "userUpdate";

  static UserController get to => Get.find();

  var repo = UserRepo.to;

  @override
  void onInit() {
    super.onInit();
    _checkSavedAuthentication();
  }

  /// Check for saved authentication on app startup
  Future<void> _checkSavedAuthentication() async {
    try {
      // Register AuthPlusController if not already registered
      if (!Get.isRegistered<AuthPlusController>()) {
        Get.put(AuthPlusController());
        print('✅ AuthPlusController registered for authentication check');
      } else {
        print('✅ AuthPlusController already registered');
      }
    } catch (e) {
      print('❌ Error checking saved authentication: $e');
    }
  }

  bool get isLoggedIn => PreferenceManager.instance.isLoggedIn();

  int? get userId => PreferenceManager.instance.userData()?.id;

  void togglFav(HotelData hotelContent, BuildContext context) async {
    if (!isLoggedIn) {
      Get.toNamed('/auth-plus');
      return;
    }
    if (repo.favExist(hotelContent.code)) {
      removeFav(hotelContent, context);
    } else {
      addFav(hotelContent, context);
    }
  }

  void addFav(HotelData hotelContent, BuildContext context) {
    Toast.success(title: AppStrings.addedToFavorite.tr, context: context);
    repo.addFavorite(hotelContent);
    update([favoriteBuilder]);
  }

  void removeFav(HotelData hotelContent, BuildContext context) {
    Toast.success(title: AppStrings.removedFromFavorite.tr, context: context);
    repo.removeFavorite(hotelContent.code);
    update([favoriteBuilder]);
  }

  void onUnAuth() async {
    await PreferenceManager.instance.logout();
    update([userUpdate]);
  }

  void logOut(BuildContext context) async {
    await PreferenceManager.instance.logout();
    Toast.success(title: AppStrings.logOutSuccess.tr, context: context);
    update([userUpdate]);
    // Get.offAllNamed(Routes.MAIN_SCREEN);
  }

  Future<void> logOutWithoutMessage() async {
    await PreferenceManager.instance.logout();
    update([userUpdate]);
  }

  void onLogIn(BuildContext context) async {
    // Toast.success(title: AppStrings.logInSuccess.tr,context: context);
    update([userUpdate]);
  }

  Future<bool> login(UserModel user) async {
    PreferenceManager.instance.login(user: user);
    update([userUpdate]);
    return false;
  }

  void updateUserBuilder() {
    update([userUpdate]);
  }

  UserModel? getUser() {
    return PreferenceManager.instance.userData();
  }
}
