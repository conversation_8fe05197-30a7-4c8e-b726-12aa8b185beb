/// 🔗 **App Link Handler**
///
/// Handles incoming deep links and app links for the Fandooq app.
/// This service integrates with the platform-specific configurations
/// and routes users to the appropriate screens.
library;

import 'dart:async';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:uni_links/uni_links.dart';
import '../components/logger.dart';
import '../../feature_plus/hotels/services/deep_link_service.dart';

class AppLinkHandler extends GetxService {
  StreamSubscription<String?>? _linkSubscription;
  late DeepLinkService _deepLinkService;

  @override
  Future<void> onInit() async {
    super.onInit();

    // Get deep link service
    _deepLinkService = Get.find<DeepLinkService>();

    // Initialize link handling
    await _initializeLinkHandling();

    Log.debug('🔗 AppLinkHandler initialized');
  }

  @override
  void onClose() {
    _linkSubscription?.cancel();
    super.onClose();
  }

  /// Initialize link handling for both cold start and warm start
  Future<void> _initializeLinkHandling() async {
    try {
      // Handle app launch from link (cold start)
      await _handleInitialLink();

      // Handle incoming links while app is running (warm start)
      _handleIncomingLinks();
    } catch (e) {
      Log.error('❌ Error initializing link handling: $e');
    }
  }

  /// Handle the initial link that launched the app
  Future<void> _handleInitialLink() async {
    try {
      // Get the initial link if app was launched from a link
      final String? initialLink = await _getInitialLink();

      if (initialLink != null && initialLink.isNotEmpty) {
        Log.debug('🔗 App launched with initial link: $initialLink');

        // Delay handling to ensure app is fully initialized
        await Future.delayed(const Duration(milliseconds: 1000));

        await _processLink(initialLink);
      }
    } catch (e) {
      Log.error('❌ Error handling initial link: $e');
    }
  }

  /// Handle incoming links while app is running
  void _handleIncomingLinks() {
    try {
      // Listen for incoming links using uni_links
      _linkSubscription = linkStream.listen(
        (String? link) async {
          if (link != null && link.isNotEmpty) {
            Log.debug('🔗 Received incoming link: $link');
            await _processLink(link);
          }
        },
        onError: (error) {
          Log.error('❌ Error in link stream: $error');
        },
      );

      Log.debug('🔗 Link stream handling initialized with uni_links');
    } catch (e) {
      Log.error('❌ Error setting up link stream: $e');
    }
  }

  /// Process the received link
  Future<void> _processLink(String link) async {
    try {
      Log.debug('🔗 Processing link: $link');

      // Validate link format
      if (!_isValidFandooqLink(link)) {
        Log.warning('⚠️ Invalid Fandooq link format: $link');
        return;
      }

      // Handle the link using DeepLinkService
      await _deepLinkService.handleDeepLink(link);
    } catch (e) {
      Log.error('❌ Error processing link: $e');
    }
  }

  /// Check if the link is a valid Fandooq link
  bool _isValidFandooqLink(String link) {
    try {
      final uri = Uri.parse(link);

      // Check for custom scheme
      if (uri.scheme == 'fandooq') {
        return ['hotel', 'booking', 'search'].contains(uri.host);
      }

      // Check for HTTPS scheme
      if (uri.scheme == 'https') {
        return uri.host == 'fandooq' &&
            (uri.path.startsWith('/hotel') ||
                uri.path.startsWith('/booking') ||
                uri.path.startsWith('/search') ||
                uri.path == '/');
      }

      return false;
    } catch (e) {
      Log.error('❌ Error validating link: $e');
      return false;
    }
  }

  /// Get the initial link (platform-specific implementation)
  Future<String?> _getInitialLink() async {
    try {
      // Get initial link using uni_links
      final String? initialLink = await getInitialLink();
      return initialLink;
    } on PlatformException catch (e) {
      Log.error('❌ Platform exception getting initial link: $e');
      return null;
    } catch (e) {
      Log.error('❌ Error getting initial link: $e');
      return null;
    }
  }

  /// Manual link handling for testing
  Future<void> handleTestLink(String link) async {
    Log.debug('🧪 Handling test link: $link');
    await _processLink(link);
  }
}

/// 🔗 **App Link Handler Extension for GetX**
extension AppLinkHandlerExtension on GetInterface {
  AppLinkHandler get appLinkHandler => Get.find<AppLinkHandler>();
}

/// 🔗 **App Link Handler Binding**
class AppLinkHandlerBinding extends Bindings {
  @override
  void dependencies() {
    // Register AppLinkHandler as a service
    Get.put(AppLinkHandler(), permanent: true);
  }
}

/// 🔗 **App Link Testing Utilities**
class AppLinkTester {
  static final AppLinkHandler _handler = Get.find<AppLinkHandler>();

  /// Test hotel deep link with paxRooms
  static Future<void> testHotelLink() async {
    // Create test paxRooms JSON
    const paxRoomsJson = '[{"Adults":2,"Children":1,"ChildrenAges":[8]}]';
    final encodedPaxRooms = Uri.encodeComponent(paxRoomsJson);

    final testLink =
        'https://fandooq/hotel?code=1107165&name=Lapita%20Dubai%20Parks&city=Dubai&stars=5&checkin=2025-07-28&checkout=2025-07-30&adults=2&children=1&rooms=1&paxRooms=$encodedPaxRooms';
    await _handler.handleTestLink(testLink);
  }

  /// Test booking deep link
  static Future<void> testBookingLink() async {
    const testLink =
        'https://fandooq/booking?ref=BOOK-123456&hotel=Test%20Hotel&id=cmdl8st3n0001mnai4c3cu4lb';
    await _handler.handleTestLink(testLink);
  }

  /// Test search deep link
  static Future<void> testSearchLink() async {
    const testLink =
        'https://fandooq/search?city=Dubai&checkin=2025-01-15&checkout=2025-01-18&adults=2';
    await _handler.handleTestLink(testLink);
  }

  /// Test date range bottom sheet
  static Future<void> testDateRangeBottomSheet() async {
    // First navigate to hotel details
    await testHotelLink();

    // Wait a bit for the page to load
    await Future.delayed(const Duration(seconds: 2));

    // Simulate clicking on date button to open bottom sheet
    // This would normally be triggered by user interaction
    Log.debug('🗓️ Testing date range bottom sheet...');
    Log.debug(
        '📱 In a real scenario, user would tap on check-in or check-out date button');
    Log.debug('🎯 This would open the beautiful date range bottom sheet');
  }

  /// Test custom scheme link with paxRooms
  static Future<void> testCustomSchemeLink() async {
    // Create test paxRooms JSON
    const paxRoomsJson = '[{"Adults":2,"Children":1,"ChildrenAges":[8]}]';
    final encodedPaxRooms = Uri.encodeComponent(paxRoomsJson);

    final testLink =
        'fandooq://hotel?code=1497248&name=Lapita%20Dubai%20Parks&checkin=2025-07-28&checkout=2025-07-30&adults=2&children=1&rooms=1&paxRooms=$encodedPaxRooms';
    await _handler.handleTestLink(testLink);
  }
}
