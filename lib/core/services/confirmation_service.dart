import 'package:fandooq/core/extensions/string.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/confirmation_dialog.dart';

/// 🔔 **خدمة تأكيد العمليات**
/// خدمة شاملة لعرض نوافذ التأكيد في جميع أنحاء التطبيق
class ConfirmationService {
  /// عرض نافذة تأكيد مخصصة
  ///
  /// **المعاملات:**
  /// - [title]: عنوان النافذة
  /// - [message]: رسالة التأكيد
  /// - [confirmText]: نص زر التأكيد (افتراضي: "تأكيد")
  /// - [cancelText]: نص زر الإلغاء (افتراضي: "إلغاء")
  /// - [confirmColor]: لون زر التأكيد
  /// - [isDangerous]: هل العملية خطيرة (يغير لون زر التأكيد للأحمر)
  /// - [barrierDismissible]: هل يمكن إغلاق النافذة بالضغط خارجها
  ///
  /// **العائد:**
  /// - `true` إذا ضغط المستخدم على تأكيد
  /// - `false` إذا ضغط المستخدم على إلغاء أو أغلق النافذة
  static Future<bool> show({
    required String title,
    required String message,
    String? confirmText,
    String? cancelText,
    Color? confirmColor,
    bool isDangerous = false,
    bool barrierDismissible = true,
  }) async {
    final result = await Get.dialog<bool>(
      ConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText ?? 'confirm'.tr,
        cancelText: cancelText ?? 'cancel'.tr,
        confirmColor: confirmColor ?? (isDangerous ? Colors.red : null),
        isDangerous: isDangerous,
      ),
      barrierDismissible: barrierDismissible,
    );

    return result ?? false;
  }

  /// عرض تأكيد تسجيل الخروج
  static Future<bool> showLogoutConfirmation() async {
    return await show(
      title: 'logout_confirmation_title'.tr,
      message: 'logout_confirmation_message'.tr,
      confirmText: 'logout'.tr,
      cancelText: 'cancel'.tr,
      isDangerous: true,
    );
  }

  /// عرض تأكيد الخروج من التطبيق
  static Future<bool> showExitAppConfirmation() async {
    return await show(
      title: 'exit_app_confirmation_title'.tr,
      message: 'exit_app_confirmation_message'.tr,
      confirmText: 'exit'.tr,
      cancelText: 'cancel'.tr,
      isDangerous: true,
    );
  }

  /// عرض تأكيد حذف عنصر
  static Future<bool> showDeleteConfirmation({
    required String itemName,
    String? customMessage,
  }) async {
    return await show(
      title: 'delete_confirmation_title'.tr,
      message: customMessage ??
          'delete_confirmation_message'.tr.replaceAll('{item}', itemName),
      confirmText: 'delete'.tr,
      cancelText: 'cancel'.tr,
      isDangerous: true,
    );
  }

  /// عرض تأكيد إلغاء الحجز
  static Future<bool> showCancelBookingConfirmation() async {
    return await show(
      title: 'cancel_booking_confirmation_title'.tr,
      message: 'cancel_booking_confirmation_message'.tr,
      confirmText: 'cancel_booking'.tr,
      cancelText: 'keep_booking'.tr,
      isDangerous: true,
    );
  }

  /// عرض تأكيد عام مع رسالة مخصصة
  static Future<bool> showCustomConfirmation({
    required String title,
    required String message,
    String? confirmText,
    String? cancelText,
    bool isDangerous = false,
  }) async {
    return await show(
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: cancelText,
      isDangerous: isDangerous,
    );
  }

  /// عرض تأكيد حفظ التغييرات
  static Future<bool> showSaveChangesConfirmation() async {
    return await show(
      title: 'save_changes_confirmation_title'.tr,
      message: 'save_changes_confirmation_message'.tr,
      confirmText: 'save'.tr,
      cancelText: 'discard'.tr,
      isDangerous: false,
    );
  }

  /// عرض تأكيد إعادة تعيين الإعدادات
  static Future<bool> showResetSettingsConfirmation() async {
    return await show(
      title: 'reset_settings_confirmation_title'.tr,
      message: 'reset_settings_confirmation_message'.tr,
      confirmText: 'reset'.tr,
      cancelText: 'cancel'.tr,
      isDangerous: true,
    );
  }
}
