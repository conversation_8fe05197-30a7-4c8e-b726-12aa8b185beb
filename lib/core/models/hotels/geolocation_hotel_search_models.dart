// import 'package:fandooq/core/network_provider/networking.dart';
//
// /// Request parameters for geolocation-based hotel search
// class GeoLocationHotelSearchRequest {
//   final double latitude;
//   final double longitude;
//   String? checkIn;
//   String? checkOut;
//   int? adults;
//   int? children;
//   int? rooms;
//   String? currency;
//   double? radiusKm; // Search radius in kilometers
//   int? maxResults; // Maximum number of results
//
//   GeoLocationHotelSearchRequest({
//     required this.latitude,
//     required this.longitude,
//     this.checkIn,
//     this.checkOut,
//     this.adults = 2,
//     this.children = 0,
//     this.rooms = 1,
//     this.currency = 'USD',
//     this.radiusKm = 10.0,
//     this.maxResults = 50,
//   });
//
//   Map<String, dynamic> toQueryParams() {
//     final Map<String, dynamic> params = {};
//
//     params['latitude'] = latitude.toString();
//     params['longitude'] = longitude.toString();
//     if (checkIn != null) params['checkIn'] = checkIn;
//     if (checkOut != null) params['checkOut'] = checkOut;
//     if (adults != null) params['adults'] = adults.toString();
//     if (children != null) params['children'] = children.toString();
//     if (rooms != null) params['rooms'] = rooms.toString();
//     if (currency != null) params['currency'] = currency;
//     if (radiusKm != null) params['radiusKm'] = radiusKm.toString();
//     if (maxResults != null) params['maxResults'] = maxResults.toString();
//
//     return params;
//   }
//
//   GeoLocationHotelSearchRequest copyWith({
//     double? latitude,
//     double? longitude,
//     String? checkIn,
//     String? checkOut,
//     int? adults,
//     int? children,
//     int? rooms,
//     String? currency,
//     double? radiusKm,
//     int? maxResults,
//   }) {
//     return GeoLocationHotelSearchRequest(
//       latitude: latitude ?? this.latitude,
//       longitude: longitude ?? this.longitude,
//       checkIn: checkIn ?? this.checkIn,
//       checkOut: checkOut ?? this.checkOut,
//       adults: adults ?? this.adults,
//       children: children ?? this.children,
//       rooms: rooms ?? this.rooms,
//       currency: currency ?? this.currency,
//       radiusKm: radiusKm ?? this.radiusKm,
//       maxResults: maxResults ?? this.maxResults,
//     );
//   }
//
//   @override
//   String toString() {
//     return 'GeoLocationHotelSearchRequest{latitude: $latitude, longitude: $longitude, checkIn: $checkIn, checkOut: $checkOut, adults: $adults, children: $children, rooms: $rooms, currency: $currency, radiusKm: $radiusKm, maxResults: $maxResults}';
//   }
// }
//
// /// Model for hotel with distance information
// class GeoLocationHotel {
//   String? hotelCode;
//   String? hotelName;
//   int? starRating;
//   String? address;
//   double? latitude;
//   double? longitude;
//   double? distanceKm; // Distance from search location in kilometers
//   List<GeoLocationRoomAvailability>? rooms;
//
//   GeoLocationHotel({
//     this.hotelCode,
//     this.hotelName,
//     this.starRating,
//     this.address,
//     this.latitude,
//     this.longitude,
//     this.distanceKm,
//     this.rooms,
//   });
//
//   factory GeoLocationHotel.fromJson(Map<String, dynamic> json) {
//     List<GeoLocationRoomAvailability>? roomsList;
//     if (json['rooms'] != null) {
//       roomsList = (json['rooms'] as List)
//           .map((room) => GeoLocationRoomAvailability.fromJson(room))
//           .toList();
//     }
//
//     return GeoLocationHotel(
//       hotelCode: json['hotelCode'],
//       hotelName: json['hotelName'],
//       starRating: json['starRating'],
//       address: json['address'],
//       latitude: json['latitude']?.toDouble(),
//       longitude: json['longitude']?.toDouble(),
//       distanceKm: json['distanceKm']?.toDouble(),
//       rooms: roomsList,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'hotelCode': hotelCode,
//       'hotelName': hotelName,
//       'starRating': starRating,
//       'address': address,
//       'latitude': latitude,
//       'longitude': longitude,
//       'distanceKm': distanceKm,
//       'rooms': rooms?.map((room) => room.toJson()).toList(),
//     };
//   }
// }
//
// /// Model for room availability in geolocation search
// class GeoLocationRoomAvailability {
//   String? roomType;
//   double? totalFare;
//   String? currency;
//   String? bookingCode;
//   String? cancellationPolicy;
//   bool? isAvailable;
//
//   GeoLocationRoomAvailability({
//     this.roomType,
//     this.totalFare,
//     this.currency,
//     this.bookingCode,
//     this.cancellationPolicy,
//     this.isAvailable = true,
//   });
//
//   factory GeoLocationRoomAvailability.fromJson(Map<String, dynamic> json) {
//     return GeoLocationRoomAvailability(
//       roomType: json['roomType'],
//       totalFare: json['totalFare']?.toDouble(),
//       currency: json['currency'],
//       bookingCode: json['bookingCode'],
//       cancellationPolicy: json['cancellationPolicy'],
//       isAvailable: json['isAvailable'] ?? true,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'roomType': roomType,
//       'totalFare': totalFare,
//       'currency': currency,
//       'bookingCode': bookingCode,
//       'cancellationPolicy': cancellationPolicy,
//       'isAvailable': isAvailable,
//     };
//   }
// }
//
// /// Model for search area information
// class SearchAreaInfo {
//   double? centerLatitude;
//   double? centerLongitude;
//   double? radiusKm;
//   String? areaName; // Nearest city or area name
//   String? countryCode;
//
//   SearchAreaInfo({
//     this.centerLatitude,
//     this.centerLongitude,
//     this.radiusKm,
//     this.areaName,
//     this.countryCode,
//   });
//
//   factory SearchAreaInfo.fromJson(Map<String, dynamic> json) {
//     return SearchAreaInfo(
//       centerLatitude: json['centerLatitude']?.toDouble(),
//       centerLongitude: json['centerLongitude']?.toDouble(),
//       radiusKm: json['radiusKm']?.toDouble(),
//       areaName: json['areaName'],
//       countryCode: json['countryCode'],
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'centerLatitude': centerLatitude,
//       'centerLongitude': centerLongitude,
//       'radiusKm': radiusKm,
//       'areaName': areaName,
//       'countryCode': countryCode,
//     };
//   }
// }
//
// /// Model for geolocation hotel search data
// class GeoLocationHotelSearchData {
//   SearchAreaInfo? searchArea;
//   List<GeoLocationHotel>? hotels;
//   int? totalResults;
//   GeoLocationHotel? nearestHotel;
//
//   GeoLocationHotelSearchData({
//     this.searchArea,
//     this.hotels,
//     this.totalResults,
//     this.nearestHotel,
//   });
//
//   factory GeoLocationHotelSearchData.fromJson(Map<String, dynamic> json) {
//     List<GeoLocationHotel>? hotelsList;
//     if (json['hotels'] != null) {
//       hotelsList = (json['hotels'] as List)
//           .map((hotel) => GeoLocationHotel.fromJson(hotel))
//           .toList();
//     }
//
//     return GeoLocationHotelSearchData(
//       searchArea: json['searchArea'] != null
//           ? SearchAreaInfo.fromJson(json['searchArea'])
//           : null,
//       hotels: hotelsList,
//       totalResults: json['totalResults'],
//       nearestHotel: json['nearestHotel'] != null
//           ? GeoLocationHotel.fromJson(json['nearestHotel'])
//           : null,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'searchArea': searchArea?.toJson(),
//       'hotels': hotels?.map((hotel) => hotel.toJson()).toList(),
//       'totalResults': totalResults,
//       'nearestHotel': nearestHotel?.toJson(),
//     };
//   }
// }
//
// /// Response model for geolocation-based hotel search
// class GeoLocationHotelSearchResponse extends BaseMappable {
//   bool? success;
//   GeoLocationHotelSearchData? data;
//
//   GeoLocationHotelSearchResponse({
//     this.success,
//     this.data,
//   });
//
//   @override
//   Mappable fromJson(dynamic json) {
//     return GeoLocationHotelSearchResponse(
//       success: json['success'],
//       data: json['data'] != null
//           ? GeoLocationHotelSearchData.fromJson(json['data'])
//           : null,
//     );
//   }
//
//   @override
//   Map<String, dynamic> toJson() {
//     return {
//       'success': success,
//       'data': data?.toJson(),
//     };
//   }
// }
