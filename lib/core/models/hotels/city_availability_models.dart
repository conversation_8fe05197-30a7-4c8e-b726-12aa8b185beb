// import 'package:fandooq/core/network_provider/networking.dart';
//
// /// Legacy request parameters for city-based hotel availability search (deprecated)
// /// Use GeoLocationHotelSearchRequest from geolocation_hotel_search_models.dart instead
// class CityAvailabilityRequest {
//   double? latitude;
//   double? longitude;
//   String? checkIn;
//   String? checkOut;
//   int? adults;
//   int? children;
//   int? rooms;
//   String? currency;
//
//   CityAvailabilityRequest({
//     this.latitude,
//     this.longitude,
//     this.checkIn,
//     this.checkOut,
//     this.adults = 2,
//     this.children = 0,
//     this.rooms = 1,
//     this.currency = 'USD',
//   });
//
//   Map<String, dynamic> toQueryParams() {
//     final Map<String, dynamic> params = {};
//
//     if (latitude != null) params['latitude'] = latitude.toString();
//     if (longitude != null) params['longitude'] = longitude.toString();
//     if (checkIn != null) params['checkIn'] = checkIn;
//     if (checkOut != null) params['checkOut'] = checkOut;
//     if (adults != null) params['adults'] = adults.toString();
//     if (children != null) params['children'] = children.toString();
//     if (rooms != null) params['rooms'] = rooms.toString();
//     if (currency != null) params['currency'] = currency;
//
//     return params;
//   }
//
//   CityAvailabilityRequest copyWith({
//     double? latitude,
//     double? longitude,
//     String? checkIn,
//     String? checkOut,
//     int? adults,
//     int? children,
//     int? rooms,
//     String? currency,
//   }) {
//     return CityAvailabilityRequest(
//       latitude: latitude ?? this.latitude,
//       longitude: longitude ?? this.longitude,
//       checkIn: checkIn ?? this.checkIn,
//       checkOut: checkOut ?? this.checkOut,
//       adults: adults ?? this.adults,
//       children: children ?? this.children,
//       rooms: rooms ?? this.rooms,
//       currency: currency ?? this.currency,
//     );
//   }
// }
//
// /// Model for nearest hotel information
// class NearestHotel {
//   String? hotelCode;
//   String? hotelName;
//   double? distance;
//
//   NearestHotel({
//     this.hotelCode,
//     this.hotelName,
//     this.distance,
//   });
//
//   factory NearestHotel.fromJson(Map<String, dynamic> json) {
//     return NearestHotel(
//       hotelCode: json['hotelCode'],
//       hotelName: json['hotelName'],
//       distance: json['distance']?.toDouble(),
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'hotelCode': hotelCode,
//       'hotelName': hotelName,
//       'distance': distance,
//     };
//   }
// }
//
// /// Model for city information
// class CityInfo {
//   String? cityCode;
//   String? cityName;
//   String? countryCode;
//
//   CityInfo({
//     this.cityCode,
//     this.cityName,
//     this.countryCode,
//   });
//
//   factory CityInfo.fromJson(Map<String, dynamic> json) {
//     return CityInfo(
//       cityCode: json['cityCode'],
//       cityName: json['cityName'],
//       countryCode: json['countryCode'],
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'cityCode': cityCode,
//       'cityName': cityName,
//       'countryCode': countryCode,
//     };
//   }
// }
//
// /// Model for room availability
// class RoomAvailability {
//   String? roomType;
//   double? totalFare;
//   String? currency;
//   String? bookingCode;
//   String? cancellationPolicy;
//
//   RoomAvailability({
//     this.roomType,
//     this.totalFare,
//     this.currency,
//     this.bookingCode,
//     this.cancellationPolicy,
//   });
//
//   factory RoomAvailability.fromJson(Map<String, dynamic> json) {
//     return RoomAvailability(
//       roomType: json['roomType'],
//       totalFare: json['totalFare']?.toDouble(),
//       currency: json['currency'],
//       bookingCode: json['bookingCode'],
//       cancellationPolicy: json['cancellationPolicy'],
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'roomType': roomType,
//       'totalFare': totalFare,
//       'currency': currency,
//       'bookingCode': bookingCode,
//       'cancellationPolicy': cancellationPolicy,
//     };
//   }
// }
//
// /// Model for hotel availability
// class HotelAvailability {
//   String? hotelCode;
//   String? hotelName;
//   int? starRating;
//   String? address;
//   List<RoomAvailability>? rooms;
//
//   HotelAvailability({
//     this.hotelCode,
//     this.hotelName,
//     this.starRating,
//     this.address,
//     this.rooms,
//   });
//
//   factory HotelAvailability.fromJson(Map<String, dynamic> json) {
//     List<RoomAvailability>? roomsList;
//     if (json['rooms'] != null) {
//       roomsList = (json['rooms'] as List)
//           .map((room) => RoomAvailability.fromJson(room))
//           .toList();
//     }
//
//     return HotelAvailability(
//       hotelCode: json['hotelCode'],
//       hotelName: json['hotelName'],
//       starRating: json['starRating'],
//       address: json['address'],
//       rooms: roomsList,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'hotelCode': hotelCode,
//       'hotelName': hotelName,
//       'starRating': starRating,
//       'address': address,
//       'rooms': rooms?.map((room) => room.toJson()).toList(),
//     };
//   }
// }
//
// /// Model for city availability data
// class CityAvailabilityData {
//   NearestHotel? nearestHotel;
//   CityInfo? cityInfo;
//   List<HotelAvailability>? availability;
//
//   CityAvailabilityData({
//     this.nearestHotel,
//     this.cityInfo,
//     this.availability,
//   });
//
//   factory CityAvailabilityData.fromJson(Map<String, dynamic> json) {
//     List<HotelAvailability>? availabilityList;
//     if (json['availability'] != null) {
//       availabilityList = (json['availability'] as List)
//           .map((hotel) => HotelAvailability.fromJson(hotel))
//           .toList();
//     }
//
//     return CityAvailabilityData(
//       nearestHotel: json['nearestHotel'] != null
//           ? NearestHotel.fromJson(json['nearestHotel'])
//           : null,
//       cityInfo:
//           json['cityInfo'] != null ? CityInfo.fromJson(json['cityInfo']) : null,
//       availability: availabilityList,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'nearestHotel': nearestHotel?.toJson(),
//       'cityInfo': cityInfo?.toJson(),
//       'availability': availability?.map((hotel) => hotel.toJson()).toList(),
//     };
//   }
// }
//
// /// Response model for city availability API
// class CityAvailabilityResponse extends BaseMappable {
//   bool? success;
//   CityAvailabilityData? data;
//
//   CityAvailabilityResponse({
//     this.success,
//     this.data,
//   });
//
//   @override
//   Mappable fromJson(dynamic json) {
//     return CityAvailabilityResponse(
//       success: json['success'],
//       data: json['data'] != null
//           ? CityAvailabilityData.fromJson(json['data'])
//           : null,
//     );
//   }
//
//   @override
//   Map<String, dynamic> toJson() {
//     return {
//       'success': success,
//       'data': data?.toJson(),
//     };
//   }
// }
