// import 'dart:math';
// import 'package:fandooq/core/extensions/string.dart';
// import 'package:fandooq/core/models/availability/request/geo_location_request.dart';
// import 'package:fandooq/core/utils/location.dart';
// import 'package:fandooq/feature_plus/settings/controllers/settings_controller.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
// import 'tbo_availability_result.dart';

// class HotelData {
//   final String? hotelCode;
//   final num? hotelRating;
//   final String? countryCode;
//   final String? map;
//   final String? faxNumber;
//   final String? phoneNumber;
//   final String? pinCode;
//   final String? cityId;
//   final String? checkInTime;
//   final String? checkOutTime;
//   final List<String>? images;
//   final String? hotelWebsiteUrl;
//   final String? language;
//   final String? name;
//   final String? description;
//   final String? address;
//   final String? cityName;
//   final double? mapLatitude;
//   final double? mapLongitude;
//   final String? countryName;
//   final List<String>? attractions;
//   final List<String>? hotelFacilities;
//   final TboHotelAvailability? availability;
//
//   HotelData({
//     this.hotelCode,
//     this.hotelRating,
//     this.countryCode,
//     this.map,
//     this.faxNumber,
//     this.phoneNumber,
//     this.pinCode,
//     this.cityId,
//     this.checkInTime,
//     this.checkOutTime,
//     this.images,
//     this.hotelWebsiteUrl,
//     this.language,
//     this.name,
//     this.description,
//     this.address,
//     this.cityName,
//     this.mapLatitude,
//     this.mapLongitude,
//     this.countryName,
//     this.attractions,
//     this.hotelFacilities,
//     this.availability,
//   });
//
//   factory HotelData.fromJson(dynamic json) {
//     final images = json['images'] is Iterable
//         ? (json['images'] as Iterable).map((e) => e.toString()).toList()
//         : null;
//     final attractions = json['attractions'] is Iterable
//         ? (json['attractions'] as Iterable).map((e) => e.toString()).toList()
//         : null;
//     final hotelFacilities = json['hotelFacilities'] is Iterable
//         ? (json['hotelFacilities'] as Iterable)
//             .map((e) => e.toString())
//             .toList()
//         : null;
//     return HotelData(
//       hotelCode: json['hotelCode'] ?? '',
//       hotelRating: (json['hotelRating'] ?? 0.0),
//       countryCode: json['countryCode'],
//       map: json['map'],
//       faxNumber: json['faxNumber'],
//       phoneNumber: json['phoneNumber'],
//       pinCode: json['pinCode'],
//       cityId: json['cityId'],
//       mapLatitude: json['mapLatitude'],
//       mapLongitude: json['mapLongitude'],
//       checkInTime: json['checkInTime'],
//       checkOutTime: json['checkOutTime'],
//       images: images,
//       hotelWebsiteUrl: json['hotelWebsiteUrl'],
//       language: json['language'] ?? 'en',
//       name: json['name'],
//       description: json['description'],
//       address: json['address'],
//       cityName: json['cityName'],
//       countryName: json['countryName'],
//       attractions: attractions,
//       hotelFacilities: hotelFacilities,
//       availability: json['availability'] != null
//           ? TboHotelAvailability.fromJson(json['availability'])
//           : null,
//     );
//   }
//
//   num get getStars => hotelRating ?? 0.0;
//
//   num get minRate => availability?.minRate ?? 0.0;
//   num get maxRate => availability?.maxRate ?? 0.0;
//
//   HotelData copyWith({
//     String? hotelCode,
//     num? hotelRating,
//     String? countryCode,
//     String? map,
//     String? faxNumber,
//     String? phoneNumber,
//     String? pinCode,
//     String? cityId,
//     double? mapLatitude,
//     double? mapLongitude,
//     String? checkInTime,
//     String? checkOutTime,
//     List<String>? images,
//     String? hotelWebsiteUrl,
//     String? language,
//     String? name,
//     String? description,
//     String? address,
//     String? cityName,
//     String? countryName,
//     List<String>? attractions,
//     List<String>? hotelFacilities,
//     TboHotelAvailability? availability,
//   }) {
//     return HotelData(
//       hotelCode: hotelCode ?? this.hotelCode,
//       hotelRating: hotelRating ?? this.hotelRating,
//       countryCode: countryCode ?? this.countryCode,
//       map: map ?? this.map,
//       faxNumber: faxNumber ?? this.faxNumber,
//       phoneNumber: phoneNumber ?? this.phoneNumber,
//       pinCode: pinCode ?? this.pinCode,
//       cityId: cityId ?? this.cityId,
//       mapLatitude: mapLatitude ?? this.mapLatitude,
//       mapLongitude: mapLongitude ?? this.mapLongitude,
//       checkInTime: checkInTime ?? this.checkInTime,
//       checkOutTime: checkOutTime ?? this.checkOutTime,
//       images: images ?? this.images,
//       hotelWebsiteUrl: hotelWebsiteUrl ?? this.hotelWebsiteUrl,
//       language: language ?? this.language,
//       name: name ?? this.name,
//       description: description ?? this.description,
//       address: address ?? this.address,
//       cityName: cityName ?? this.cityName,
//       countryName: countryName ?? this.countryName,
//       attractions: attractions ?? this.attractions,
//       hotelFacilities: hotelFacilities ?? this.hotelFacilities,
//       availability: availability ?? this.availability,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'hotelCode': hotelCode,
//       'hotelRating': hotelRating,
//       'countryCode': countryCode,
//       'map': map,
//       'faxNumber': faxNumber,
//       'phoneNumber': phoneNumber,
//       'pinCode': pinCode,
//       'cityId': cityId,
//       'mapLatitude': mapLatitude,
//       'mapLongitude': mapLongitude,
//       'checkInTime': checkInTime,
//       'checkOutTime': checkOutTime,
//       'images': images,
//       'hotelWebsiteUrl': hotelWebsiteUrl,
//       'language': language,
//       'name': name,
//       'description': description,
//       'address': address,
//       'cityName': cityName,
//       'countryName': countryName,
//       'attractions': attractions,
//       'hotelFacilities': hotelFacilities,
//       'availability': availability?.toJson(),
//     };
//   }
//
//
//   get containsFreeCancellation =>
//       availability?.containsFreeCancellation ?? false;
//
//   get containsBreakfastInclude => availability?.containsBreakfastInclude;
//
//   String? get accommodationTypeCode => "H";
//
//   bool get paymentDataRequired => false;
//
//   get getAddress => address ?? "";
//
//   List<String> get interestPoints => hotelFacilities ?? [];
//   List<String> get facilities => hotelFacilities ?? [];
//   List<String> get facilitiesWhereMostPopular => [];
//
//   get exclusiveDeal => 0;
//
//   String get currency {
//     try {
//       // Try to get currency from SettingsController first
//       if (Get.isRegistered<SettingsController>()) {
//         final settingsController = Get.find<SettingsController>();
//         return settingsController.appSettings.currency;
//       }
//     } catch (e) {
//       // Fallback to USD if settings controller is not available
//     }
//     return "USD";
//   }
//
//   String get code => hotelCode ?? "";
//
//   String? get customHash1 {
//     if (availability != null) {
//       return "$code Avail";
//     }
//     return "$code Content";
//   }
//
//   String get accommodation => "Hotel";
//
//   bool get isHotel => true;
//
//   String get email => "";
//
//   bool get isContainNonRefund => false;
//
//   DateTime getCheckInTime(DateTime checkInDay) {
//     if (checkInTime == null) return checkInDay;
//     return parseHotelTime(checkInTime!, checkInDay);
//   }
//
//   DateTime getCheckOutTime(DateTime checkOutDay) {
//     if (checkOutTime == null) return checkOutDay;
//     return parseHotelTime(checkOutTime!, checkOutDay);
//   }
//
//   DateTime parseHotelTime(String time, DateTime baseDate) {
//     try {
//       DateTime parsed;
//       if (time.contains('AM') || time.contains('PM')) {
//         parsed = DateFormat.jm().parse(time); // "2:00 PM"
//       } else {
//         parsed = DateFormat.Hm().parse(time); // "14:00"
//       }
//       return DateTime(
//         baseDate.year,
//         baseDate.month,
//         baseDate.day,
//         parsed.hour,
//         parsed.minute,
//       );
//     } catch (e) {
//       print('❌ Invalid time format: $time');
//       return baseDate;
//     }
//   }
//
//   double get latitude => mapLatitude!;
//
//   double get longitude => mapLongitude!;
//
//   LatLng get latLng {
//     return LatLng(mapLatitude!, mapLongitude!);
//   }
//
//   LatLng toLatLng() {
//     return LatLng(mapLatitude!, mapLongitude!);
//   }
//
//
// }
