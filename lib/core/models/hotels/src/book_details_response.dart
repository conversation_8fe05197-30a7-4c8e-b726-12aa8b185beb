//
//
// import 'tbo_availability_result.dart';
//
// class BookingResponseData {
//   final String? bookingStatus;
//   final bool? voucherStatus;
//   final String? confirmationNumber;
//   final String? invoiceNumber;
//   final DateTime? checkIn;
//   final DateTime? checkOut;
//   final DateTime? bookingDate;
//   final num? noOfRooms;
//   final HotelDetails? hotelDetails;
//   final List<Room>? rooms;
//   final List<String>? rateConditions;
//
//   BookingResponseData({
//     this.bookingStatus,
//     this.voucherStatus,
//     this.confirmationNumber,
//     this.invoiceNumber,
//     this.checkIn,
//     this.checkOut,
//     this.bookingDate,
//     this.noOfRooms,
//     this.hotelDetails,
//     this.rooms,
//     this.rateConditions,
//   });
//
//   factory BookingResponseData.fromJson(Map<String, dynamic> json) {
//     List<NativeRoomsRoom> nativeRoomsRoom = ((json['Rooms'] ?? []) as List).map((e)=> NativeRoomsRoom.fromJson(e)).toList();
//     var rooms = NativeRoomsRoom.groupRooms(nativeRoomsRoom);
//     return BookingResponseData(
//       bookingStatus: json['BookingStatus'],
//       voucherStatus: json['VoucherStatus'],
//       confirmationNumber: json['ConfirmationNumber'],
//       invoiceNumber: json['InvoiceNumber'],
//       checkIn: DateTime.tryParse(json['CheckIn'] ?? ''),
//       checkOut: DateTime.tryParse(json['CheckOut'] ?? ''),
//       bookingDate: DateTime.tryParse(json['BookingDate'] ?? ''),
//       noOfRooms: json['NoOfRooms'],
//       hotelDetails: json['HotelDetails'] != null ? HotelDetails.fromJson(json['HotelDetails']) : null,
//       rooms: rooms,
//       rateConditions: (json['RateConditions'] as List?)?.map((e) => e.toString()).toList(),
//     );
//   }
// }
//
// class HotelDetails {
//   final String? hotelName;
//   final String? rating;
//   final String? addressLine1;
//   final String? map;
//   final String? city;
//
//   HotelDetails({this.hotelName, this.rating, this.addressLine1, this.map, this.city});
//
//   factory HotelDetails.fromJson(Map<String, dynamic> json) {
//     return HotelDetails(
//       hotelName: json['HotelName'],
//       rating: json['Rating'],
//       addressLine1: json['AddressLine1'],
//       map: json['Map'],
//       city: json['City'],
//     );
//   }
// }
//
//
//
//
// class CustomerDetail {
//   final List<CustomerName>? customerNames;
//
//   CustomerDetail({this.customerNames});
//
//   factory CustomerDetail.fromJson(Map<String, dynamic> json) {
//     return CustomerDetail(
//       customerNames: (json['CustomerNames'] as List?)?.map((e) => CustomerName.fromJson(e)).toList(),
//     );
//   }
// }
//
// class CustomerName {
//   final String? title;
//   final String? firstName;
//   final String? lastName;
//   final String? type;
//
//   CustomerName({this.title, this.firstName, this.lastName, this.type});
//
//   factory CustomerName.fromJson(Map<String, dynamic> json) {
//     return CustomerName(
//       title: json['Title'],
//       firstName: json['FirstName'],
//       lastName: json['LastName'],
//       type: json['Type'],
//     );
//   }
// }
//
