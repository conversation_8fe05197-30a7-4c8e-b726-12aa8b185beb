

import 'package:fandooq/core/models/hotels/hotels.dart';
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';

class HotelsSearchResponse extends BaseMappable{

  final List<HotelData> hotelList;

  final int? total;

  HotelsSearchResponse({
    this.hotelList = const [],
    this.total = 0,
  });

  factory HotelsSearchResponse.fromJson(dynamic json) {
    return _fromJson(json);
  }

  static HotelsSearchResponse _fromJson(dynamic json){
    var hotelsJson = json['data'];
    if(hotelsJson == null){
      return HotelsSearchResponse(hotelList: []);
    }
    return HotelsSearchResponse(
        hotelList: json['data'] != null ? (json['data'] as List).map((e) => HotelData.fromJson(e)).toList() : [],
        total: json['total'],
    );
  }

  static HotelsSearchResponse fromHotelBeds(dynamic json){
    var hotelsJson = json['data'];
    if(hotelsJson == null){
      return HotelsSearchResponse(hotelList: []);
    }
    return HotelsSearchResponse(
        hotelList: json["data"] != null ? (json["data"] as List).map((e) => HotelData.fromJson(e)).toList() : [],
        total: json["hotels"]['total']
    );
  }

  @override
  HotelsSearchResponse fromJson(dynamic json) {
    return _fromJson(json);
  }

  @override
  Map<String, dynamic> toJson({num commission = 0}) => {
    'hotels': hotelList.map((e) => e.toJson()).toList(),
    'total': total,
  };


  HotelsSearchResponse copyWith({
    List<HotelData>? hotelList,
    String? checkIn,
    int? total,
    String? checkOut,
  }) {
    return HotelsSearchResponse(
      hotelList: hotelList ?? this.hotelList,
      total: total ?? this.total,
    );
  }


}