//
//
// class BookingDetailBody {
//   final String? bookingReferenceId;
//   final String? paymentMode;
//
//   BookingDetailBody({this.bookingReferenceId, this.paymentMode});
//
//   factory BookingDetailBody.fromJson(Map<String, dynamic> json) =>
//       BookingDetailBody(
//         bookingReferenceId: json['BookingReferenceId'],
//         paymentMode: json['PaymentMode'],
//       );
//
//   Map<String, dynamic> toJson() => {
//     "BookingReferenceId": bookingReferenceId,
//     "PaymentMode": paymentMode,
//   };
// }
