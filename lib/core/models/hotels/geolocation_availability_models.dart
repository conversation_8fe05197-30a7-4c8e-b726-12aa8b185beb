// import 'package:fandooq/core/models/availability/request/hotel_request.dart';
// import 'package:fandooq/core/network_provider/networking.dart';
// import 'package:fandooq/feature_plus/settings/controllers/settings_controller.dart';
// import 'package:fandooq/core/controllers/settings/settings_app_controller.dart';
// import 'package:get/get.dart';
// import 'package:flutter/foundation.dart';
//
// /// Request model for geolocation availability search
// class GeolocationAvailabilityRequest {
//   final double latitude;
//   final double longitude;
//   final String checkIn;
//   final String checkOut;
//   final int adults;
//   final int children;
//   final int rooms;
//   final String currency;
//   final double? radiusKm;
//   final int? maxResults;
//   final String? guestNationality;
//
//   GeolocationAvailabilityRequest({
//     required this.latitude,
//     required this.longitude,
//     required this.checkIn,
//     required this.checkOut,
//     required this.adults,
//     required this.children,
//     required this.rooms,
//     required this.currency,
//     this.radiusKm,
//     this.maxResults,
//     this.guestNationality,
//   });
//
//   Map<String, dynamic> toJson() {
//     return {
//       'latitude': latitude,
//       'longitude': longitude,
//       'checkIn': checkIn,
//       'checkOut': checkOut,
//       'adults': adults,
//       'children': children,
//       'rooms': rooms,
//       'currency': currency,
//       if (radiusKm != null) 'radiusKm': radiusKm,
//       if (maxResults != null)
//         'limit': maxResults, // API uses 'limit' not 'maxResults'
//       if (guestNationality != null) 'guestNationality': guestNationality,
//     };
//   }
//
//   /// Convert to query parameters for GET request
//   Map<String, String> toQueryParams() {
//     final params = <String, String>{};
//
//     params['latitude'] = latitude.toString();
//     params['longitude'] = longitude.toString();
//     params['checkIn'] = checkIn;
//     params['checkOut'] = checkOut;
//     params['adults'] = adults.toString();
//     params['children'] = children.toString();
//     params['rooms'] = rooms.toString();
//     params['currency'] = currency;
//
//     if (maxResults != null) {
//       params['limit'] = maxResults.toString();
//     }
//
//     // Remove radiusKm and guestNationality as they're not in the API spec
//
//     return params;
//   }
//
//   factory GeolocationAvailabilityRequest.fromJson(Map<String, dynamic> json) {
//     return GeolocationAvailabilityRequest(
//       latitude: json['latitude']?.toDouble() ?? 0.0,
//       longitude: json['longitude']?.toDouble() ?? 0.0,
//       checkIn: json['checkIn'] ?? '',
//       checkOut: json['checkOut'] ?? '',
//       adults: json['adults'] ?? 2,
//       children: json['children'] ?? 0,
//       rooms: json['rooms'] ?? 1,
//       currency: json['currency'] ?? 'USD',
//       radiusKm: json['radiusKm']?.toDouble(),
//       maxResults: json['maxResults'],
//       guestNationality: json['guestNationality'],
//     );
//   }
//
//   /// Get currency from settings or default to USD
//   static String _getCurrencyFromSettings() {
//     try {
//       // Method 1: Try to get currency from SettingsController (feature_plus) - PRIORITY
//       if (Get.isRegistered<SettingsController>()) {
//         final settingsController = Get.find<SettingsController>();
//         final currency = settingsController.appSettings.currency;
//         if (currency.isNotEmpty) {
//           if (kDebugMode) {
//             print(
//                 '💱 ✅ Got currency from SettingsController (feature_plus): $currency');
//           }
//           return currency;
//         }
//       }
//
//       // Method 2: Try to get currency from SettingsAppController (fallback)
//       if (Get.isRegistered<SettingsAppController>()) {
//         final settingsAppController = Get.find<SettingsAppController>();
//         final currency = settingsAppController.currency?.currency;
//         if (currency != null && currency.isNotEmpty) {
//           if (kDebugMode) {
//             print(
//                 '💱 ✅ Got currency from SettingsAppController (fallback): $currency');
//           }
//           return currency;
//         }
//       }
//     } catch (e) {
//       if (kDebugMode) {
//         print('💱 ⚠️ Error getting currency from settings: $e');
//       }
//     }
//
//     if (kDebugMode) {
//       print('💱 ⚠️ Using default currency: USD');
//     }
//     return 'USD';
//   }
//
//   /// Create from HotelRequest (existing search data)
//   factory GeolocationAvailabilityRequest.fromHotelRequest(
//       HotelRequest hotelRequest) {
//     // Extract data from HotelRequest
//     final geolocation = hotelRequest.geolocation;
//     final stay = hotelRequest.stay;
//     final rooms = hotelRequest.rooms;
//
//     // Calculate totals from rooms
//     var totalAdults = 0;
//     var totalChildren = 0;
//
//     for (var room in rooms) {
//       // room.adults is num, convert to int safely
//       final adults = room.adults.length;
//       totalAdults += adults;
//
//       // room.children is a List<GuestDataRequest>
//       final childrenCount = room.children.length;
//       totalChildren += childrenCount;
//     }
//
//     // Get currency from settings
//     final currency = _getCurrencyFromSettings();
//
//     return GeolocationAvailabilityRequest(
//       latitude: geolocation?.latitude ?? 21.4925, // Default to Jeddah
//       longitude: geolocation?.longitude ?? 39.1776, // Default to Jeddah
//       checkIn: stay?.checkIn?.toString() ??
//           DateTime.now()
//               .add(const Duration(days: 2))
//               .toIso8601String()
//               .split('T')[0],
//       checkOut: stay?.checkOut?.toString() ??
//           DateTime.now()
//               .add(const Duration(days: 4))
//               .toIso8601String()
//               .split('T')[0],
//       adults: totalAdults > 0 ? totalAdults : 2,
//       children: totalChildren,
//       rooms: rooms.isNotEmpty == true ? rooms.length : 1,
//       currency: currency, // Get from settings
//       radiusKm: null, // Not used in API
//       maxResults: 100, // Default limit
//       guestNationality: null, // Not used in API
//     );
//   }
// }
//
// /// City information model
// class CityInfo {
//   final String? id;
//   final String? name;
//   final String? code;
//
//   CityInfo({
//     this.id,
//     this.name,
//     this.code,
//   });
//
//   factory CityInfo.fromJson(Map<String, dynamic> json) {
//     return CityInfo(
//       id: json['id'],
//       name: json['name'],
//       code: json['code'],
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'code': code,
//     };
//   }
// }
//
// /// Pricing information model
// class PricingInfo {
//   final String? originalCurrency;
//   final double? originalPrice;
//   final double? originalTax;
//   final double? convertedPrice;
//   final double? convertedTax;
//   final String? currency;
//   final double? exchangeRate;
//   final double? markupPercentage;
//   final String? conversionNote;
//
//   PricingInfo({
//     this.originalCurrency,
//     this.originalPrice,
//     this.originalTax,
//     this.convertedPrice,
//     this.convertedTax,
//     this.currency,
//     this.exchangeRate,
//     this.markupPercentage,
//     this.conversionNote,
//   });
//
//   factory PricingInfo.fromJson(Map<String, dynamic> json) {
//     return PricingInfo(
//       originalCurrency: json['originalCurrency'],
//       originalPrice: json['originalPrice']?.toDouble(),
//       originalTax: json['originalTax']?.toDouble(),
//       convertedPrice: json['convertedPrice']?.toDouble(),
//       convertedTax: json['convertedTax']?.toDouble(),
//       currency: json['currency'],
//       exchangeRate: json['exchangeRate']?.toDouble(),
//       markupPercentage: json['markupPercentage']?.toDouble(),
//       conversionNote: json['conversionNote'],
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'originalCurrency': originalCurrency,
//       'originalPrice': originalPrice,
//       'originalTax': originalTax,
//       'convertedPrice': convertedPrice,
//       'convertedTax': convertedTax,
//       'currency': currency,
//       'exchangeRate': exchangeRate,
//       'markupPercentage': markupPercentage,
//       'conversionNote': conversionNote,
//     };
//   }
// }
//
// /// Daily rate model
// class DailyRate {
//   final double? basePrice;
//   final double? originalBasePrice;
//   final double? taxes;
//   final String? currency;
//
//   DailyRate({
//     this.basePrice,
//     this.originalBasePrice,
//     this.taxes,
//     this.currency,
//   });
//
//   factory DailyRate.fromJson(Map<String, dynamic> json) {
//     return DailyRate(
//       basePrice: json['basePrice']?.toDouble(),
//       originalBasePrice: json['originalBasePrice']?.toDouble(),
//       taxes: json['taxes']?.toDouble(),
//       currency: json['currency'],
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'basePrice': basePrice,
//       'originalBasePrice': originalBasePrice,
//       'taxes': taxes,
//       'currency': currency,
//     };
//   }
// }
//
// /// Cancellation policy model
// class CancellationPolicy {
//   final String? fromDate;
//   final String? chargeType;
//   final double? cancellationCharge;
//
//   CancellationPolicy({
//     this.fromDate,
//     this.chargeType,
//     this.cancellationCharge,
//   });
//
//   factory CancellationPolicy.fromJson(Map<String, dynamic> json) {
//     return CancellationPolicy(
//       fromDate: json['FromDate'],
//       chargeType: json['ChargeType'],
//       cancellationCharge: json['CancellationCharge']?.toDouble(),
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'FromDate': fromDate,
//       'ChargeType': chargeType,
//       'CancellationCharge': cancellationCharge,
//     };
//   }
// }
//
// /// Room availability model
// class RoomAvailability {
//   final String? roomType;
//   final String? bookingCode;
//   final String? inclusion;
//   final String? mealType;
//   final double? totalFare;
//   final double? totalTax;
//   final double? originalPrice;
//   final double? originalTax;
//   final double? markupPercentage;
//   final List<DailyRate>? dailyRates;
//   final bool? isRefundable;
//   final bool? withTransfers;
//   final List<CancellationPolicy>? cancellationPolicies;
//   final List<String>? roomPromotions;
//   final PricingInfo? pricing;
//
//   RoomAvailability({
//     this.roomType,
//     this.bookingCode,
//     this.inclusion,
//     this.mealType,
//     this.totalFare,
//     this.totalTax,
//     this.originalPrice,
//     this.originalTax,
//     this.markupPercentage,
//     this.dailyRates,
//     this.isRefundable,
//     this.withTransfers,
//     this.cancellationPolicies,
//     this.roomPromotions,
//     this.pricing,
//   });
//
//   factory RoomAvailability.fromJson(Map<String, dynamic> json) {
//     return RoomAvailability(
//       roomType: json['roomType'],
//       bookingCode: json['bookingCode'],
//       inclusion: json['inclusion'],
//       mealType: json['mealType'],
//       totalFare: json['totalFare']?.toDouble(),
//       totalTax: json['totalTax']?.toDouble(),
//       originalPrice: json['originalPrice']?.toDouble(),
//       originalTax: json['originalTax']?.toDouble(),
//       markupPercentage: json['markupPercentage']?.toDouble(),
//       dailyRates: json['dailyRates'] != null
//           ? (json['dailyRates'] as List)
//               .map((rate) => DailyRate.fromJson(rate))
//               .toList()
//           : null,
//       isRefundable: json['isRefundable'],
//       withTransfers: json['withTransfers'],
//       cancellationPolicies: json['cancellationPolicies'] != null
//           ? (json['cancellationPolicies'] as List)
//               .map((policy) => CancellationPolicy.fromJson(policy))
//               .toList()
//           : null,
//       roomPromotions: json['roomPromotions'] != null
//           ? List<String>.from(json['roomPromotions'])
//           : null,
//       pricing: json['pricing'] != null
//           ? PricingInfo.fromJson(json['pricing'])
//           : null,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'roomType': roomType,
//       'bookingCode': bookingCode,
//       'inclusion': inclusion,
//       'mealType': mealType,
//       'totalFare': totalFare,
//       'totalTax': totalTax,
//       'originalPrice': originalPrice,
//       'originalTax': originalTax,
//       'markupPercentage': markupPercentage,
//       'dailyRates': dailyRates?.map((rate) => rate.toJson()).toList(),
//       'isRefundable': isRefundable,
//       'withTransfers': withTransfers,
//       'cancellationPolicies':
//           cancellationPolicies?.map((policy) => policy.toJson()).toList(),
//       'roomPromotions': roomPromotions,
//       'pricing': pricing?.toJson(),
//     };
//   }
// }
//
// /// Hotel availability model
// class HotelAvailability {
//   final int? totalRooms;
//   final List<RoomAvailability>? rooms;
//
//   HotelAvailability({
//     this.totalRooms,
//     this.rooms,
//   });
//
//   factory HotelAvailability.fromJson(Map<String, dynamic> json) {
//     return HotelAvailability(
//       totalRooms: json['totalRooms'],
//       rooms: json['rooms'] != null
//           ? (json['rooms'] as List)
//               .map((room) => RoomAvailability.fromJson(room))
//               .toList()
//           : null,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'totalRooms': totalRooms,
//       'rooms': rooms?.map((room) => room.toJson()).toList(),
//     };
//   }
// }
//
// /// Hotel information model
// class HotelInfo {
//   final String? code;
//   final String? name;
//   final String? description;
//   final int? starRating;
//   final String? address;
//   final String? city;
//   final String? country;
//   final String? currency;
//   final List<String>? images;
//   final List<String>? amenities;
//   final List<String>? facilities;
//
//   HotelInfo({
//     this.code,
//     this.name,
//     this.description,
//     this.starRating,
//     this.address,
//     this.city,
//     this.country,
//     this.currency,
//     this.images,
//     this.amenities,
//     this.facilities,
//   });
//
//   factory HotelInfo.fromJson(Map<String, dynamic> json) {
//     return HotelInfo(
//       code: json['code'],
//       name: json['name'],
//       description: json['description'],
//       starRating: json['starRating'],
//       address: json['address'],
//       city: json['city'],
//       country: json['country'],
//       currency: json['currency'],
//       images: json['images'] != null ? List<String>.from(json['images']) : null,
//       amenities: json['amenities'] != null
//           ? List<String>.from(json['amenities'])
//           : null,
//       facilities: json['facilities'] != null
//           ? List<String>.from(json['facilities'])
//           : null,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'code': code,
//       'name': name,
//       'description': description,
//       'starRating': starRating,
//       'address': address,
//       'city': city,
//       'country': country,
//       'currency': currency,
//       'images': images,
//       'amenities': amenities,
//       'facilities': facilities,
//     };
//   }
// }
//
// /// Complete hotel with availability model
// class GeolocationHotelResult {
//   final HotelInfo? hotel;
//   final HotelAvailability? availability;
//   final PricingInfo? pricing;
//
//   GeolocationHotelResult({
//     this.hotel,
//     this.availability,
//     this.pricing,
//   });
//
//   factory GeolocationHotelResult.fromJson(Map<String, dynamic> json) {
//     return GeolocationHotelResult(
//       hotel: json['hotel'] != null ? HotelInfo.fromJson(json['hotel']) : null,
//       availability: json['availability'] != null
//           ? HotelAvailability.fromJson(json['availability'])
//           : null,
//       pricing: json['pricing'] != null
//           ? PricingInfo.fromJson(json['pricing'])
//           : null,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'hotel': hotel?.toJson(),
//       'availability': availability?.toJson(),
//       'pricing': pricing?.toJson(),
//     };
//   }
//
//   /// Convert to HotelData for compatibility with existing UI
//   Map<String, dynamic> toHotelData() {
//     return {
//       'hotelCode': hotel?.code ?? '',
//       'name': hotel?.name ?? '',
//       'description': hotel?.description ?? '',
//       'hotelRating': hotel?.starRating ?? 0,
//       'address': hotel?.address ?? '',
//       'cityName': hotel?.city ?? '',
//       'countryName': hotel?.country ?? '',
//       'countryCode': hotel?.country ?? '',
//       'mapLatitude': 0.0, // Will be set from geolocation if available
//       'mapLongitude': 0.0, // Will be set from geolocation if available
//       'images': hotel?.images ?? [],
//       'hotelFacilities': hotel?.facilities ?? [],
//       'attractions': hotel?.amenities ?? [],
//       'language': 'en',
//       'currency': hotel?.currency ?? 'USD',
//       'checkInTime': null,
//       'checkOutTime': null,
//       'hotelWebsiteUrl': null,
//       'faxNumber': null,
//       'phoneNumber': null,
//       'pinCode': null,
//       'cityId': null,
//       'map': null,
//       // Convert availability to TboHotelAvailability format
//       'availability': _convertAvailabilityToTbo(),
//     };
//   }
//
//   /// Convert GeolocationAvailability to TboHotelAvailability format
//   Map<String, dynamic>? _convertAvailabilityToTbo() {
//     if (availability?.rooms == null || availability!.rooms!.isEmpty) {
//       return null;
//     }
//
//     final rooms = availability!.rooms!;
//     final minPrice =
//         rooms.map((r) => r.totalFare ?? 0.0).reduce((a, b) => a < b ? a : b);
//     final maxPrice =
//         rooms.map((r) => r.totalFare ?? 0.0).reduce((a, b) => a > b ? a : b);
//
//     return {
//       'minRate': minPrice,
//       'maxRate': maxPrice,
//       'totalRooms': availability?.totalRooms ?? rooms.length,
//       'rooms': rooms
//           .map((room) => {
//                 'roomType': room.roomType ?? '',
//                 'bookingCode': room.bookingCode ?? '',
//                 'inclusion': room.inclusion ?? '',
//                 'mealType': room.mealType ?? '',
//                 // Create rates array that the filter expects
//                 'Rates': [
//                   {
//                     'TotalFare': room.totalFare ?? 0.0,
//                     'TotalTax': room.totalTax ?? 0.0,
//                     'isRefundable': room.isRefundable ?? false,
//                     'mealType': room.mealType ?? '',
//                     'Inclusion': room.inclusion ?? '',
//                     'CancelPolicies': room.cancellationPolicies
//                             ?.map((policy) => {
//                                   'FromDate': policy.fromDate,
//                                   'ChargeType': policy.chargeType,
//                                   'CancellationCharge':
//                                       policy.cancellationCharge,
//                                 })
//                             .toList() ??
//                         [],
//                     'BookingCode': room.bookingCode ?? '',
//                     'roomId': 0, // pageId
//                   }
//                 ],
//               })
//           .toList(),
//     };
//   }
// }
//
// /// Hotels data container
// class HotelsData {
//   final bool? success;
//   final int? totalRequested;
//   final int? totalAvailable;
//   final List<GeolocationHotelResult>? hotels;
//   final Map<String, dynamic>? debug;
//
//   HotelsData({
//     this.success,
//     this.totalRequested,
//     this.totalAvailable,
//     this.hotels,
//     this.debug,
//   });
//
//   factory HotelsData.fromJson(Map<String, dynamic> json) {
//     return HotelsData(
//       success: json['success'],
//       totalRequested: json['totalRequested'],
//       totalAvailable: json['totalAvailable'],
//       hotels: json['hotels'] != null
//           ? (json['hotels'] as List)
//               .map((hotel) => GeolocationHotelResult.fromJson(hotel))
//               .toList()
//           : null,
//       debug: json['debug'],
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'success': success,
//       'totalRequested': totalRequested,
//       'totalAvailable': totalAvailable,
//       'hotels': hotels?.map((hotel) => hotel.toJson()).toList(),
//       'debug': debug,
//     };
//   }
// }
//
// /// Main response data model
// class GeolocationAvailabilityData {
//   final bool? success;
//   final CityInfo? cityInfo;
//   final HotelsData? hotels;
//
//   GeolocationAvailabilityData({
//     this.success,
//     this.cityInfo,
//     this.hotels,
//   });
//
//   factory GeolocationAvailabilityData.fromJson(Map<String, dynamic> json) {
//     return GeolocationAvailabilityData(
//       success: json['success'],
//       cityInfo:
//           json['cityInfo'] != null ? CityInfo.fromJson(json['cityInfo']) : null,
//       hotels:
//           json['hotels'] != null ? HotelsData.fromJson(json['hotels']) : null,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'success': success,
//       'cityInfo': cityInfo?.toJson(),
//       'hotels': hotels?.toJson(),
//     };
//   }
// }
//
// /// Complete API response model
// class GeolocationAvailabilityResponse extends BaseMappable {
//   final bool? success;
//   final String? message;
//   final GeolocationAvailabilityData? data;
//   final Map<String, dynamic>? meta;
//
//   GeolocationAvailabilityResponse({
//     this.success,
//     this.message,
//     this.data,
//     this.meta,
//   });
//
//   factory GeolocationAvailabilityResponse.fromJson(Map<String, dynamic> json) {
//     return GeolocationAvailabilityResponse(
//       success: json['success'] ?? false,
//       message: json['message'] ?? '',
//       data: json['data'] != null
//           ? GeolocationAvailabilityData.fromJson(json['data'])
//           : null,
//       meta: json['meta'],
//     );
//   }
//
//   @override
//   Mappable fromJson(dynamic json) {
//     return GeolocationAvailabilityResponse.fromJson(json);
//   }
//
//   @override
//   Map<String, dynamic> toJson() {
//     return {
//       'success': success,
//       'message': message,
//       'data': data?.toJson(),
//       'meta': meta,
//     };
//   }
// }
