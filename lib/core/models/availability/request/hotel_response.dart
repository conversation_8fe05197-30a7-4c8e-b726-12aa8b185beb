
import 'package:fandooq/core/models/hotels/hotels.dart';
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';


class HotelResponse extends BaseMappable{

  final HotelData? hotel;

  HotelResponse({
    this.hotel,
  });

  HotelResponse copyWith({
    HotelData? hotel,
  }) {
    return HotelResponse(
      hotel: hotel ?? this.hotel,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hotel': hotel?.toJson(),
    };
  }

  factory HotelResponse.fromJson(dynamic json){
    return _fromJson(json);
  }

  static HotelResponse _fromJson(dynamic json){
    return HotelResponse(
      hotel: json['hotel'] != null ? HotelData.fromJson(json['hotel']) : null,
    );
  }

  @override
  Mappable fromJson(dynamic json) {
    return _fromJson(json);
  }

  // Assuming that each hotel JSON has a 'code' field that serves as the unique integer key
  // static Map<int, HotelContent> _convertHotelsListToMap(List<dynamic> hotelsList) {
  //   final Map<int, HotelContent> hotelsMap = {};
  //   for (var hotelJson in hotelsList) {
  //     var hotel = HotelContent.fromJson(hotelJson);
  //     hotelsMap[hotel.code!] = hotel;  // Assuming 'code' is an integer and a field in _Hotel
  //   }
  //   return hotelsMap;
  // }

  factory HotelResponse.fromJson2(Map<String, dynamic> json) {
    return _fromJson(json);
  }

}

