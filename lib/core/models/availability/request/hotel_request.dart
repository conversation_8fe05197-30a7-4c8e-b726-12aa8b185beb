
class Destination {
  final String code;
  Destination({required this.code});
  factory Destination.fromJson(dynamic json) {
    return Destination(
      code: json['code'],
    );
  }
  Map<String, dynamic> toJson() {
    return {"code": code};
  }
}

enum FacilitySelectionEnum {
  FREE_BREAKFAST,
  FREE_PARKING,
  POOL,
  PET_FRIENDLY,
  FREE_WIFI,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>OWER,
  HA<PERSON>DRYER,
  TO<PERSON>ET<PERSON>ES,
  DIS<PERSON>ILITY_FRIENDLY_BATHROOM,
  LIVING_ROOM,
  BATHROBES,
}

enum SortedBy {
  LOWEST_PRICE,
  HIGHEST_PRICE,
  HIGHEST_RATED,
  DISTANCE_FROM_CENTER,
}




