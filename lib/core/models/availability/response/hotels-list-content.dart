

import 'package:fandooq/core/models/hotels/src/hotel_data.dart';
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';


class HotelsListResponse extends BaseMappable{

  final int? from;

  final List<HotelData>? hotels;

  final int? to;

  final int? total;

  HotelsListResponse({
    this.from = 0,
    this.hotels,
    this.to = 0,
    this.total = 0,
  });

  HotelsListResponse copyWith({
    int? from,
    List<HotelData>? hotels,
    int? to,
    int? total,
  }) {
    return HotelsListResponse(
      from: from ?? this.from,
      hotels: hotels ?? this.hotels,
      to: to ?? this.to,
      total: total ?? this.total,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'from': from,
      'hotels': (hotels ?? []).map((e) => e.toJson()).toList(),
      'to': to,
      'total': total,
    };
  }

  @override
  int get hashCode => from.hashCode ^ hotels.hashCode ^ to.hashCode ^ total.hashCode;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HotelsListResponse &&
        other.from == from &&
        other.hotels == hotels &&
        other.to == to &&
        other.total == total;
  }

  factory HotelsListResponse.fromJson(dynamic json){
    return _fromJson(json);
  }

  static HotelsListResponse _fromJson(dynamic json){
    return HotelsListResponse(
      hotels: json['hotels'] != null ? (json['hotels'] as List).map((e) => HotelData.fromJson(e)).toList() : null,
    );
  }

  @override
  Mappable fromJson(dynamic json) {
    return _fromJson(json);
  }

  // Assuming that each hotel JSON has a 'code' field that serves as the unique integer key
  // static Map<int, HotelContent> _convertHotelsListToMap(List<dynamic> hotelsList) {
  //   final Map<int, HotelContent> hotelsMap = {};
  //   for (var hotelJson in hotelsList) {
  //     var hotel = HotelContent.fromJson(hotelJson);
  //     hotelsMap[hotel.code!] = hotel;  // Assuming 'code' is an integer and a field in _Hotel
  //   }
  //   return hotelsMap;
  // }

  factory HotelsListResponse.fromJson2(Map<String, dynamic> json) {
    return _fromJson(json);
  }

}