
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/feature_plus/hotels/models/tbo_availability_result.dart' show TboHotelAvailability;


class CheckRateResponse extends BaseMappable{

  final TboHotelAvailability? availability;

  CheckRateResponse({
    this.availability,
  });

  CheckRateResponse copyWith({
    TboHotelAvailability? availability,
  }) => CheckRateResponse(
    availability: availability ?? this.availability,
  );

  Map<String, dynamic> toJson() => {
    'availability': availability,
  };

  @override
  Mappable fromJson(dynamic json) {
    return CheckRateResponse(
      availability: json['data'] != null ? TboHotelAvailability.fromJson(json['data']) : null,
    );
  }

  bool get hasData => availability != null;

}
