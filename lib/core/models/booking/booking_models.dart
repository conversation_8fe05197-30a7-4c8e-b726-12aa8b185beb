import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/feature_plus/bookings/models/booking_models_plus.dart';

/// Request model for pre-booking a hotel
class PreBookRequest {
  String? bookingCode;
  String? paymentMode;

  PreBookRequest({
    this.bookingCode,
    this.paymentMode = 'CREDIT_CARD',
  });

  Map<String, dynamic> toJson() {
    return {
      'bookingCode': bookingCode,
      'paymentMode': paymentMode,
    };
  }

  PreBookRequest copyWith({
    String? bookingCode,
    String? paymentMode,
  }) {
    return PreBookRequest(
      bookingCode: bookingCode ?? this.bookingCode,
      paymentMode: paymentMode ?? this.paymentMode,
    );
  }
}

/// Response model for pre-booking
class PreBookResponse extends BaseMappable {
  bool? success;
  PreBookData? data;

  PreBookResponse({
    this.success,
    this.data,
  });

  @override
  Mappable fromJson(dynamic json) {
    return PreBookResponse(
      success: json['success'],
      data: json['data'] != null ? PreBookData.fromJson(json['data']) : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data?.toJson(),
    };
  }
}

/// Data model for pre-booking response
class PreBookData {
  String? preBookCode;
  String? hotelName;
  String? roomType;
  double? totalFare;
  String? currency;
  String? checkIn;
  String? checkOut;
  String? expiresAt;

  PreBookData({
    this.preBookCode,
    this.hotelName,
    this.roomType,
    this.totalFare,
    this.currency,
    this.checkIn,
    this.checkOut,
    this.expiresAt,
  });

  factory PreBookData.fromJson(Map<String, dynamic> json) {
    return PreBookData(
      preBookCode: json['preBookCode'],
      hotelName: json['hotelName'],
      roomType: json['roomType'],
      totalFare: json['totalFare']?.toDouble(),
      currency: json['currency'],
      checkIn: json['checkIn'],
      checkOut: json['checkOut'],
      expiresAt: json['expiresAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'preBookCode': preBookCode,
      'hotelName': hotelName,
      'roomType': roomType,
      'totalFare': totalFare,
      'currency': currency,
      'checkIn': checkIn,
      'checkOut': checkOut,
      'expiresAt': expiresAt,
    };
  }
}

/// Request model for completing a booking
class CompleteBookingRequest {
  String? bookingCode;
  String? emailId;
  String? title;
  String? firstName;
  String? lastName;
  String? countryCode;
  String? countryName;
  String? contactNo;
  double? totalFare;
  double? originalPrice;
  String? paymentMode;
  String? guestNationality;
  String? checkIn;
  String? checkOut;

  CompleteBookingRequest({
    this.bookingCode,
    this.emailId,
    this.title,
    this.firstName,
    this.lastName,
    this.countryCode,
    this.countryName,
    this.contactNo,
    this.totalFare,
    this.originalPrice,
    this.paymentMode = 'CREDIT_CARD',
    this.guestNationality,
    this.checkIn,
    this.checkOut,
  });

  Map<String, dynamic> toJson() {
    return {
      'bookingCode': bookingCode,
      'emailId': emailId,
      'title': title,
      'firstName': firstName,
      'lastName': lastName,
      'countryCode': countryCode,
      'countryName': countryName,
      'contactNo': contactNo,
      'totalFare': totalFare,
      'originalPrice': originalPrice,
      'paymentMode': paymentMode,
      'guestNationality': guestNationality,
      'checkIn': checkIn,
      'checkOut': checkOut,
    };
  }

  CompleteBookingRequest copyWith({
    String? bookingCode,
    String? emailId,
    String? title,
    String? firstName,
    String? lastName,
    String? countryCode,
    String? countryName,
    String? contactNo,
    double? totalFare,
    double? originalPrice,
    String? paymentMode,
    String? guestNationality,
    String? checkIn,
    String? checkOut,
  }) {
    return CompleteBookingRequest(
      bookingCode: bookingCode ?? this.bookingCode,
      emailId: emailId ?? this.emailId,
      title: title ?? this.title,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      countryCode: countryCode ?? this.countryCode,
      countryName: countryName ?? this.countryName,
      contactNo: contactNo ?? this.contactNo,
      totalFare: totalFare ?? this.totalFare,
      originalPrice: originalPrice ?? this.originalPrice,
      paymentMode: paymentMode ?? this.paymentMode,
      guestNationality: guestNationality ?? this.guestNationality,
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
    );
  }
}

/// Response model for completed booking
class CompleteBookingResponse extends BaseMappable {
  bool? success;
  BookingSummaryPlus? data;

  CompleteBookingResponse({
    this.success,
    this.data,
  });

  @override
  Mappable fromJson(dynamic json) {
    return CompleteBookingResponse(
      success: json['success'],
      data: json['data'] != null
          ? BookingSummaryPlus.fromJson(json['data'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data?.toJson(),
    };
  }
}

/// Data model for completed booking response

/// Hotel information in booking
class BookingHotel {
  String? name;
  String? address;

  BookingHotel({this.name, this.address});

  factory BookingHotel.fromJson(Map<String, dynamic> json) {
    return BookingHotel(
      name: json['name'],
      address: json['address'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
    };
  }
}

/// Guest information in booking
class BookingGuest {
  String? name;
  String? email;
  String? phone;

  BookingGuest({this.name, this.email, this.phone});

  factory BookingGuest.fromJson(Map<String, dynamic> json) {
    return BookingGuest(
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
    };
  }
}

/// Booking dates information
class BookingDates {
  String? checkIn;
  String? checkOut;
  int? nights;

  BookingDates({this.checkIn, this.checkOut, this.nights});

  factory BookingDates.fromJson(Map<String, dynamic> json) {
    return BookingDates(
      checkIn: json['checkIn'],
      checkOut: json['checkOut'],
      nights: json['nights'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'checkIn': checkIn,
      'checkOut': checkOut,
      'nights': nights,
    };
  }
}

/// Request parameters for my bookings
class MyBookingsRequest {
  int? page;
  int? limit;
  String? status;

  MyBookingsRequest({
    this.page = 1,
    this.limit = 10,
    this.status = 'ALL',
  });

  Map<String, dynamic> toQueryParams() {
    final Map<String, dynamic> params = {};

    if (page != null) params['page'] = page.toString();
    if (limit != null) params['limit'] = limit.toString();
    if (status != null) params['status'] = status;

    return params;
  }
}


/// Pagination information
class BookingPagination {
  int? page;
  int? limit;
  int? total;
  int? totalPages;

  BookingPagination({
    this.page,
    this.limit,
    this.total,
    this.totalPages,
  });

  factory BookingPagination.fromJson(Map<String, dynamic> json) {
    return BookingPagination(
      page: json['page'],
      limit: json['limit'],
      total: json['total'],
      totalPages: json['totalPages'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'total': total,
      'totalPages': totalPages,
    };
  }
}

/// Response model for my bookings list
class MyBookingsResponse extends BaseMappable {
  bool? success;
  MyBookingsData? data;

  MyBookingsResponse({this.success, this.data});

  @override
  Mappable fromJson(dynamic json) {
    final rawData = json['data'];
    MyBookingsData? parsedData;

    if (rawData is List) {
      final bookings = rawData
          .whereType<Map<String, dynamic>>()
          .map(BookingSummaryPlus.fromJson)
          .toList();

      parsedData = MyBookingsData(
        bookings: bookings,
        pagination: json['pagination'] != null
            ? BookingPagination.fromJson(json['pagination'])
            : null,
      );
    } else if (rawData is Map<String, dynamic>) {
      parsedData = MyBookingsData.fromJson(rawData);
    } else {
      print('❌ Unexpected data format in MyBookingsResponse: ${rawData.runtimeType}');
    }

    return MyBookingsResponse(
      success: json['success'],
      data: parsedData,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data?.toJson(),
    };
  }
}

/// Data model for my bookings response
class MyBookingsData {
  List<BookingSummaryPlus>? bookings;
  BookingPagination? pagination;

  MyBookingsData({this.bookings, this.pagination});

  factory MyBookingsData.fromJson(Map<String, dynamic> json) {
    final rawBookings = json['bookings'];
    List<BookingSummaryPlus>? parsedBookings;

    if (rawBookings is List) {
      parsedBookings = rawBookings
          .whereType<Map<String, dynamic>>()
          .map(BookingSummaryPlus.fromJson)
          .toList();
    } else {
      print('❌ Bookings is not a list: ${rawBookings.runtimeType}');
    }

    return MyBookingsData(
      bookings: parsedBookings,
      pagination: json['pagination'] != null
          ? BookingPagination.fromJson(json['pagination'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bookings': bookings?.map((e) => e.toJson()).toList(),
      'pagination': pagination?.toJson(),
    };
  }
}

/// Request model for canceling a booking
class CancelBookingRequest {
  String? bookingReferenceId;
  String? reason;

  CancelBookingRequest({
    this.bookingReferenceId,
    this.reason,
  });

  Map<String, dynamic> toJson() {
    return {
      'bookingReferenceId': bookingReferenceId,
      'reason': reason,
    };
  }
}
