
import 'package:fandooq/core/ex/phone_number.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:fandooq/feature_plus/hotels/models/tbo_availability_result.dart';
import 'package:flutter/material.dart';

part 'src/biling-address.dart';
part 'src/card-type.dart';
part 'src/contact-data.dart';
part 'src/device-request.dart';
part 'src/extensions.dart';
part 'src/holder.dart';
part 'src/pax.dart';
part 'src/payment-card.dart';
part 'src/payment-data.dart';
part 'src/room-request-booking.dart';
part 'src/user-type.dart';
part 'src/invoice_company.dart';

class Booking{

  final Holder? holder;
  final HotelData? hotel;
  final TboHotelAvailability? bookingDetails;
  final String? id;
  final String? bookingReferenceId;
  final String? hotelCode;
  final String? userId;
  final String? clientReferenceId;
  final String? confirmationNumber;
  final String? phone;
  final String? email;
  final num? billingAmount;
  final String? billingCurrency;
  final DateTime? createdAt;


  Booking({
    required this.id,
    required this.bookingReferenceId,
    required this.holder,
    required this.hotelCode,
    this.userId,
    required this.clientReferenceId,
    required this.confirmationNumber,
    required this.phone,
    required this.email,
    required this.billingAmount,
    required this.billingCurrency,
    required this.bookingDetails,
    required this.createdAt,
    required this.hotel,
  });

  /// يعمل على نسخ الكائن مع إمكانية تعديل أي من الخصائص
  Booking copyWith({
    Holder? holder,
    HotelData? hotel,
    TboHotelAvailability? bookingDetails,
    String? id,
    String? bookingReferenceId,
    String? hotelCode,
    String? userId,
    String? clientReferenceId,
    String? confirmationNumber,
    String? phone,
    String? email,
    num? billingAmount,
    String? billingCurrency,
    DateTime? createdAt,
  }) {
    return Booking(
      id: id ?? this.id,
      bookingReferenceId: bookingReferenceId ?? this.bookingReferenceId,
      holder: holder ?? this.holder,
      hotelCode: hotelCode ?? this.hotelCode,
      userId: userId ?? this.userId,
      clientReferenceId: clientReferenceId ?? this.clientReferenceId,
      confirmationNumber: confirmationNumber ?? this.confirmationNumber,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      billingAmount: billingAmount ?? this.billingAmount,
      billingCurrency: billingCurrency ?? this.billingCurrency,
      bookingDetails: bookingDetails ?? this.bookingDetails,
      createdAt: createdAt ?? this.createdAt,
      hotel: hotel ?? this.hotel,
    );
  }

  /// تحويل JSON إلى كائن Booking
  factory Booking.fromJson(Map<String, dynamic> json) {
    return Booking(
      id: json['id'] as String,
      bookingReferenceId: json['bookingReferenceId'] as String?,
      holder: json['holder'] != null ? Holder.fromJson(json['holder']) : null,
      hotelCode: json['hotelCode'] as String?,
      userId: json['userId'] as String?,
      clientReferenceId: json['clientReferenceId'] as String?,
      confirmationNumber: json['confirmationNumber'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      billingAmount: json['billingAmount'] ?? 0,
      billingCurrency: json['billingCurrency'],
      bookingDetails: json['bookingDetails'] != null ? TboHotelAvailability.fromJson(json['bookingDetails']) : null,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      hotel: json['hotel'] != null ? HotelData.fromJson(json['hotel']) : null,
    );
  }

  /// تحويل كائن Booking إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bookingReferenceId': bookingReferenceId,
      'holder': holder?.toJson(),
      'hotelCode': hotelCode,
      'userId': userId,
      'clientReferenceId': clientReferenceId,
      'confirmationNumber': confirmationNumber,
      'phone': phone,
      'email': email,
      'billingAmount': billingAmount,
      'billingCurrency': billingCurrency,
      'bookingDetails': bookingDetails?.toJson(),
      'createdAt': createdAt?.toIso8601String(),
      'hotel': hotel?.toJson(),
    };
  }

  bool get canCancel{
    return bookingDetails!.canCancel;
  }

  String get status => bookingDetails!.bookingStatus ?? '';

  Widget get statusIcon{
    switch(status){
      case "Cancelled": return Icon(Icons.close,color: getStatusColor,size: 70,);
      case "CancellationInProgress": return Icon(Icons.loop,color: getStatusColor,size: 70,);
      case "Confirmed": return Icon(Icons.check,color: getStatusColor,size: 70,);
      case "Not Cancelled": return Icon(Icons.check,color: getStatusColor,size: 70,);
    }
    return Icon(Icons.info_outline,color: getStatusColor);
  }

  Color get getStatusColor{
    switch(status){
      case "Cancelled": return Colors.red;
      case "CancellationInProgress": return Colors.orange;
      case "Confirmed": return Colors.green;
      case "Not Cancelled": return Colors.green;
    }
    return Colors.grey;
  }

}