

import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';


extension B on Map<String,int>{

  Map<String, int> wherePopular() {
    // Filtering and converting to Map
    return Map.fromEntries(
        entries.where((element) => element.key == "H")
    );
  }

}

extension C on Map<int,int>{

  Map<int, int> wherePopular() {
    // Filtering and converting to Map
    return Map.fromEntries(
        entries.where((element) => element.key == 5)
    );
  }

}

class AvailableFilters{

  final Map<double,int> ratings;
  final Map<String,int> accommodation;
  final Map<String,int> interestPoints;
  final Map<String,int> facilities;
  final Map<String,int> meals;
  final Map<String,int> facilitiesRooms;
  final Map<String,int> pointsOfInterest;
  final Map<String,int> popularFilters;

  final List<HotelData> hotels;

  AvailableFilters({
    this.ratings = const {},
    this.accommodation = const {},
    this.interestPoints = const {},
    this.facilities = const {},
    this.meals = const {},
    this.facilitiesRooms = const {},
    this.pointsOfInterest = const {},
    this.popularFilters = const {},
    this.hotels = const [],
  });

  AvailableFilters copyWith(
      Map<double,int>? ratings,
      Map<String,int>? accommodation,
      Map<String,int>? interestPoints,
      Map<String,int>? facilities,
      Map<String,int>? meals,
      Map<String,int>? facilitiesRooms,
      Map<String,int>? pointsOfInterest,
      Map<String,int>? popularFilters,
      ){
    return AvailableFilters(
      ratings: ratings ?? this.ratings,
      accommodation: accommodation ?? this.accommodation,
      interestPoints: interestPoints ?? this.interestPoints,
      facilities: facilities ?? this.facilities,
      meals: meals ?? this.meals,
      facilitiesRooms: facilitiesRooms ?? this.facilitiesRooms,
      pointsOfInterest: pointsOfInterest ?? this.pointsOfInterest,
      popularFilters: popularFilters ?? this.popularFilters,
    );
  }

  AvailableFilters initFilters(){

    // Set<double> ratings = hotels.map((hotel) => hotel.getStarsInt).toSet();
    // Set<String> accommodations = hotels.map((hotel) => hotel.accommodationTypeCode!).toSet();
    //
    // Set<String> interestPoints = hotels.map((hotel) => hotel.interestPoints.toSet()).expand((element) => element.toSet()).toSet();
    // Set<String> facilities = hotels.map((hotel) => hotel.facilities.whereFacilities().toSet()).expand((element) => element.toSet()).toSet();
    // Set<String> facilitiesMeals = hotels.map((hotel) => hotel.facilities.whereMeals().toSet()).expand((element) => element.toSet()).toSet();
    // Set<String> facilitiesRooms = hotels.map((hotel) => hotel.facilities.whereFacilitiesRooms().toSet()).expand((element) => element.toSet()).toSet();
    // Set<String> pointsOfInterest = hotels.map((hotel) => hotel.facilities.wherePointsOfInterest().toSet()).expand((element) => element.toSet()).toSet();

    return AvailableFilters(
        // ratings: {
        //   for (double key in ratings) key : countStars(key)
        // },
        // accommodation: {
        //   for (String key in accommodations) key : hotels.where((hotel) => hotel.accommodationTypeCode == key).length
        // },
        // interestPoints: {
        //   for (String key in interestPoints) key : hotels.where((hotel) => hotel.interestPoints.contains(key)).length
        // },
        // facilities: {
        //   for (String key in facilities) key : hotels.where((hotel) => hotel.facilities.whereFacilities().contains(key)).length
        // },
        // meals: {
        //   for (String key in facilitiesMeals) key : hotels.where((hotel) => hotel.facilities.whereMeals().contains(key)).length
        // },
        // facilitiesRooms: {
        //   for (String key in facilitiesRooms) key : hotels.where((hotel) => hotel.facilities.whereFacilitiesRooms().contains(key)).length
        // },
        // pointsOfInterest: {
        //   for (String key in pointsOfInterest) key : hotels.where((hotel) => hotel.facilities.wherePointsOfInterest().contains(key)).length
        // },
        // popularFilters: {
        //   "free_cancellation": hotels.countFreeCancellation(),
        //   "breakfast_included": hotels.countIncludeBreakfast(),
        // }
    );

  }

  int countStars(double value) {
    return hotels.where((hotel) {
      // Calculate the rate based on the price option
      // Check if hotel rate is within the min and max rate inclusive.
      return hotel.getStars == value;
    }).length;
  }

}