// ...existing code...
// ...existing code...
// ...existing code...
const Map<String, String> en = {
  'payment.title': 'Payment & Booking',
  'payment.nights': 'night(s)',
  'payment.guests': 'adult(s), {children} child(ren)',
  'payment.rooms': 'room(s)',
  "payment.securePayment": "Secure Payment",
  "payment.creatingPaymentIntent": "Creating payment intent...",
  "payment.confirmingPaymentMethod": "Confirming payment method...",
  "payment.awaitingPaymentMethod": "Awaiting payment method",
  "payment.awaitingConfirmation": "Awaiting confirmation",
  "payment.requiresAdditionalAuth": "Requires additional authentication",
  "payment.processing": "Processing payment",
  "payment.capturingFunds": "Funds authorized - completing booking",
  "payment.status": "Payment status",
  "payment.cardHolderNameLabel": "Cardholder Name",
  "payment.cardHolderNameHint": "Name as shown on the card",
  "payment.applePayTitle": "Pay with Apple Pay",
  "payment.applePayDescription":
      "Use Face ID, Touch ID, or passcode for secure payment",
  "payment.payNow": "pay Now",

  // Notifications
  "notifications.title": "Notifications",
  "notifications.loading": "Loading notifications...",
  "notifications.noNotifications": "No notifications",
  "notifications.noNotificationsSubtitle":
      "Your notifications will appear here when they arrive",
  "notifications.noUnreadNotifications": "No unread notifications",
  "notifications.markAsRead": "Mark as read",
  "notifications.markAllAsRead": "Mark all as read",
  "notifications.markAllAsReadConfirmation":
      "Do you want to mark all notifications as read?",
  "notifications.delete": "Delete",
  "notifications.deleteNotification": "Delete notification",
  "notifications.deleteConfirmation":
      "Do you want to delete this notification?",
  "notifications.allMarkedAsRead": "All notifications marked as read",
  "notifications.notificationDeleted": "Notification deleted",
  "notifications.failedToMarkAsRead": "Failed to mark notification as read",
  "notifications.failedToDelete": "Failed to delete notification",
  "notifications.unexpectedError": "An unexpected error occurred",
  "notifications.success": "Success",
  "notifications.error": "Error",
  "notifications.filters": "Filters",
  "notifications.settings": "Settings",
  "notifications.showUnreadOnly": "Show unread only",
  "notifications.showUnreadOnlySubtitle": "Hide read notifications",
  "notifications.notificationType": "Notification type",
  "notifications.all": "All",
  "notifications.clearFilters": "Clear filters",
  "notifications.activeFilters": "Active filters",
  "notifications.clearAll": "Clear all",
  "notifications.unreadOnly": "Unread only",
  "notifications.loadMore": "Load more",
  "notifications.today": "Today",
  "notifications.yesterday": "Yesterday",

  // Notification Types
  "notifications.types.bookingConfirmation": "Booking Confirmation",
  "notifications.types.bookingReminder": "Booking Reminder",
  "notifications.types.specialOffer": "Special Offer",
  "notifications.types.bookingCancellation": "Booking Cancellation",
  "notifications.types.paymentConfirmation": "Payment Confirmation",
  "notifications.types.paymentFailed": "Payment Failed",
  "notifications.types.hotelReviewRequest": "Hotel Review Request",

  // Notification Priorities
  "notifications.priority.high": "High Priority",
  "notifications.priority.urgent": "Urgent",

  // Notification Categories
  "notifications.categories.booking": "Bookings",
  "notifications.categories.payment": "Payments",
  "notifications.categories.marketing": "Marketing",
  "notifications.categories.system": "System",

  // Time Ago
  "notifications.timeAgo.now": "Now",
  "notifications.timeAgo.minutes": "{minutes} minutes ago",
  "notifications.timeAgo.hours": "{hours} hours ago",
  "notifications.timeAgo.days": "{days} days ago",

  // Notification Preferences
  "notifications.preferences.title": "Notification Settings",
  "notifications.preferences.loading": "Loading settings...",
  "notifications.preferences.noPreferences": "No preferences available",
  "notifications.preferences.pushNotifications": "Push Notifications",
  "notifications.preferences.pushNotificationsSubtitle":
      "Receive instant notifications on your device",
  "notifications.preferences.emailNotifications": "Email Notifications",
  "notifications.preferences.emailNotificationsSubtitle":
      "Receive notifications via email",
  "notifications.preferences.inAppNotifications": "In-App Notifications",
  "notifications.preferences.inAppNotificationsSubtitle":
      "Show notifications within the app",
  "notifications.preferences.quietHours": "Quiet Hours",
  "notifications.preferences.quietHoursSubtitle":
      "You won't receive push notifications during these hours",
  "notifications.preferences.from": "From",
  "notifications.preferences.to": "To",
  "notifications.preferencesUpdated": "Preferences updated",
  "notifications.failedToUpdatePreferences": "Failed to update preferences",

  // Delete Account
  "deleteAccount.title": "Delete Account",
  "deleteAccount.warningTitle": "Important Warning",
  "deleteAccount.warningMessage":
      "Account deletion is irreversible. You will permanently lose all your data.",
  "deleteAccount.warningConsequences":
      "• All your bookings will be deleted\n• You will lose loyalty points\n• Data cannot be recovered",
  "deleteAccount.formTitle": "Confirm Account Deletion",
  "deleteAccount.phoneNumber": "Phone Number",
  "deleteAccount.phoneHint": "Enter your phone number",
  "deleteAccount.phoneRequired": "Phone number is required",
  "deleteAccount.otpCode": "Verification Code",
  "deleteAccount.otpHint": "Enter verification code",
  "deleteAccount.otpExpired": "Code has expired",
  "deleteAccount.otpExpiresIn": "Code expires in {seconds}",
  "deleteAccount.resendOtp": "Resend",
  "deleteAccount.reasonTitle": "Reason for deletion (optional)",
  "deleteAccount.customReasonHint": "Write your reason...",
  "deleteAccount.sendOtp": "Send Verification Code",
  "deleteAccount.confirmDelete": "Confirm Account Deletion",
  "deleteAccount.confirmTitle": "Confirm Deletion",
  "deleteAccount.confirmMessage":
      "Are you sure you want to permanently delete your account?",
  "deleteAccount.confirmWarning": "This action cannot be undone!",
  "deleteAccount.fillAllFields": "Please fill all required fields",
  "deleteAccount.success": "Success",
  "deleteAccount.error": "Error",
  "deleteAccount.unexpectedError": "An unexpected error occurred",
  "payment.googlePayTitle": "Pay with Google Pay",
  "payment.googlePayDescription": "Pay quickly and securely using Google Pay",
  "payment.redirectTitle": "You will be redirected to {name}",
  "payment.redirectDescription":
      "You will be redirected to the app after completing the payment",
  "payment.secureAndEncrypted":
      "Secure and encrypted payment without sharing card data",
  "payment.testCardsTitle": "Test Cards",
  "payment.testCardsDescription":
      "Use test cards to simulate different payment scenarios",
  "payment.selectTestCard": "Select Test Card",
  "payment.clear": "Clear",
  "payment.priceDetails": "Price Details",
  "payment.totalWithTax": "Total (including taxes)",
  "payment.taxIncluded": "Including taxes and fees",
  "payment.processingFee": "Processing Fee",
  "payment.finalTotal": "Final Total",
  "payment.confirming": "Confirming payment...",
  "payment.requiresAction": "Additional authentication required...",
  "payment.capturing": "Completing booking...",
  "payment.processingPayment": "Processing payment...",
  "payment.payNowWithAmount": "Pay {amount} {currency}",
  "payment.method": "Payment Method",
  "payment.cardDetails": "Card Details",

  "hotelSearch.guestsAndRooms": "Guests & Rooms",
  "hotelDetails.selectStayDates": "Select Stay Dates",
  "hotelDetails.priceFrom": "Starting from {price}",

  "selectDate": "Select Date",

  "autoFillTooltip": "Auto-fill for testing",
  "guest.importantNotes.note1": "Names must exactly match the passport",
  "guest.importantNotes.note2": "Passport will be requested at check-in",
  "guest.importantNotes.note3": "Passport must be valid for at least 6 months",
  "guest.importantNotes.note4":
      "Additional fees may apply for special requests",
  "guest.importantNotes.note5":
      "All data is required and cannot be modified after confirmation",
  'guest.mainGuest': 'Main Guest',
  'guests.continueToPayment': 'continue To Payment',
  'guests.specialRequests': 'Special Requests',
  'guests.addSpecialRequests': 'add Special Requests',

  "guest.adult": "adult",
  "guest.child": "child",
  'guest.title': 'Title',
  'guest.selectTitle': 'Select Title',
  "guest.importantNotes.title": "Important Notes",
  "guestDetailsTitle": "Guest Details",

  'preBooking.mealTypeRoomOnly': 'Room Only',
  'preBooking.mealTypeBreakfast': 'Breakfast',
  'preBooking.mealTypeHalfBoard': 'Half Board',
  'preBooking.mealTypeFullBoard': 'Full Board',
  'preBooking.mealTypeAllInclusive': 'All Inclusive',
  'preBooking.basePrice': 'Base Price',
  'preBooking.originalPrice': 'Original Price',
  'preBooking.taxes': 'Taxes',
  'preBooking.payment': 'Payment',
  'preBooking.deposit': 'Deposit',
  'preBooking.cancellation': 'Cancellation',
  'preBooking.refund': 'Refund',
  'preBooking.wifi': 'WiFi',
  'preBooking.parking': 'Parking',
  'preBooking.breakfast': 'Breakfast',
  'preBooking.checkInTimeBegin': 'Check-in time:',
  'preBooking.checkOutTime': 'Check-out time: {time}',
  'preBooking.minimumCheckInAge': 'Minimum check-in age: {age} years',
  'preBooking.cardsAccepted': 'Cards accepted:',
  'preBooking.rateConditionsTitle': 'Booking Terms & Conditions',
  'preBooking.noSpecialConditions': 'No special conditions for this booking',
  'preBooking.continueToGuestDetails': 'Continue to guest details',
  'preBooking.readAllDetailsFirst': 'Read all details first',
  'preBooking.cancel': 'Cancel',
  'preBooking.importantNoteReviewTerms':
      'Please review all terms and conditions before proceeding',
  'preBooking.importantNoteCheckGuestData':
      'Ensure guest information is correct before completing the booking',
  'preBooking.importantNoteExtraFees':
      'Additional fees may apply according to hotel policy',
  'preBooking.importantNoteValidID': 'A valid ID must be presented at check-in',
  'preBooking.policyFromDate': 'From date: {date}',
  'preBooking.policyChargeType': 'Charge type: {type}',
  'preBooking.policyCancellationCharge': 'Cancellation charge: {charge}%',
// ...existing code...
  'preBooking.refundable': 'Refundable',
  'preBooking.nonRefundable': 'Non-refundable',
  // No-show policy
  'cancellationPolicy.noShowTitle': 'No-show (No-Show)',
  'cancellationPolicy.noShowDesc':
      'In case of no-show, the full booking amount will be charged',
  'cancellationPolicy.noShowDetail1':
      '100% of the booking amount will be charged',
  'cancellationPolicy.noShowDetail2': 'No amount can be refunded',
  'cancellationPolicy.noShowDetail3':
      'The hotel must be informed in case of delay',
  'myBookingsScreenPlus.hotelAmenitiesAndFacilities':
      'Hotel Amenities and Facilities',
  'myBookingsScreenPlus.hotelAmenities': 'Hotel Amenities',
  'myBookingsScreenPlus.moreFeaturesCount': 'and more features',
  'myBookingsScreenPlus.hotelDescription': 'Hotel Description',

  'hotelDetails.hotelLocation': 'hotel Location',
  'hotelDetails.openInGoogleMaps': 'open In Google Maps',
  'hotelDetails.distance': 'km from you',
  "hotelDetails.stars": "stars",
  "hotelDetails.featuresNote": "Features Note",
  "hotelDetails.pricesStartFrom": "prices Start From",

// ...existing code...
  'myBookingsScreenPlus.hotelStars': '{stars} stars',
  'myBookingsScreenPlus.hotelFacilities': 'Hotel Facilities',

  'myBookingsScreenPlus.cancelWarning':
      'Warning: Cancellation fees may apply according to hotel policy. Please review the cancellation terms below.',
  'myBookingsScreenPlus.priceDetails': 'Price Details',
  'myBookingsScreenPlus.cancelReason': 'Cancellation Reason',
  // Hotel info and meal types
  'myBookingsScreenPlus.hotelLocationAndContact': 'Hotel Location & Contact',
  'myBookingsScreenPlus.hotelAddress': 'Hotel Address',
  'myBookingsScreenPlus.hotelPhone': 'Phone Number',
  'myBookingsScreenPlus.hotelEmail': 'Email',
  'myBookingsScreenPlus.hotelCityCountry': 'City & Country',
  'myBookingsScreenPlus.hotelWebsite': 'Website',
  'myBookingsScreenPlus.hotelCoordinates': 'Coordinates',
  'myBookingsScreenPlus.mealTypeNotSpecified': 'Meal type not specified',
  'myBookingsScreenPlus.mealTypeRoomOnly': 'Room Only',
  'myBookingsScreenPlus.mealTypeBreakfast': 'Breakfast Only',
  'myBookingsScreenPlus.mealTypeHalfBoard': 'Half Board (Breakfast & Dinner)',
  'myBookingsScreenPlus.mealTypeFullBoard':
      'Full Board (Breakfast, Lunch, Dinner)',
  'myBookingsScreenPlus.mealTypeAllInclusive': 'All Inclusive',
  'myBookingsScreenPlus.pleaseSpecifyReason': 'Please specify the reason',
  'myBookingsScreenPlus.writeCancelReason': 'Write the cancellation reason...',
  'myBookingsScreenPlus.cancellationPolicyTitle': 'Cancellation Policy',
  'myBookingsScreenPlus.cancellationPolicyText':
      '• Free cancellation up to 24 hours before arrival\n• Cancellation fees may apply after this date\n• Refunds will be processed within 5-7 business days',

  'myBookingsScreenPlus.cancel': 'Cancel',
  'myBookingsScreenPlus.confirmCancel': 'Confirm Cancellation',
  'myBookingsScreenPlus.emptyTitle': 'No Bookings',
  'myBookingsScreenPlus.emptySubtitle':
      'You have not made any bookings yet.\nStart searching for a hotel and book your perfect stay!',
  'myBookingsScreenPlus.searchHotels': 'Search Hotels',
  'myBookingsScreenPlus.needHelp': 'Need help?',
  'myBookingsScreenPlus.noResults': 'No Results',
  'myBookingsScreenPlus.noResultsSubtitle':
      'No bookings match the filter "{filter}".\nTry changing the filter or searching with different keywords.',
  'myBookingsScreenPlus.clearFilters': 'Clear Filters',
  'myBookingsScreenPlus.loadingBookings': 'Loading bookings...',
  'myBookingsScreenPlus.pleaseWait': 'Please wait',
  'myBookingsScreenPlus.errorTitle': 'An error occurred',
  'myBookingsScreenPlus.retry': 'Retry',
  'myBookingsScreenPlus.detailsTitle': 'Booking Details',
  'myBookingsScreenPlus.guestDetails': 'Guest Details',
  'myBookingsScreenPlus.roomDetails': 'Room Details',
  'myBookingsScreenPlus.stayDates': 'Stay Dates',
  'myBookingsScreenPlus.invoiceBreakdown': 'Invoice Breakdown',
  'myBookingsScreenPlus.totalAmount': 'Total Amount',
  'myBookingsScreenPlus.pricingBreakdown': 'Pricing Breakdown',
  'myBookingsScreenPlus.originalTaxes': 'Original Taxes',
  'myBookingsScreenPlus.finalPrice': 'Final Price',
  'myBookingsScreenPlus.bookingActions': 'Booking Actions',
  'myBookingsScreenPlus.downloadInvoice': 'Download Invoice',
  'myBookingsScreenPlus.shareBooking': 'Share Booking',
  'myBookingsScreenPlus.support': 'Support',
  'myBookingsScreenPlus.cancelBooking': 'Cancel Booking',
  'myBookingsScreenPlus.notFoundTitle': 'Booking Not Found',
  'myBookingsScreenPlus.notFoundSubtitle':
      'We couldn\'t find the booking details.',
  'myBookingsScreenPlus.loadingDetails': 'Loading booking details...',
  'myBookingsScreenPlus.roomGuestsWithChildren':
      '{adults} adult(s), {children} child(ren)',
  'myBookingsScreenPlus.roomGuestsAdultsOnly': '{adults} adult(s)',
  'myBookingsScreenPlus.roomNumber': 'Room {number}',
  'myBookingsScreenPlus.childLabel': 'Child',
  'myBookingsScreenPlus.adultLabel': 'Adult',
  'myBookingsScreenPlus.childGuestLabel': 'Child {index}',
  'myBookingsScreenPlus.adultGuestLabel': 'Adult {index}',
  'myBookingsScreenPlus.cancellationPolicy': 'Cancellation Policy',

  // Dynamic booking details keys
  'myBookingsScreenPlus.roomLabel': '{count} room(s)',
  'myBookingsScreenPlus.adultsCount': '{count} adult(s)',
  'myBookingsScreenPlus.childrenCount': '{count} child(ren)',

  // Authentication
  'authentication.enterYourPhoneNumber': 'Enter your phone number',
  'authentication.weWillSendYouVerificationCodeViaSMS':
      'We will send you a verification code via SMS',
  'authentication.phoneNumber': 'Phone Number',
  'authentication.enterVerificationCode': 'Enter verification code',
  'authentication.verificationCodeSentTo': 'Verification code sent to',
  'authentication.verify': 'Verify',
  'authentication.resendCode': 'Resend Code',
  'authentication.invalidPhoneNumber': 'Invalid phone number',
  'authentication.invalidOTP': 'Invalid verification code',
  'authentication.otpExpired': 'Verification code expired',
  'authentication.loginSuccessful': 'Login successful',
  'authentication.welcomeBack': 'Welcome back',
  'authentication.next': 'Next',
  'authentication.code': 'Code',
  'authentication.yourNumber': 'Your number',
  'authentication.pleaseConfirmYourPhoneCountryCodeAndEnterYourPhoneNumber':
      'Please confirm your phone country code and enter your phone number',
  'authentication.bySigningUpYouAgreeToOur': 'By signing up you agree to our',
  'authentication.termsOfService': 'Terms of Service',

  // Common
  'countryCode': 'Country Code',
  'search': 'Search',
  'loading': 'Loading...',
  'error': 'Error',
  'success': 'Success',
  'retry': 'Retry',
  'cancel': 'Cancel',
  'ok': 'OK',
  'close': 'Close',
  'back': 'Back',
  'next': 'Next',
  'done': 'Done',
  'save': 'Save',
  'edit': 'Edit',
  'delete': 'Delete',
  'add': 'Add',

  // Home
  'home.title': 'Home',
  'home.welcomeBack': 'Welcome back 👋',
  'home.guestsInformation': 'Guests information',
  'home.where': 'Where',
  'home.whereAreYouGoing': 'Where are you going',
  'home.changeLocation': 'Change location',
  'home.from': 'From',
  'home.to': 'To',
  'home.maximumNumberOfGuestsPerRoomIs8':
      'Maximum number of guests per room is 3',
  'home.addRoom': 'Add room',
  'home.ageOfChild': 'Age of child',
  'home.under1YearOld': 'Under 1 year old',
  'home.yearOld': 'Year old',
  'home.apply': 'Apply',
  'home.room': 'Room',
  'home.remove': 'Remove',
  'home.adults': 'Adults',
  'home.agePlus12': 'Age 12+',
  'home.children': 'Children',
  'home.age011': 'Age 0-11',
  'home.child': 'Child',
  'home.under1YearOldChild': 'Under 1 year old child',
  'home.yearOlds': 'Year olds',
  'home.yearsOld': 'Years old',
  'home.viewAll': 'View all',
  'home.nearbyHotels': 'Nearby hotels',
  'home.findBestPlace': 'Find the best place to relax',
  'home.checkAvailability': 'Check Availability',
  'home.search': 'Search',
  'home.countryCode': 'Country Code',
  'home.rooms': 'Rooms',
  'home.fandooq': 'Fandooq',
  'home.ar': 'Arabic',
  'home.en': 'English',
  'home.currency': 'Currency',
  'home.whereToStay': 'Where do you want to travel?',
  'home.chooseDestination': 'Choose destination',
  'home.checkIn': 'Check-in',
  'home.checkOut': 'Check-out',
  'home.selectDate': 'Select date',
  'home.guests': 'Guests',
  'home.guestsAndRooms': 'Guests & Rooms',
  'home.searchHotels': 'Search Hotels',
  'home.nights': 'nights',
  'home.currentCurrency': 'Current Currency',
  'home.whereIsYourHotel': 'Where is your hotel',
  'home.aroundMyLocation': 'Around my location',
  'home.loadingYourLocation': 'Loading your location',
  'home.clickToBringUpYourLocation': 'Click to bring up your location',
  'home.recentSearches': 'Recent searches',
  'home.clearAll': 'Clear all',
  'home.famousCities': 'Famous cities',
  'hotels.youDontHaveAnyHotels': 'You don\'t have any hotels',
  'hotels.useAnotherFilter': 'Use another filter',

  // Settings
  'settings.title': 'Settings',
  'settings.account': 'Account',
  'settings.login': 'Login',
  'settings.loginToAccessFeatures': 'Login to access all features',
  'settings.editProfile': 'Edit Profile',
  'settings.managePersonalInfo': 'Manage your personal information',
  'settings.bookings': 'My Bookings',
  'settings.viewAllBookings': 'View all bookings',
  'settings.logout': 'Logout',
  'settings.logoutFromAccount': 'Logout from account',
  'settings.appSettings': 'App Settings',
  'settings.language': 'Language',
  'settings.selectLanguage': 'Select Language',
  'settings.country': 'Country',
  'settings.displayCurrency': 'Display Currency',
  'settings.english': 'English',
  'settings.arabic': 'Arabic',
  'settings.paymentMethods': 'Payment Methods',
  'settings.currency': 'Currency',
  'settings.notifications': 'Notifications',
  'settings.manageAppNotifications': 'Manage app notifications',
  'settings.accountSettings': 'Account Settings',
  'settings.deleteAccountSubtitle': 'Permanently delete account with all data',
  // تم حذف التكرار: 'settings.editProfile' و 'settings.managePersonalInfo'
  'settings.changePassword': 'Change Password',
  'settings.updateAccountSecurity': 'Update account security',
  'settings.advancedSettings': 'Advanced Settings',
  'settings.clearCache': 'Clear Cache',
  'settings.improveAppPerformance': 'Improve app performance',
  'settings.legalAndPrivacy': 'Legal & Privacy',
  'settings.privacyPolicy': 'Privacy Policy',
  'settings.howWeProtectYourData': 'How we protect your data',
  'settings.termsAndConditions': 'Terms & Conditions',
  'settings.appUsageTerms': 'App usage terms',
  'settings.darkMode': 'Dark Mode',
  'settings.enableDarkMode': 'Enable dark mode',
  'settings.locationServices': 'Location Services',
  'settings.allowLocationAccess': 'Allow location access',
  'settings.supportAndHelp': 'Support & Help',
  'settings.contactUs': 'Contact Us',
  'settings.getHelpAndSupport': 'Get help and support',
  'settings.rateApp': 'Rate App',
  'settings.rateYourExperience': 'Rate your experience',
  'settings.shareApp': 'Share App',
  'settings.shareWithFriends': 'Share with friends',
  'settings.aboutApp': 'About App',
  'settings.appInfo': 'App Info',
  'settings.version': 'Version',
  'settings.copyright': '© 2024 Fandooq. All rights reserved.',

  // Profile
  'profile.editProfile': 'Edit Profile',
  'profile.personalInformation': 'Personal Information',
  'profile.firstName': 'First Name',
  'profile.lastName': 'Last Name',
  'profile.email': 'Email',
  'profile.dateOfBirth': 'Date of Birth',
  'profile.gender': 'Gender',
  'profile.nationality': 'Nationality',
  'profile.male': 'Male',
  'profile.female': 'Female',
  'profile.other': 'Other',
  'profile.profileImage': 'Profile Image',
  'profile.changeProfileImage': 'Change Profile Image',
  'profile.takePhoto': 'Take Photo',
  'profile.chooseFromGallery': 'Choose from Gallery',
  'profile.removeImage': 'Remove Image',
  'profile.saveChanges': 'Save Changes',
  'profile.cancel': 'Cancel',
  'profile.profileUpdatedSuccessfully': 'Profile updated successfully',
  'profile.profileImageUpdated': 'Profile image updated successfully',
  'profile.profileImageDeleted': 'Profile image deleted successfully',
  'profile.errorLoadingProfile': 'Error loading profile',
  'profile.errorUpdatingProfile': 'Error updating profile',
  'profile.errorUploadingImage': 'Error uploading image',
  'profile.errorDeletingImage': 'Error deleting image',
  'profile.errorSelectingImage': 'Error selecting image',
  'profile.errorTakingPhoto': 'Error taking photo',
  'profile.invalidEmail': 'Please enter a valid email address',
  'profile.mustBeAtLeast2Characters': 'must be at least 2 characters',
  'profile.selectDate': 'Select Date',
  'profile.selectGender': 'Select Gender',
  'profile.selectNationality': 'Select Nationality',
  'profile.optional': 'Optional',
  'profile.phoneNumber': 'Phone Number',
  'profile.verified': 'Verified',
  'profile.notVerified': 'Not Verified',
  'profile.accountInformation': 'Account Information',
  'profile.contactInformation': 'Contact Information',

  'apply': 'Apply',

  // My Bookings Screen Plus
  'myBookingsScreenPlus.title': 'My Bookings',
  'myBookingsScreenPlus.refresh': 'Refresh',
  'myBookingsScreenPlus.loading': 'Loading bookings...',
  'myBookingsScreenPlus.confirmed': 'Confirmed',
  'myBookingsScreenPlus.pending': 'Pending',
  'myBookingsScreenPlus.cancelled': 'Cancelled',
  'myBookingsScreenPlus.newBooking': 'New Booking',
  'myBookingsScreenPlus.help': 'Help',
  'myBookingsScreenPlus.helpTitle': 'Help',
  'myBookingsScreenPlus.helpContent':
      'For help with your bookings, you can:\n\n• Search for a specific booking\n• Filter bookings by status\n• View details of any booking\n• Cancel bookings that are cancellable\n\nFor more help, contact us.',
  'myBookingsScreenPlus.ok': 'OK',
  'myBookingsScreenPlus.bookingReference': 'Booking Reference',
  'myBookingsScreenPlus.bookingReferenceNA': 'N/A',

  // Booking status and cancellation info
  'myBookingsScreenPlus.statusConfirmedDesc':
      'Your booking is confirmed. Enjoy your stay!',
  'myBookingsScreenPlus.statusPendingDesc':
      'Your booking is pending. We will notify you once it is confirmed.',
  'myBookingsScreenPlus.statusCancelledDesc':
      'Your booking has been cancelled. If you paid in advance, a refund will be processed as per policy.',
  'myBookingsScreenPlus.statusFailedDesc':
      'Booking failed. Please try again or contact support.',
  'myBookingsScreenPlus.statusUnknownDesc':
      'Booking status is unknown. Please check details or contact support.',
  'myBookingsScreenPlus.confirmationNumber': 'Confirmation Number',
  'myBookingsScreenPlus.freeCancelUntil':
      'Free cancellation for {days} more day(s)',
  'myBookingsScreenPlus.lastFreeCancelDay':
      'Today is the last day for free cancellation',
  'myBookingsScreenPlus.searchHint': 'Search bookings...',
  'myBookingsScreenPlus.advancedSearch': 'Advanced Search',
  'myBookingsScreenPlus.clearAll': 'Clear All',
  'myBookingsScreenPlus.applyFilters': 'Apply Filters',
  'myBookingsScreenPlus.dateRange': 'Date Range',
  'myBookingsScreenPlus.selectDateRange': 'Select date range',
  'myBookingsScreenPlus.priceRange': 'Price Range',
  'myBookingsScreenPlus.selectPriceRange': 'Select price range',
  'myBookingsScreenPlus.setPriceRange': 'Set Price Range',
  'myBookingsScreenPlus.statusAll': 'All',
  'myBookingsScreenPlus.statusConfirmed': 'Confirmed',
  'myBookingsScreenPlus.statusPending': 'Pending',
  'myBookingsScreenPlus.statusCancelled': 'Cancelled',
  'myBookingsScreenPlus.statusFailed': 'Failed',
  'myBookingsScreenPlus.quickActive': 'Active Bookings',
  'myBookingsScreenPlus.quickUpcoming': 'Upcoming Bookings',
  'myBookingsScreenPlus.quickPast': 'Past Bookings',
  'myBookingsScreenPlus.unknownHotel': 'Unknown Hotel',
  'myBookingsScreenPlus.checkIn': 'Check-in',
  'myBookingsScreenPlus.checkOut': 'Check-out',

  // Timeline nights count
  'myBookingsScreenPlus.nightsCount': '{count} night(s)',
  'myBookingsScreenPlus.night': 'night',
  'myBookingsScreenPlus.room': 'room',
  'myBookingsScreenPlus.guest': 'guest',
  'myBookingsScreenPlus.totalPrice': 'Total Price',
  'myBookingsScreenPlus.viewDetails': 'View Details',
  // Home Hotels Section Plus
  'homeHotelsSectionPlus.nearbyHotels': 'Hotels Near You',
  'homeHotelsSectionPlus.showAll': 'Show All',
  'homeHotelsSectionPlus.noNearbyHotels': 'No nearby hotels',
  'homeHotelsSectionPlus.enableLocationToShowNearby':
      'Please enable location to show nearby hotels',
  'homeHotelsSectionPlus.stars': '{count} stars',
  'homeHotelsSectionPlus.startingFrom': 'Starting from',
  // Popular Locations Suggestions
  'popularLocationsSection.suggestionDubai': 'Dubai',
  'popularLocationsSection.suggestionCairo': 'Cairo',
  'popularLocationsSection.suggestionRiyadh': 'Riyadh',
  'popularLocationsSection.suggestionJeddah': 'Jeddah',
  'popularLocationsSection.suggestionDoha': 'Doha',
  'popularLocationsSection.suggestionKuwait': 'Kuwait',
  // Popular Locations Section
  'popularLocationsSection.popularDestinations': 'Popular Destinations',
  'popularLocationsSection.refresh': 'Refresh',
  'popularLocationsSection.quickSearch': 'Quick Search',
  'popularLocationsSection.noPopularDestinations': 'No popular destinations',
  // Location Search Bar Section
  'locationSearchBar.currentLocation': 'Current Location',
  // Location Results List Section
  'locationResultsList.noResults': 'No results found',
  'locationResultsList.tryDifferentKeywords':
      'Try searching with different keywords',
  // Location Picker Section
  'locationPicker.label': 'Location',
  'locationPicker.chooseLocation': 'Choose Location',
  'locationPicker.where': 'Where',
  'locationPicker.chooseDestination': 'Choose Destination',
  'locationPicker.searchHint': 'Search for a city or hotel...',
  // Location Search Plus Section
  'locationSearchPlus.chooseDestination': 'Choose Destination',
  'locationSearchPlus.searchHint': 'Search for city or destination',
  'locationSearchPlus.popularDestinations': 'Popular Destinations',
  // Location Search Section
  'locationSearch.searchHint': 'Search for a city or hotel...',
  'locationSearch.chooseLocation': 'Choose Location',
  // Hotel Details Section
  'hotelDetails.availableRooms': 'Available Rooms',
  'hotelDetails.startingFrom': 'Starting from',
  'hotelDetails.finalPrice': 'Final Price',
  'hotelDetails.perStay': 'per stay',
  'hotelDetails.selected': 'Selected',
  'hotelDetails.freeCancellation': 'Free Cancellation',
  'hotelDetails.freeBreakfast': 'Free Breakfast',
  'hotelDetails.airportTransfer': 'Airport Transfer',
  'hotelDetails.noRoomsAvailable': 'No rooms available',
  'hotelDetails.noRoomsAvailableDesc':
      'No rooms are available for this hotel at the moment. Please select other dates or search for another hotel.',

  'hotelDetails.withBreakfast': 'With Breakfast',
  'hotelDetails.halfBoard': 'Half Board',
  'hotelDetails.fullBoard': 'Full Board',
  'hotelDetails.allInclusive': 'All Inclusive',
  'hotelDetails.roomOnly': 'Room Only',
  'hotelDetails.chooseRoom': 'Choose the right room for you',
  'hotelDetails.additionalChargesAtProperty':
      'Additional charges may be payable at the property.',
  'hotelDetails.priceIncludesAll': 'Price includes all taxes and charges',
  'hotelDetails.additionalChargesMayApply': 'Additional charges may apply',
  'hotelDetails.someRoomsExtraCharges':
      'Some rooms may require extra charges at the property.',

  'hotelDetails.finalPrices': 'Final Prices',
  'hotelDetails.allPricesIncludeTax': 'All prices include taxes and charges.',
  'hotelDetails.specialOffers': 'Special Offers',
  'hotelDetails.aboutHotel': 'About Hotel',
  'hotelDetails.quickOverview': 'Quick Overview',
  'hotelDetails.roomTypesAvailable': '{count} room types available',

  'currencySelection.header': 'Select Currency',
  'currencySelection.searchHint': 'Search for currency or country...',
  'currencySelection.resultsCount': '{count} currencies found',
  'currencySelection.noResults': 'No currencies found',
  'currencySelection.tryDifferentKeywords':
      'Try searching with different keywords',
  'currencySelection.clearSearch': 'Clear search',

  'currency.usd': 'US Dollar',
  'currency.eur': 'Euro',
  'currency.sar': 'Saudi Riyal',
  'currency.aed': 'UAE Dirham',
  'currency.egp': 'Egyptian Pound',
  'currency.qar': 'Qatari Riyal',
  'currency.kwd': 'Kuwaiti Dinar',
  'currency.bhd': 'Bahraini Dinar',
  'currency.omr': 'Omani Rial',
  'currency.jod': 'Jordanian Dinar',
  'currency.lbp': 'Lebanese Pound',
  'currency.gbp': 'British Pound',
  'currency.jpy': 'Japanese Yen',
  'currency.cny': 'Chinese Yuan',
  'currency.inr': 'Indian Rupee',
  'currency.pkr': 'Pakistani Rupee',
  'currency.try': 'Turkish Lira',
  'currency.cad': 'Canadian Dollar',
  'currency.aud': 'Australian Dollar',
  'currency.chf': 'Swiss Franc',
  'currency.country.us': 'United States',
  'currency.country.eu': 'European Union',
  'currency.country.sa': 'Saudi Arabia',
  'currency.country.ae': 'United Arab Emirates',
  'currency.country.eg': 'Egypt',
  'currency.country.qa': 'Qatar',
  'currency.country.kw': 'Kuwait',
  'currency.country.bh': 'Bahrain',
  'currency.country.om': 'Oman',
  'currency.country.jo': 'Jordan',
  'currency.country.lb': 'Lebanon',
  'currency.country.gb': 'United Kingdom',
  'currency.country.jp': 'Japan',
  'currency.country.cn': 'China',
  'currency.country.in': 'India',
  'currency.country.pk': 'Pakistan',
  'currency.country.tr': 'Turkey',
  'currency.country.ca': 'Canada',
  'currency.country.au': 'Australia',
  'currency.country.ch': 'Switzerland',

  // Filter
  'filter.popularFilters': 'Popular filters',
  'filter.freeCancellation': 'Free cancellation',
  'filter.breakfastIncluded': 'Breakfast included',
  'filter.roomOnly': 'Room only',
  'filter.halfBoard': 'Half board',

  // Cancellation
  'cancellation.reasonOfCancellation': 'Reason of cancellation',

  // General UI
  'ui.seeAll': 'See all',
  'ui.webResponsiveMessage': 'Web responsive will available soon....',

  // Interested Points
  'interestedPoints.seeAll': 'See all',

  // Alerts and Messages
  'alerts.success': 'Success',
  'alerts.error': 'Error',
  'alerts.back': 'Back',

  // Hotels Plus
  'hotelsPlus.freeCancellation': 'Free cancellation',
  'hotelsPlus.freeBreakfast': 'Free breakfast',
  'hotelsPlus.freeWiFi': 'Free WiFi',
  'hotelsPlus.pool': 'Pool',
  'hotelsPlus.facilitiesAndServices': 'Facilities and Services',
  'hotelsPlus.facilitiesDescription':
      'Discover all available features and facilities',
  'hotelsPlus.aboutHotel': 'About Hotel',
  'hotelsPlus.quickOverview': 'Quick Overview',
  'hotelsPlus.roomTypesAvailable': '{count} room types available',
  'hotelsPlus.bookNow': 'Book Now',
  'hotelsPlus.selectRoom': 'Select Room',
  'hotelsPlus.additionalChargesMayApply':
      'Additional charges may apply at property',
  'hotelsPlus.additionalCharges':
      'Additional {currency} {price} may apply at property',

  // Booking Success
  'bookingSuccess.statusConfirmed': 'Confirmed',
  'bookingSuccess.statusPending': 'Pending',
  'bookingSuccess.statusCancelled': 'Cancelled',

  // Booking Details Plus
  'bookingDetailsPlus.detailsHelpContent':
      'Here you can view all your booking details, download invoice, and contact support if needed.',

  // Hotel Details Plus - Missing translations
  'hotelDetails.priceIncludesAllTaxes': 'Price includes all taxes and charges',

  // Hotel Search Params - Missing translations
  'hotelSearchParams.adults': 'adults',
  'hotelSearchParams.adult': 'adult',
  'hotelSearchParams.children': 'children',
  'hotelSearchParams.child': 'child',
  'hotelSearchParams.rooms': 'rooms',
  'hotelSearchParams.room': 'room',
  'hotelSearchParams.searchParameters': 'Search Parameters',

  // Booking Status Translations
  'bookingStatus.confirmed': 'Confirmed',
  'bookingStatus.pending': 'Pending',
  'bookingStatus.cancelled': 'Cancelled',
  'bookingStatus.failed': 'Failed',
  'bookingStatus.cancellationInProgress': 'Cancellation in Progress',
  'bookingStatus.refunded': 'Refunded',
  'bookingStatus.unknown': 'Unknown',

  // Payment Status Translations
  'paymentStatus.pending': 'Pending',
  'paymentStatus.completed': 'Completed',
  'paymentStatus.failed': 'Failed',

  // Payment Mode Translations
  'paymentMode.limit': 'Immediate Payment',
  'paymentMode.credit': 'Credit',

  // Cancellation Policy Translations
  'cancellationPolicy.title': 'Cancellation Policy',
  'cancellationPolicy.refundable': 'Refundable',
  'cancellationPolicy.nonRefundable': 'Non-refundable',
  'cancellationPolicy.nonRefundableTitle': 'Non-refundable or Non-cancellable',
  'cancellationPolicy.nonRefundableDesc':
      'This booking is non-refundable and non-cancellable under any circumstances.',
  'cancellationPolicy.freeCancellation': 'Free Cancellation',
  'cancellationPolicy.freeCancellationDesc':
      'Free cancellation until 2 PM on {date}',
  'cancellationPolicy.details': 'Cancellation Policy Details:',
  'cancellationPolicy.nonRefundablePolicy':
      'Non-refundable. 100% charge will be applied for cancellation.',
  'cancellationPolicy.percentageCharge':
      'Cancellation fee: {charge}% of total amount',
  'cancellationPolicy.reviewWithHotel':
      'Please review cancellation policy with the hotel',
  'cancellationPolicy.freeUntil24Hours':
      'Free cancellation until 24 hours before arrival. After that, one night fee will be charged.',
  'cancellationPolicy.nonRefundableAfterConfirmation':
      'Non-refundable. Amount cannot be refunded after confirmation.',

  // Late Cancellation Policy
  'cancellationPolicy.lateCancellationTitle': 'Late Cancellation Fee',
  'cancellationPolicy.lateCancellationDesc':
      'If cancelled after the deadline, the cost of {nights} will be charged.',
  'cancellationPolicy.lateCancellationDetail1':
      'Cancellation fee: {percent}% of the value of {nights}',
  'cancellationPolicy.lateCancellationDetail2':
      'The amount will be deducted from the credit card used for booking.',
  'cancellationPolicy.lateCancellationDetail3':
      'Refund processing takes 5-7 business days.',

  // Night count for cancellation policy
  'cancellationPolicy.nightsOne': 'one night',
  'cancellationPolicy.nightsTwo': 'two nights',

  // Free cancellation details
  'cancellationPolicy.freeCancellationDetail1':
      'Free cancellation until {date} at 2:00 PM',
  'cancellationPolicy.freeCancellationDetail2':
      'Full refund of the paid amount',
  'cancellationPolicy.freeCancellationDetail3': 'No additional fees',

  // Non-refundable details
  'cancellationPolicy.nonRefundableDetail1': 'This booking cannot be cancelled',
  'cancellationPolicy.nonRefundableDetail2':
      'The paid amount cannot be refunded',
  'cancellationPolicy.nonRefundableDetail3':
      'In case of no-show, the full booking amount will be charged',

  // Payment Method Selection
  'paymentMethod.selectTitle': 'Select Payment Method',
  'paymentMethod.applePay': 'Apple Pay',
  'paymentMethod.googlePay': 'Google Pay',
  'paymentMethod.creditCard': 'Credit Card',
  'paymentMethod.cancel': 'Cancel',

  // Hotel Features
  'hotelFeatures.freeCancellation': 'Free cancellation',
  'hotelFeatures.breakfastIncluded': 'Breakfast included',
  'hotelFeatures.freeWiFi': 'Free WiFi',
  'hotelFeatures.pool': 'Pool',

  // Hotel Pricing
  'hotelPricing.from': 'From',
  'hotelPricing.startingFrom': 'Starting from',
  'hotelPricing.finalPriceInclusive': 'Final price includes all fees',
  'hotelPricing.perStay': 'per stay',
  'hotelPricing.seeAvailability': 'See availability',

  // Sorting Options
  'sorting.priceLowToHigh': 'Price (Low to High)',
  'sorting.priceLowToHighDesc': 'Best deals first',
  'sorting.priceHighToLow': 'Price (High to Low)',
  'sorting.priceHighToLowDesc': 'Luxury options first',
  'sorting.starRating': 'Star Rating',
  'sorting.starRatingDesc': 'Highest rated first',
  'sorting.hotelName': 'Hotel Name',
  'sorting.hotelNameDesc': 'Alphabetical order',

  // Price Range
  'priceRange.title': 'Price Range: {min} - {max}',

  // Hotels Screen - Filter Quick Chips
  'hotelsScreen.freeCancellation': 'Free Cancellation',
  'hotelsScreen.breakfast': 'Breakfast',
  'hotelsScreen.fourPlusStars': '4+ Stars',
  'hotelsScreen.under100': 'Under {symbol}100',
  'hotelsScreen.freeWiFi': 'Free WiFi',
  'hotelsScreen.pool': 'Pool',

  // Hotels Screen - General
  'hotelsScreen.noHotelsFound': 'No hotels found',
  'hotelsScreen.unknownState': 'Unknown state',
  'hotelsScreen.hotelsFound': '{count} hotels found',
  'hotelsScreen.showOnMap': 'Show on map',
  'hotelsScreen.sortBy': 'Sort by',

  // Hotels Screen - Filters
  'hotels.filters': 'Filters',
  'hotels.resetAll': 'Reset All',
  'hotels.amenities': 'Amenities',
  'hotels.freeCancellation': 'Free Cancellation',
  'hotels.flexibleBooking': 'Flexible booking options',
  'hotels.breakfastIncluded': 'Breakfast Included',
  'hotels.startDayRight': 'Start your day right',
  'hotels.freeWifi': 'Free WiFi',
  'hotels.stayConnected': 'Stay connected',
  'hotels.swimmingPool': 'Swimming Pool',
  'hotels.relaxUnwind': 'Relax and unwind',

  // Hotel Details Plus - Additional
  'hotelsPlus.amenities': 'Amenities',
  'hotelsPlus.showMore': 'Show More',
  'hotelsPlus.showLess': 'Show Less',

  // Hotel Search Params Section
  'hotelSearchParams.checkInDate': 'Check-in',
  'hotelSearchParams.checkOutDate': 'Check-out',
  'hotelSearchParams.nights': '{count} nights',
  'hotelSearchParams.night': '{count} night',
  'hotelSearchParams.updateSearch': 'Update Search',
  'hotelSearchParams.updating': 'Updating...',

  // Pre-booking Screen
  'preBooking.bookingExpired': 'Booking Expired',
  'preBooking.bookingExpiresIn': 'Booking Expires In',
  'preBooking.pleaseRetry': 'Please try again',
  'preBooking.timeRemaining': 'Remaining: {time}',
  'preBooking.bookingSummary': 'Booking Summary',
  'preBooking.priceBreakdown': 'Price Breakdown',
  'preBooking.cancellationPolicy': 'Cancellation Policy',
  'preBooking.importantNotes': 'Important Notes',
  'preBooking.rateConditions': 'Rate Conditions',
  'preBooking.dailyRates': 'Daily Rates',
  'preBooking.cancellationPolicyDetails': 'Cancellation Policy Details:',

  "share.title": "🏨 Booking Details",
  "share.bookingNumber": "📋 Booking Number",
  "share.confirmationNumber": "✅ Confirmation Number",
  "share.hotel": "🏨 Hotel",
  "share.location": "📍 Location",
  "share.checkIn": "📅 Check-in Date",
  "share.checkOut": "📅 Check-out Date",
  "share.nights": "🌙 Number of Nights",
  "share.totalPrice": "💰 Total Price",
  "share.status": "💳 Payment Status",
  "share.footer": "Booked via Fandooq App",
  "share.subject": "Booking Details",

  "snackbar.cancelling": "Cancelling booking...",
  "snackbar.cancel.success": "Booking cancelled successfully",
  "snackbar.cancel.failed": "Failed to cancel booking",
  "snackbar.cancel.notFound": "Booking not found or already cancelled",
  "snackbar.cancel.unauthorized":
      "You are not authorized to cancel this booking",
  "snackbar.error.loadingDetails":
      "An error occurred while loading booking details",

  "modifyBooking.soon": "Booking modification will be available soon",

  "booking.load.error.notFound": "Booking ID not found",
  "booking.load.error.failed": "Failed to load booking details",
  "booking.load.error.exception":
      "An error occurred while loading booking details",

  "cancellation.inProgress": "Cancelling the booking...",
  "cancellation.success": "The booking was cancelled successfully",
  "cancellation.error.general":
      "An error occurred while cancelling the booking",
  "cancellation.error.notFound":
      "The booking was not found or has already been cancelled",
  "cancellation.error.unauthorized":
      "You are not authorized to cancel this booking",

  "cancellation.changeOfPlans": "Change of plans",
  "cancellation.emergencyTravel": "Emergency travel",
  "cancellation.healthIssues": "Health issues",
  "cancellation.workCommitments": "Work commitments",
  "cancellation.financialReasons": "Financial reasons",
  "cancellation.weatherConditions": "Weather conditions",
  "cancellation.familyEmergency": "Family emergency",
  "cancellation.betterDeal": "Better deal",
  "cancellation.hotelIssues": "Hotel issues",
  "cancellation.other": "Other",
};
