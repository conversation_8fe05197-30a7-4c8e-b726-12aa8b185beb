import 'package:get/get.dart';

class AppStrings {
  //--------------------- Global ---------------------
  static String get darkTheme => 'global.darkTheme'.tr;
  static String get lightTheme => 'global.lightTheme'.tr;

  //--------------------- Auth Validation ---------------------
  static String get emailValidator => 'authValidation.emailValidator'.tr;
  static String get latLngValidator => 'authValidation.latLngValidator'.tr;
  static String get passwordValidator => 'authValidation.passwordValidator'.tr;
  static String get passwordValidatorEmpty =>
      'authValidation.passwordValidatorEmpty'.tr;
  static String get validateMatchError =>
      'authValidation.validateMatchError'.tr;
  static String get noInternetConnection =>
      'authValidation.noInternetConnection'.tr;
  static String get nameValidatorEmpty =>
      'authValidation.nameValidatorEmpty'.tr;
  static String get nameValidator => 'authValidation.nameValidator'.tr;
  static String get phoneValidator => 'authValidation.phoneValidator'.tr;

  //--------------------- Authentication ---------------------
  static String get yourNumber => 'authentication.yourNumber'.tr;
  static String get pleaseConfirmYourPhoneCountryCodeAndEnterYourPhoneNumber =>
      'authentication.pleaseConfirmYourPhoneCountryCodeAndEnterYourPhoneNumber'
          .tr;
  static String get next => 'authentication.next'.tr;
  static String get bySigningUpYouAgreeToOur =>
      'authentication.bySigningUpYouAgreeToOur'.tr;
  static String get termsOfService => 'authentication.termsOfService'.tr;
  static String get code => 'authentication.code'.tr;

  //--------------------- Home ---------------------
  static String get home => 'home.home'.tr;
  static String get welcomeBack => 'home.welcomeBack'.tr;
  static String get guestsInformation => 'home.guestsInformation'.tr;
  static String get where => 'home.where'.tr;
  static String get whereAreYouGoing => 'home.whereAreYouGoing'.tr;
  static String get changeLocation => 'home.changeLocation'.tr;
  static String get from => 'home.from'.tr;
  static String get to => 'home.to'.tr;
  static String get maximumNumberOfGuestsPerRoomIs8 =>
      'home.maximumNumberOfGuestsPerRoomIs8'.tr;
  static String get addRoom => 'home.addRoom'.tr;
  static String get ageOfChild => 'home.ageOfChild'.tr;
  static String get under1YearOld => 'home.under1YearOld'.tr;
  static String get yearOld => 'home.yearOld'.tr;
  static String get apply => 'home.apply'.tr;
  static String get room => 'home.room'.tr;
  static String get remove => 'home.remove'.tr;
  static String get adults => 'home.adults'.tr;
  static String get agePlus12 => 'home.agePlus12'.tr;
  static String get children => 'home.children'.tr;
  static String get age011 => 'home.age011'.tr;
  static String get whereToStay => 'home.whereToStay'.tr;
  static String get chooseDestination => 'home.chooseDestination'.tr;
  static String get checkIn => 'home.checkIn'.tr;
  static String get checkOut => 'home.checkOut'.tr;
  static String get selectDate => 'home.selectDate'.tr;
  static String get guests => 'home.guests'.tr;
  static String get guestsAndRooms => 'home.guestsAndRooms'.tr;
  static String get searchHotels => 'home.searchHotels'.tr;
  static String get nights => 'home.nights'.tr;
  static String get currentCurrency => 'home.currentCurrency'.tr;
  static String get child => 'home.child'.tr;
  static String get under1YearOldChild => 'home.under1YearOldChild'.tr;
  static String get yearOlds => 'home.yearOlds'.tr;
  static String get yearsOld => 'home.yearsOld'.tr;
  static String get viewAll => 'home.viewAll'.tr;
  static String get nearbyHotels => 'home.nearbyHotels'.tr;
  static String get search => 'home.search'.tr;
  static String get rooms => 'home.rooms'.tr;
  static String get fandooq => 'home.fandooq'.tr;
  static String get ar => 'home.ar'.tr;
  static String get en => 'home.en'.tr;
  static String get currency => 'home.currency'.tr;
  static String get whereIsYourHotel => 'home.whereIsYourHotel'.tr;
  static String get aroundMyLocation => 'home.aroundMyLocation'.tr;
  static String get loadingYourLocation => 'home.loadingYourLocation'.tr;
  static String get clickToBringUpYourLocation =>
      'home.clickToBringUpYourLocation'.tr;
  static String get recentSearches => 'home.recentSearches'.tr;
  static String get clearAll => 'home.clearAll'.tr;
  static String get famousCities => 'home.famousCities'.tr;

  //--------------------- Profile ---------------------
  static String get profile => 'profile.profile'.tr;
  static String get travellers => 'profile.travellers'.tr;
  static String get whyHaveAnAccountIsImportantForYou =>
      'profile.whyHaveAnAccountIsImportantForYou'.tr;
  static String get whyHaveAnAccountIsImportantForYouDescription =>
      'profile.whyHaveAnAccountIsImportantForYouDescription'.tr;
  static String get signIn => 'profile.signIn'.tr;
  static String get dontHaveAnAccount => 'profile.dontHaveAnAccount'.tr;
  static String get createAnAccount => 'profile.createAnAccount'.tr;
  static String get bookings => 'profile.bookings'.tr;
  static String get favourites => 'profile.favourites'.tr;
  static String get messages => 'profile.messages'.tr;
  static String get wallet => 'profile.wallet'.tr;
  static String get addNewCard => 'profile.addNewCard'.tr;
  static String get personalInformation => 'profile.personalInformation'.tr;
  static String get settings => 'profile.settings'.tr;
  static String get userInformation => 'profile.userInformation'.tr;
  static String get contactUs => 'profile.contactUs'.tr;
  static String get branchLocations => 'profile.branchLocations'.tr;
  static String get help => 'profile.help'.tr;
  static String get about => 'profile.about'.tr;
  static String get accountDetails => 'profile.accountDetails'.tr;
  static String get emailAddress => 'profile.emailAddress'.tr;
  static String get mobileNumber => 'profile.mobileNumber'.tr;
  static String get completeYourDetailsToEnjoyAMorePersonalizedExperience =>
      'profile.completeYourDetailsToEnjoyAMorePersonalizedExperience'.tr;
  static String get firstName => 'profile.firstName'.tr;
  static String get middleName => 'profile.middleName'.tr;
  static String get lastName => 'profile.lastName'.tr;
  static String get gender => 'profile.gender'.tr;
  static String get dateOfBirth => 'profile.dateOfBirth'.tr;
  static String get nationality => 'profile.nationality'.tr;
  static String get countryOfResidence => 'profile.countryOfResidence'.tr;
  static String get familyStatus => 'profile.familyStatus'.tr;
  static String get deleteAccount => 'profile.deleteAccount'.tr;
  static String get notVerified => 'profile.notVerified'.tr;
  static String get save => 'profile.save'.tr;
  static String get signOut => 'profile.signOut'.tr;
  static String
      get pleaseConfirmYourEmailAddressByFollowingTheLinkWeSentToYourEmail =>
          'profile.pleaseConfirmYourEmailAddressByFollowingTheLinkWeSentToYourEmail'
              .tr;
  static String get resendEmail => 'profile.resendEmail'.tr;
  static String get enterYourEmailAddress => 'profile.enterYourEmailAddress'.tr;
  static String
      get weWillSendYouAnEmailWithAVerificationLinkToConfirmYourEmailAddress =>
          'profile.weWillSendYouAnEmailWithAVerificationLinkToConfirmYourEmailAddress'
              .tr;
  static String get enterYourMobileNumber => 'profile.enterYourMobileNumber'.tr;
  static String
      get weWillSendYouAMessageOnWhatsappWithAVerificationCodeToConfirmYourMobileNumber =>
          'profile.weWillSendYouAMessageOnWhatsappWithAVerificationCodeToConfirmYourMobileNumber'
              .tr;
  static String get editProfile => 'profile.editProfile'.tr;

  //--------------------- Settings ---------------------
  static String get language => 'settings.language'.tr;
  static String get selectLanguage => 'settings.selectLanguage'.tr;
  static String get country => 'settings.country'.tr;
  static String get displayCurrency => 'settings.displayCurrency'.tr;
  static String get paymentMethods => 'settings.paymentMethods'.tr;
  static String get english => 'settings.english'.tr;
  static String get arabic => 'settings.arabic'.tr;

  //--------------------- Favourites ---------------------
  static String get youDontHaveAnyFavorites =>
      'favourites.youDontHaveAnyFavorites'.tr;
  static String get addFavoritesToSeeThemHere =>
      'favourites.addFavoritesToSeeThemHere'.tr;
  static String get addedToFavorite => 'favourites.addedToFavorite'.tr;
  static String get select_direction_message_validator =>
      'select_direction_message_validator'.tr;
  static String get removedFromFavorite => 'favourites.removedFromFavorite'.tr;

  //--------------------- Hotels ---------------------
  static String get youDontHaveAnyHotels => 'hotels.youDontHaveAnyHotels'.tr;
  static String get useAnotherFilter => 'hotels.useAnotherFilter'.tr;

  //--------------------- Bookings ---------------------
  static String get youDontHaveAnyBookings =>
      'bookings.youDontHaveAnyBookings'.tr;
  static String get backAndStartSelect => 'bookings.backAndStartSelect'.tr;

  //--------------------- Login ---------------------
  static String get logOutSuccess => 'login.logOutSuccess'.tr;
  static String get logInSuccess => 'login.logInSuccess'.tr;

  //--------------------- About ---------------------
  static String get companyInfo => 'about.companyInfo'.tr;
  static String get companyInfoDisc => 'about.companyInfoDisc'.tr;
  static String get termsAndConditions => 'about.termsAndConditions'.tr;
  static String get termsDis => 'about.termsDis'.tr;
  static String get privacyPolicy => 'about.privacyPolicy'.tr;
  static String get appFeedback => 'about.appFeedback'.tr;
  static String get giveUsFeedback => 'about.giveUsFeedback'.tr;
  static String get rateTheApp => 'about.rateTheApp'.tr;
  static String get version => 'about.version'.tr;
  static String get yourVersionIsUpToDate => 'about.yourVersionIsUpToDate'.tr;
  static String get general => 'about.general'.tr;
  static String get hotels => 'about.hotels'.tr;
  static String get generalTermsDis => 'about.generalTermsDis'.tr;
}
