import 'dart:developer';
import 'dart:math';

import 'package:fandooq/core/extensions/assets_extension.dart';
import 'package:fandooq/core/const/assets.dart';
import 'package:fandooq/core/extensions/icons.dart';
import 'package:fandooq/widgets/app_bar/default_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sizer/flutter_sizer.dart';
import 'package:get/get.dart' hide ScreenType;

import 'screen_size.dart';

class ResponsiveUi extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveUi({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return FlutterSizer(
      builder: (context, orientation, deviceType) {
        return _getWidget(deviceType);
      },
    );
    final ScreenSize size = ScreenSize.getDeviceType();
    if (size == ScreenSize.desktop) {
      return desktop ?? mobile;
    } else if (size == ScreenSize.tablet) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }

  Widget _getWidget(ScreenType deviceType) {
    switch (deviceType) {
      case ScreenType.tablet:
        return tablet ?? mobile;
      case ScreenType.mobile:
        return mobile;
    }
  }
}

class ResponsiveWidth extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  const ResponsiveWidth({
    Key? key,
    required this.child,
    this.maxWidth = 1200,
    this.padding,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var padding = (size.width - maxWidth) / 2;
    return Padding(
      padding: this.padding ?? const EdgeInsets.symmetric(horizontal: 0),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: max(padding, 0)),
        child: child,
      ),
    );
  }
}

class ResponsiveWidthOption2 extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;
  final double? height;
  const ResponsiveWidthOption2({
    Key? key,
    required this.child,
    this.height,
    this.maxWidth = 700,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var padding = (size.width - maxWidth) / 2;
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: height ?? MediaQuery.of(context).size.height,
        maxWidth: maxWidth,
      ),
      child: child,
    );
  }
}

class ResponsiveWidthOption3 extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;
  final double? height;
  const ResponsiveWidthOption3({
    Key? key,
    required this.child,
    this.height,
    this.maxWidth = 400,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var padding = (size.width - maxWidth) / 2;

    return Column(
      children: [
        Material(
          child: DefaultAppBarTablet(),
        ),
        Expanded(
          child: Container(
            child: ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(12)),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(12))),
                      padding:
                          EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                      child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                        child: child,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    child: Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Assets.icon_fandooq_svg.svg(width: 50),
                              const SizedBox(
                                width: 20,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    "Fandooq",
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge!
                                        .copyWith(
                                            color: Colors.white, fontSize: 40),
                                  ),
                                  Text(
                                    "ui.webResponsiveMessage".tr,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
