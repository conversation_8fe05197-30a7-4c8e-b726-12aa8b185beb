

import '../../../feature_plus/auth/models/user_model.dart' show UserModel;
import '../../network_provider/networking.dart';

class UpdateProfileResponse extends BaseMappable{

  final UserModel? user;

  UpdateProfileResponse({this.user});

  @override
  Mappable fromJson(dynamic json) {
    return UpdateProfileResponse.fromJson(json);
  }

  factory UpdateProfileResponse.fromJson(dynamic data) {
    var json = data;
    return UpdateProfileResponse(
      user: json["data"] != null ? UserModel.fromJson(json["data"]) : null
    );
  }

  @override
  Map<String, dynamic> toJson() {
    // TODO: implement toJson
    throw UnimplementedError();
  }

}