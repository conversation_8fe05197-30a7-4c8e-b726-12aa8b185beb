// Models for location autocomplete API
import 'package:google_map_location_picker/google_map_location_picker.dart';

import '../../../core/network_provider/networking.dart';

class LocationSearchResponse extends BaseMappable {
  final bool success;
  final String? message;
  final List<LocationPlace> data;
  final LocationSearchMeta meta;

  LocationSearchResponse({
    required this.success,
    this.message,
    required this.data,
    required this.meta,
  });

  // Default constructor for NetworkHandler
  LocationSearchResponse.empty()
      : success = false,
        message = null,
        data = const [],
        meta = LocationSearchMeta(
          query: '',
          limit: 0,
          resultsCount: 0,
          apiProvider: '',
          version: '1.0',
          timestamp: '',
        );

  factory LocationSearchResponse.fromJson(Map<String, dynamic> json) {
    return LocationSearchResponse(
      success: json['success'] ?? false,
      message: json['message'],
      data: (json['data'] as List? ?? [])
          .map((item) => LocationPlace.fromJson(item))
          .toList(),
      meta: LocationSearchMeta.fromJson(json['meta'] ?? {}),
    );
  }

  @override
  Mappable fromJson(dynamic json) {
    return LocationSearchResponse.fromJson(json as Map<String, dynamic>);
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.map((item) => item.toJson()).toList(),
      'meta': meta.toJson(),
    };
  }
}

class LocationPlace {
  final String locationId;
  final String name;
  final String country;
  final String lat;
  final String lon;
  final String displayName;
  final String language;
  final String countryCode;
  final String? state;

  LocationPlace({
    required this.locationId,
    required this.name,
    required this.country,
    required this.lat,
    required this.lon,
    required this.displayName,
    required this.language,
    required this.countryCode,
    this.state,
  });

  factory LocationPlace.fromJson(Map<String, dynamic> json) {
    return LocationPlace(
      locationId: Uuid().generateV4(),
      name: json['name'] ?? '',
      country: json['country'] ?? '',
      lat: json['lat'] ?? '0.0',
      lon: json['lon'] ?? '0.0',
      displayName: json['displayName'] ?? '',
      language: json['language'] ?? 'en',
      countryCode: json['countryCode'] ?? '',
      state: json['state'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'country': country,
      'lat': lat,
      'lon': lon,
      'displayName': displayName,
      'language': language,
      'countryCode': countryCode,
      if (state != null) 'state': state,
    };
  }

  // Get latitude as double
  double get latitude => double.tryParse(lat) ?? 0.0;

  // Get longitude as double
  double get longitude => double.tryParse(lon) ?? 0.0;

  // Get formatted location string
  String get formattedLocation {
    if (state != null) {
      return '$name, $state, $country';
    }
    return '$name, $country';
  }

  // Get short location string
  String get shortLocation {
    return '$name, $country';
  }

  @override
  String toString() => displayName;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationPlace &&
        other.name == name &&
        other.country == country &&
        other.lat == lat &&
        other.lon == lon;
  }

  @override
  int get hashCode {
    return name.hashCode ^ country.hashCode ^ lat.hashCode ^ lon.hashCode;
  }
}

class LocationSearchMeta {
  final String query;
  final int limit;
  final int resultsCount;
  final String apiProvider;
  final String version;
  final String timestamp;

  LocationSearchMeta({
    required this.query,
    required this.limit,
    required this.resultsCount,
    required this.apiProvider,
    required this.version,
    required this.timestamp,
  });

  factory LocationSearchMeta.fromJson(Map<String, dynamic> json) {
    return LocationSearchMeta(
      query: json['query'] ?? '',
      limit: json['limit'] ?? 5,
      resultsCount: json['resultsCount'] ?? 0,
      apiProvider: json['apiProvider'] ?? '',
      version: json['version'] ?? '1.0',
      timestamp: json['timestamp'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'query': query,
      'limit': limit,
      'resultsCount': resultsCount,
      'apiProvider': apiProvider,
      'version': version,
      'timestamp': timestamp,
    };
  }
}

// Request model for location search
class LocationSearchRequest {
  final String query;
  final int limit;
  final String language;

  LocationSearchRequest({
    required this.query,
    this.limit = 5,
    this.language = 'en',
  });

  Map<String, dynamic> toQueryParams() {
    return {
      'query': query,
      'limit': limit.toString(),
      'language': language,
    };
  }
}
