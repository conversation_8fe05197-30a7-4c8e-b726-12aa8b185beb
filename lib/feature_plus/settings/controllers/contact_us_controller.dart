import 'package:get/get.dart';
import '../../../core/components/logger.dart';
import '../models/contact_info_model.dart';
import '../services/contact_service.dart';

/// 📞 **متحكم شاشة تواصل معنا**
/// يدير حالة شاشة معلومات التواصل
class ContactUsController extends GetxController {
  // Observable variables
  final Rx<ContactInfo?> contactInfo = Rx<ContactInfo?>(null);
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // Services
  late final ContactService _contactService;

  @override
  void onInit() {
    super.onInit();
    _contactService = Get.find<ContactService>();
    loadContactInfo();
  }

  /// تحميل معلومات التواصل
  Future<void> loadContactInfo() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      Log.debug('🔄 Loading contact information...');

      final info = await _contactService.getContactInfo();

      if (info != null) {
        contactInfo.value = info;
        Log.debug('✅ Contact information loaded successfully');
      } else {
        errorMessage.value = 'لا توجد معلومات تواصل متاحة';
        Log.warning('⚠️ No contact information available');
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ في تحميل معلومات التواصل';
      Log.error('❌ Error loading contact information: $e');

      // محاولة استخدام البيانات الافتراضية
      contactInfo.value = _getDefaultContactInfo();
    } finally {
      isLoading.value = false;
    }
  }

  /// إعادة تحميل معلومات التواصل
  Future<void> refreshContactInfo() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      Log.debug('🔄 Refreshing contact information...');

      final info = await _contactService.refreshContactInfo();

      if (info != null) {
        contactInfo.value = info;
        Log.debug('✅ Contact information refreshed successfully');
      } else {
        errorMessage.value = 'فشل في تحديث معلومات التواصل';
        Log.warning('⚠️ Failed to refresh contact information');
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ في تحديث معلومات التواصل';
      Log.error('❌ Error refreshing contact information: $e');

      Get.snackbar(
        'خطأ',
        'حدث خطأ في تحديث معلومات التواصل',
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// التحقق من حداثة البيانات
  Future<bool> isDataFresh() async {
    try {
      return await _contactService.isDataFresh();
    } catch (e) {
      Log.error('❌ Error checking data freshness: $e');
      return false;
    }
  }

  /// الحصول على تاريخ آخر تحديث
  Future<DateTime?> getLastUpdateTime() async {
    try {
      return await _contactService.getLastUpdateTime();
    } catch (e) {
      Log.error('❌ Error getting last update time: $e');
      return null;
    }
  }

  /// مسح البيانات المحفوظة محلياً
  Future<void> clearCache() async {
    try {
      await _contactService.clearCache();
      Log.debug('✅ Contact info cache cleared');

      Get.snackbar(
        'تم المسح',
        'تم مسح البيانات المحفوظة محلياً',
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
      );

      // إعادة تحميل البيانات
      await loadContactInfo();
    } catch (e) {
      Log.error('❌ Error clearing cache: $e');

      Get.snackbar(
        'خطأ',
        'حدث خطأ في مسح البيانات',
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  /// الحصول على معلومات تواصل افتراضية
  ContactInfo _getDefaultContactInfo() {
    return ContactInfo(
      phone: '+201068913950',
      email: '<EMAIL>',
      address: 'Riyadh, Saudi Arabia',
      whatsapp: 'https://wa.me/201068913950',
      instagram: 'https://instagram.com/fandooq',
      facebook: 'https://facebook.com/fandooq',
      twitter: 'https://twitter.com/fandooq',
      linkedin: 'https://linkedin.com/company/fandooq',
      youtube: 'https://youtube.com/@fandooq',
      website: 'https://fandooq.com',
      alternativePhone: '+966501234567',
      description:
          'Fandooq helps you book the best hotels across Saudi Arabia and the Middle East.',
      businessHours: 'Sunday to Thursday: 9:00 AM - 6:00 PM (GMT+3)',
    );
  }

  /// التحقق من صحة معلومات التواصل
  bool get isContactInfoValid {
    final info = contactInfo.value;
    return info != null && info.isValid;
  }

  /// الحصول على رسالة الحالة
  String get statusMessage {
    if (isLoading.value) {
      return 'جاري تحميل معلومات التواصل...';
    }

    if (errorMessage.value.isNotEmpty) {
      return errorMessage.value;
    }

    if (contactInfo.value == null) {
      return 'لا توجد معلومات تواصل متاحة';
    }

    return 'معلومات التواصل محدثة';
  }

  /// الحصول على لون حالة البيانات
  String get dataStatusColor {
    if (isLoading.value) {
      return 'blue';
    }

    if (errorMessage.value.isNotEmpty) {
      return 'red';
    }

    if (contactInfo.value == null) {
      return 'orange';
    }

    return 'green';
  }

  @override
  void onClose() {
    // تنظيف الموارد إذا لزم الأمر
    super.onClose();
  }
}
