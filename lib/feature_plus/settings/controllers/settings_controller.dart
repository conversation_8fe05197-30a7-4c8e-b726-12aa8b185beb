import 'dart:async';
import 'dart:io';
import 'package:fandooq/feature_plus/auth/routes/auth_routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/core.dart';
import '../../../core/components/logger.dart';
import '../../../core/services/confirmation_service.dart';
import '../../../core/setting_app/setting_app.dart';
import '../../auth/models/user_model.dart';
import '../models/contact_info_model.dart';
import '../services/contact_service.dart';
import '../../../core/controllers/user-app/user_controller.dart';
import '../../../core/router/app_pages.dart';
import '../../../core/enums/langs_enums.dart';
import '../../auth/controllers/auth_controller.dart';
import '../../auth/models/auth_models.dart';
import '../../bookings/integration/booking_integration.dart';
import '../models/settings_models.dart';
import '../models/language_model.dart';
import '../views/widgets/currency_selection_sheet.dart';
import '../screens/language_selection_bottom_sheet.dart';
import '../../hotels/controllers/hotels_controller_plus.dart';
import '../../hotels/controllers/hotel_details_controller_plus.dart';
import '../../location_search/controllers/location_search_controller.dart';

class SettingsController extends GetxController {
  // Observable variables
  final _appSettings = AppSettings.defaultSettings().obs;
  final _userProfile = UserProfile.guest().obs;
  final _appInfo = AppInfo.defaultInfo().obs;
  final _isLoading = false.obs;
  final _selectedLanguage = LanguageModel.defaultLanguage.obs;

  // Contact service
  late final ContactService _contactService;

  // Getters
  AppSettings get appSettings => _appSettings.value;
  UserProfile get userProfile => _userProfile.value;
  AppInfo get appInfo => _appInfo.value;
  bool get isLoading => _isLoading.value;
  Rx<LanguageModel> get selectedLanguage => _selectedLanguage;

  @override
  void onInit() {
    super.onInit();
    _contactService = Get.put(ContactService());
    _loadAppInfo();
    _loadUserProfile();
    _loadSettings();
  }

  /// Refresh user profile (call this after login/logout)
  Future<void> refreshUserProfile() async {
    await _loadUserProfile();
  }

  /// Load app information
  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _appInfo.value = AppInfo(
        appName: packageInfo.appName,
        version: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
        description:
            'Your trusted travel companion for finding the best hotels',
      );
    } catch (e) {
      print('❌ Error loading app info: $e');
    }
  }

  /// Load user profile from authentication systems
  Future<void> _loadUserProfile() async {
    try {
      // Check both authentication systems
      bool isLoggedIn = false;
      UserModel? user;
      AuthUser? authUser;

      // Check new AuthPlusController first
      if (Get.isRegistered<AuthPlusController>()) {
        final authController = Get.find<AuthPlusController>();
        if (authController.isAuthenticated) {
          isLoggedIn = true;
          authUser = authController.currentUser;
          print('✅ User authenticated via AuthPlusController');
        }
      }

      // Check existing UserController if not authenticated via new system
      if (!isLoggedIn && Get.isRegistered<UserController>()) {
        try {
          final userController = Get.find<UserController>();
          isLoggedIn = userController.isLoggedIn;
          if (isLoggedIn) {
            user = userController.getUser();
            print('✅ User authenticated via UserController');
          }
        } catch (e) {
          print('❌ Error accessing UserController: $e');
        }
      }

      if (isLoggedIn) {
        if (authUser != null) {
          // Use data from new auth system
          _userProfile.value = UserProfile(
            isSignedIn: true,
            name: '${authUser.firstName} ${authUser.lastName}'.trim().isNotEmpty
                ? '${authUser.firstName} ${authUser.lastName}'.trim()
                : 'مستخدم',
            email: '', // AuthUser doesn't have email field
            phone: authUser.phone,
            profileImage: null, // AuthUser doesn't have profile image
          );
        } else if (user != null) {
          // Use data from existing system
          _userProfile.value = UserProfile(
            isSignedIn: true,
            name: user.fullName.isNotEmpty ? user.fullName : 'مستخدم',
            email: user.email ?? '',
            phone: user.fullPhone,
            profileImage: user.photo,
          );
        } else {
          _userProfile.value = UserProfile.guest();
        }
      } else {
        _userProfile.value = UserProfile.guest();
      }
    } catch (e) {
      print('❌ Error loading user profile: $e');
      _userProfile.value = UserProfile.guest();
    }
  }

  /// Load app settings
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final language = prefs.getString('app_language') ?? 'ar';
      final notificationsEnabled =
          prefs.getBool('notifications_enabled') ?? true;
      final darkModeEnabled = prefs.getBool('dark_mode_enabled') ?? false;
      final currency = prefs.getString('app_currency') ?? 'USD';
      final locationServicesEnabled =
          prefs.getBool('location_services_enabled') ?? true;

      _appSettings.value = AppSettings(
        language: language,
        notificationsEnabled: notificationsEnabled,
        darkModeEnabled: darkModeEnabled,
        currency: currency,
        locationServicesEnabled: locationServicesEnabled,
      );

      // Load selected language model
      final languageModel =
          LanguageModel.getByCode(language) ?? LanguageModel.defaultLanguage;
      _selectedLanguage.value = languageModel;

      print('✅ Settings loaded successfully');
    } catch (e) {
      print('❌ Error loading settings: $e');
      _appSettings.value = AppSettings.defaultSettings();
    }
  }

  /// Save app settings
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString('app_language', _appSettings.value.language);
      await prefs.setBool(
          'notifications_enabled', _appSettings.value.notificationsEnabled);
      await prefs.setBool(
          'dark_mode_enabled', _appSettings.value.darkModeEnabled);
      await prefs.setString('app_currency', _appSettings.value.currency);
      await prefs.setBool('location_services_enabled',
          _appSettings.value.locationServicesEnabled);

      print('✅ Settings saved successfully');
    } catch (e) {
      print('❌ Error saving settings: $e');
    }
  }

  /// Change app language
  Future<void> changeLanguage(String languageCode) async {
    try {
      _isLoading.value = true;

      // Convert string to LangEnums
      LangEnums targetLanguage =
          languageCode == 'ar' ? LangEnums.ar : LangEnums.en;

      // Use existing language change functionality with specific language
      MyAppSettingUsesCase.changeLanguage(langEnums: targetLanguage);

      // Update settings
      _appSettings.value = _appSettings.value.copyWith(language: languageCode);

      // Save to preferences
      await _saveSettings();

      // Refresh all feature_plus controllers to apply language changes
      await _refreshFeaturePlusControllers();

      // Restart app to apply language changes
      await Future.delayed(const Duration(seconds: 1));
      Get.offAllNamed('/'); // Navigate to home to refresh UI
    } catch (e) {
      print('❌ Error changing language: $e');
      Core.showGlobalSnackBar(
        'خطأ في تغيير اللغة',
        backgroundColor: Colors.red,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Refresh all feature_plus controllers to apply language changes
  Future<void> _refreshFeaturePlusControllers() async {
    try {
      print('🔄 Refreshing feature_plus controllers for language change...');

      // Refresh hotels controller
      try {
        final hotelsController = Get.find<HotelsControllerPlus>();
        hotelsController.update();
        print('✅ Hotels controller refreshed');
      } catch (e) {
        print('⚠️ Hotels controller not found: $e');
      }

      // Refresh hotel details controller
      try {
        final hotelDetailsController = Get.find<HotelDetailsControllerPlus>();
        hotelDetailsController.update();
        print('✅ Hotel details controller refreshed');
      } catch (e) {
        print('⚠️ Hotel details controller not found: $e');
      }

      // Refresh location search controller
      try {
        final locationController = Get.find<LocationSearchController>();
        locationController.update();
        print('✅ Location search controller refreshed');
      } catch (e) {
        print('⚠️ Location search controller not found: $e');
      }

      // Refresh auth controller
      try {
        final authController = Get.find<AuthPlusController>();
        authController.update();
        print('✅ Auth controller refreshed');
      } catch (e) {
        print('⚠️ Auth controller not found: $e');
      }

      print('✅ All feature_plus controllers refreshed successfully');
    } catch (e) {
      print('❌ Error refreshing feature_plus controllers: $e');
    }
  }

  /// Toggle notifications
  Future<void> toggleNotifications(bool enabled) async {
    try {
      _appSettings.value =
          _appSettings.value.copyWith(notificationsEnabled: enabled);

      // Save to preferences
      await _saveSettings();

      // Update notification settings in system
      if (enabled) {
        // Request notification permissions
        await _requestNotificationPermissions();
      }

      Core.showGlobalSnackBar(
        enabled ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
        backgroundColor: enabled ? Colors.green : Colors.orange,
      );
    } catch (e) {
      print('❌ Error toggling notifications: $e');
      Core.showGlobalSnackBar(
        'خطأ في تحديث إعدادات الإشعارات',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Request notification permissions
  Future<void> _requestNotificationPermissions() async {
    try {
      // This would typically use firebase_messaging or local_notifications
      // For now, just show a message
      print('📱 Requesting notification permissions...');
    } catch (e) {
      print('❌ Error requesting notification permissions: $e');
    }
  }

  /// Toggle dark mode
  Future<void> toggleDarkMode(bool enabled) async {
    try {
      _appSettings.value =
          _appSettings.value.copyWith(darkModeEnabled: enabled);

      // Save to preferences
      await _saveSettings();

      // Apply theme change
      Get.changeThemeMode(enabled ? ThemeMode.dark : ThemeMode.light);

      Core.showGlobalSnackBar(
        enabled ? 'تم تفعيل الوضع الليلي' : 'تم إيقاف الوضع الليلي',
        backgroundColor: enabled ? Colors.grey[800]! : Colors.blue,
      );
    } catch (e) {
      print('❌ Error toggling dark mode: $e');
      Core.showGlobalSnackBar(
        'خطأ في تحديث الوضع الليلي',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Sign in
  Future<void> signIn() async {
    try {
      _isLoading.value = true;

      // Navigate to new auth plus system
      final result = await AuthNavigator.navigateToLogin();

      if (result != null) {
        // User successfully logged in, refresh user profile
        await refreshUserProfile();

        Core.showGlobalSnackBar(
          'تم تسجيل الدخول بنجاح',
          backgroundColor: Colors.green,
        );
      }
    } catch (e) {
      print('❌ Error signing in: $e');
      Core.showGlobalSnackBar(
        'خطأ في تسجيل الدخول',
        backgroundColor: Colors.red,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    // Show confirmation dialog using ConfirmationService
    final confirmed = await ConfirmationService.showLogoutConfirmation();

    if (confirmed == true) {
      try {
        _isLoading.value = true;

        // Use existing logout functionality
        final userController = UserController.to;
        await userController.logOutWithoutMessage();

        // Update user profile to guest
        _userProfile.value = UserProfile.guest();

        Core.showGlobalSnackBar(
          'تم تسجيل الخروج بنجاح',
          backgroundColor: Colors.green,
        );
      } catch (e) {
        print('❌ Error signing out: $e');
        Core.showGlobalSnackBar(
          'خطأ في تسجيل الخروج',
          backgroundColor: Colors.red,
        );
      } finally {
        _isLoading.value = false;
      }
    }
  }

  /// Show bookings
  void showBookings() {
    final userController = UserController.to;

    if (!userController.isLoggedIn) {
      Core.showGlobalSnackBar(
        'يجب تسجيل الدخول أولاً لعرض الحجوزات',
        backgroundColor: Colors.orange,
      );
      return;
    }

    // Navigate to My Bookings screen
    try {
      // Use the BookingIntegration helper to show My Bookings
      BookingIntegration.showMyBookings();
    } catch (e) {
      print('❌ Error opening My Bookings: $e');
      // Fallback: show dialog if route doesn't exist
      Get.dialog(
        AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.book_online, color: Colors.blue),
              SizedBox(width: 8),
              Text('حجوزاتي'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('لا توجد حجوزات حالياً'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Column(
                    children: [
                      Icon(Icons.hotel, size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text(
                        'ابدأ في البحث عن الفنادق لإنشاء حجوزاتك الأولى',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    }
  }

  /// Show about app screen
  void showAboutApp() {
    // Navigate to the About screen using new Terms system
    Get.toNamed(Routes.COMPANY_INFO);
  }

  /// Contact support - Navigate to contact screen
  void contactSupport() {
    Get.toNamed(Routes.CONTACT_US);
  }

  /// Contact via email
  Future<void> _contactViaEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: 'support@fandooq',
      query: 'subject=دعم تطبيق فندوق&body=مرحباً، أحتاج مساعدة في...',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        Core.showGlobalSnackBar(
          'لا يمكن فتح تطبيق البريد الإلكتروني',
          backgroundColor: Colors.red,
        );
      }
    } catch (e) {
      Core.showGlobalSnackBar(
        'خطأ في فتح البريد الإلكتروني',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Contact via WhatsApp
  Future<void> _contactViaWhatsApp() async {
    final Uri whatsappUri = Uri.parse(
      'https://wa.me/+966123456789?text=مرحباً، أحتاج مساعدة في تطبيق فندوق',
    );

    try {
      if (await canLaunchUrl(whatsappUri)) {
        await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
      } else {
        Core.showGlobalSnackBar(
          'لا يمكن فتح واتساب',
          backgroundColor: Colors.red,
        );
      }
    } catch (e) {
      Core.showGlobalSnackBar(
        'خطأ في فتح واتساب',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Rate app
  Future<void> rateApp() async {
    // Official app store URLs
    final Uri appStoreUri = Uri.parse(
      'https://apps.apple.com/app/id6480403280',
    );
    final Uri playStoreUri = Uri.parse(
      'https://play.google.com/store/apps/details?id=com.fandooq',
    );

    try {
      // Use Platform.isIOS for accurate platform detection
      final Uri storeUri = Platform.isIOS ? appStoreUri : playStoreUri;

      if (await canLaunchUrl(storeUri)) {
        await launchUrl(storeUri, mode: LaunchMode.externalApplication);

        // Show success message
        Core.showGlobalSnackBar(
          Platform.isIOS
              ? 'تم فتح App Store لتقييم التطبيق'
              : 'تم فتح Google Play لتقييم التطبيق',
          backgroundColor: Colors.green,
        );
      } else {
        // Fallback: show rating dialog
        _showRatingDialog();
      }
    } catch (e) {
      Log.error('❌ Error opening app store: $e');
      _showRatingDialog();
    }
  }

  /// Show rating dialog
  void _showRatingDialog() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.star_rate, color: Colors.amber),
            SizedBox(width: 8),
            Text('تقييم التطبيق'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('ما رأيك في تطبيق فندوق؟'),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
                Icon(Icons.star, color: Colors.amber, size: 32),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('لاحقاً'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Core.showGlobalSnackBar(
                'شكراً لتقييمك! 🌟',
                backgroundColor: Colors.green,
              );
            },
            child: const Text('تقييم'),
          ),
        ],
      ),
    );
  }

  /// Share app
  Future<void> shareApp() async {
    try {
      // Official app store URLs
      const String iosUrl = 'https://apps.apple.com/app/id6480403280';
      const String androidUrl =
          'https://play.google.com/store/apps/details?id=com.fandooq';

      final appLink = Platform.isIOS ? iosUrl : androidUrl;

      // Create share message with URLs
      const String shareText =
          'Check out the Fandooq App for amazing hotel deals!\n\n🏨 Discover the best hotels at great prices\n✅ Easy and fast search\n✅ All-inclusive pricing\n✅ Secure and guaranteed booking\n\n📱 Download the app now:\n\nAndroid: $androidUrl\n\niOS: $iosUrl\n\n#Fandooq #Travel #Hotels #Booking';

      await Share.share(
        shareText,
        subject: 'Fandooq App - Your Best Hotel Booking Companion',
      );
    } catch (e) {
      Log.error('❌ Error sharing app: $e');
      Core.showGlobalSnackBar(
        'خطأ في مشاركة التطبيق',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Change currency
  Future<bool> changeCurrency() async {
    final previousCurrency = _appSettings.value.currency;
    await _showCurrencyBottomSheet();
    final currentCurrency = _appSettings.value.currency;

    // Return true if currency actually changed
    final hasChanged = previousCurrency != currentCurrency;
    print(
        '💱 Currency change check: $previousCurrency → $currentCurrency (changed: $hasChanged)');
    return hasChanged;
  }

  /// Show language selection
  Future<void> showLanguageSelection() async {
    await _showLanguageBottomSheet();
  }

  /// Update language
  Future<void> updateLanguage(LanguageModel language) async {
    try {
      _selectedLanguage.value = language;
      _appSettings.value = _appSettings.value.copyWith(language: language.code);
      // await _saveSettings();
      await changeLanguage(language.code);
      print('✅ Language updated to: ${language.name} (${language.code})');
    } catch (e) {
      print('❌ Error updating language: $e');
      Core.showGlobalSnackBar(
        'Error changing language',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Show language selection bottom sheet
  Future<void> _showLanguageBottomSheet() async {
    try {
      print('🌐 Opening language selection bottom sheet...');

      final result = await showModalBottomSheet(
        context: Get.context!,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        enableDrag: true,
        isDismissible: true,
        builder: (context) => const LanguageSelectionBottomSheet(),
      );

      print('🌐 Language selection result: $result');
    } catch (e) {
      print('❌ Error showing language bottom sheet: $e');
      Core.showGlobalSnackBar(
        'خطأ في فتح قائمة اللغات',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Show currency selection bottom sheet
  Future<void> _showCurrencyBottomSheet() async {
    await showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: CurrencySelectionSheet(
            currentCurrency: _appSettings.value.currency,
            onCurrencySelected: (currency) async {
              _appSettings.value = _appSettings.value.copyWith(
                currency: currency['code']!,
              );
              await _saveSettings();

              // Close the bottom sheet
              if (context.mounted) {
                Navigator.of(context).pop();
              }

              // Note: Hotel refresh is now handled by the calling screen
              // to avoid unnecessary API calls when currency doesn't change

              // Notify geolocation hotel search controller
              // try {
              //   // final geoController =
              //   //     Get.find<GeoLocationHotelSearchController>();
              //   // geoController.updateCurrency(currency['code']!);
              // } catch (e) {
              //   print('⚠️ Geolocation hotel search controller not found');
              // }
            },
            scrollController: scrollController,
          ),
        ),
      ),
    );
  }

  /// Toggle location services
  Future<void> toggleLocationServices(bool enabled) async {
    try {
      _appSettings.value = _appSettings.value.copyWith(
        locationServicesEnabled: enabled,
      );
      await _saveSettings();
    } catch (e) {
      Core.showGlobalSnackBar(
        'خطأ في تحديث إعدادات الموقع',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Clear app cache
  Future<void> clearCache() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.delete_sweep, color: Colors.orange),
            SizedBox(width: 8),
            Text('مسح البيانات المؤقتة'),
          ],
        ),
        content: const Text(
          'سيتم مسح جميع البيانات المؤقتة المحفوظة. هذا قد يحسن أداء التطبيق.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(foregroundColor: Colors.orange),
            child: const Text('مسح'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        _isLoading.value = true;

        // Simulate cache clearing
        await Future.delayed(const Duration(seconds: 2));

        Core.showGlobalSnackBar(
          'تم مسح البيانات المؤقتة بنجاح',
          backgroundColor: Colors.green,
        );
      } catch (e) {
        Core.showGlobalSnackBar(
          'خطأ في مسح البيانات المؤقتة',
          backgroundColor: Colors.red,
        );
      } finally {
        _isLoading.value = false;
      }
    }
  }

  /// Show privacy policy
  void showPrivacyPolicy() {
    // Navigate to the Privacy Policy screen using new Terms system
    Get.toNamed(Routes.PRIVACY);
  }

  /// Show terms of service
  void showTermsOfService() {
    // Navigate to the Terms and Conditions screen using new Terms system
    Get.toNamed(Routes.TERMS);
  }
}
