import 'package:fandooq/feature_plus/settings/views/settings_screen.dart';
import 'package:get/get.dart';

import '../../../core/router/app_pages.dart';

class SettingsNavigator {

  static String get settings => Routes.SETTINGS;

  static List<GetPage> routes = [
    GetPage(
      name: settings,
      page: () => const SettingsScreen(),
    ),
  ];

  static void navigateToSettings() {
    Get.toNamed(settings);
  }

}
