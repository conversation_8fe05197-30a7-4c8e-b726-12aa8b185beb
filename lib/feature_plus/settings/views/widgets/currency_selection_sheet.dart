import 'package:fandooq/core/extensions/string.dart';
import 'package:flutter/material.dart';
import '../../../../core/theme/color_manager.dart';
import 'package:get/get.dart';

class CurrencySelectionSheet extends StatefulWidget {
  final String currentCurrency;
  final Function(Map<String, String>) onCurrencySelected;
  final ScrollController scrollController;

  const CurrencySelectionSheet({
    super.key,
    required this.currentCurrency,
    required this.onCurrencySelected,
    required this.scrollController,
  });

  @override
  State<CurrencySelectionSheet> createState() => _CurrencySelectionSheetState();
}

class _CurrencySelectionSheetState extends State<CurrencySelectionSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, String>> _filteredCurrencies = [];

  final List<Map<String, String>> _allCurrencies = [
    {
      'code': 'USD',
      'name': 'currency.usd'.tr,
      'symbol': 'USD',
      'country': 'currency.country.us'.tr
    },
    {
      'code': 'EUR',
      'name': 'currency.eur'.tr,
      'symbol': 'EUR',
      'country': 'currency.country.eu'.tr
    },
    {
      'code': 'SAR',
      'name': 'currency.sar'.tr,
      'symbol': 'SAR', // ✅ تم تعديلها من AED إلى SAR
      'country': 'currency.country.sa'.tr
    },
    {
      'code': 'AED',
      'name': 'currency.aed'.tr,
      'symbol': 'AED',
      'country': 'currency.country.ae'.tr
    },
    {
      'code': 'EGP',
      'name': 'currency.egp'.tr,
      'symbol': 'EGP',
      'country': 'currency.country.eg'.tr
    },
    {
      'code': 'QAR',
      'name': 'currency.qar'.tr,
      'symbol': 'QAR',
      'country': 'currency.country.qa'.tr
    },
    {
      'code': 'KWD',
      'name': 'currency.kwd'.tr,
      'symbol': 'KWD',
      'country': 'currency.country.kw'.tr
    },
    {
      'code': 'BHD',
      'name': 'currency.bhd'.tr,
      'symbol': 'BHD',
      'country': 'currency.country.bh'.tr
    },
    {
      'code': 'OMR',
      'name': 'currency.omr'.tr,
      'symbol': 'OMR',
      'country': 'currency.country.om'.tr
    },
    {
      'code': 'JOD',
      'name': 'currency.jod'.tr,
      'symbol': 'JOD',
      'country': 'currency.country.jo'.tr
    },
    {
      'code': 'LBP',
      'name': 'currency.lbp'.tr,
      'symbol': 'LBP',
      'country': 'currency.country.lb'.tr
    },
    {
      'code': 'GBP',
      'name': 'currency.gbp'.tr,
      'symbol': '£',
      'country': 'currency.country.gb'.tr
    },
    {
      'code': 'JPY',
      'name': 'currency.jpy'.tr,
      'symbol': '¥',
      'country': 'currency.country.jp'.tr
    },
    {
      'code': 'CNY',
      'name': 'currency.cny'.tr,
      'symbol': '¥',
      'country': 'currency.country.cn'.tr
    },
    {
      'code': 'INR',
      'name': 'currency.inr'.tr,
      'symbol': '₹',
      'country': 'currency.country.in'.tr
    },
    {
      'code': 'PKR',
      'name': 'currency.pkr'.tr,
      'symbol': 'PKR',
      'country': 'currency.country.pk'.tr
    },
    {
      'code': 'TRY',
      'name': 'currency.try'.tr,
      'symbol': '₺',
      'country': 'currency.country.tr'.tr
    },
    {
      'code': 'CAD',
      'name': 'currency.cad'.tr,
      'symbol': r'CAD',
      'country': 'currency.country.ca'.tr
    },
    {
      'code': 'AUD',
      'name': 'currency.aud'.tr,
      'symbol': r'AUD',
      'country': 'currency.country.au'.tr
    },
    {
      'code': 'CHF',
      'name': 'currency.chf'.tr,
      'symbol': 'CHF',
      'country': 'currency.country.ch'.tr
    },
  ];

  @override
  void initState() {
    super.initState();
    _filteredCurrencies = List.from(_allCurrencies);
    _searchController.addListener(_filterCurrencies);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterCurrencies() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      if (query.isEmpty) {
        _filteredCurrencies = List.from(_allCurrencies);
      } else {
        _filteredCurrencies = _allCurrencies.where((currency) {
          return currency['name']!.toLowerCase().contains(query) ||
              currency['code']!.toLowerCase().contains(query) ||
              currency['country']!.toLowerCase().contains(query) ||
              currency['symbol']!.toLowerCase().contains(query);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Handle bar
        Container(
          margin: const EdgeInsets.only(top: 8),
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(2),
          ),
        ),

        // Header
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              const Icon(
                Icons.attach_money,
                color: ColorManager.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'currencySelection.header'.tr,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
        ),

        // Search bar
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: TextField(
              controller: _searchController,
              textDirection: TextDirection.rtl,
              decoration: InputDecoration(
                hintText: 'currencySelection.searchHint'.tr,
                hintStyle: TextStyle(color: Colors.grey[500]),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.grey[600],
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                        },
                        icon: Icon(
                          Icons.clear,
                          color: Colors.grey[600],
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
        ),

        // Results count
        if (_searchController.text.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Text(
              '${_filteredCurrencies.length} ${'currencySelection.resultsCount'.tr}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),

        const SizedBox(height: 16),

        // Currency list
        Expanded(
          child: _filteredCurrencies.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  controller: widget.scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  itemCount: _filteredCurrencies.length,
                  itemBuilder: (context, index) {
                    final currency = _filteredCurrencies[index];
                    final isSelected =
                        currency['code'] == widget.currentCurrency;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? ColorManager.primary.withOpacity(0.1)
                            : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? ColorManager.primary
                              : Colors.grey[200]!,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        leading: Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: ColorManager.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Center(
                            child: Text(
                              currency['symbol']!,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: ColorManager.primary,
                              ),
                            ),
                          ),
                        ),
                        title: Text(
                          currency['name']!,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isSelected
                                ? ColorManager.primary
                                : Colors.black87,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            Text(
                              currency['code']!,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              currency['country']!,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                        trailing: isSelected
                            ? const Icon(
                                Icons.check_circle,
                                color: ColorManager.primary,
                                size: 24,
                              )
                            : Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.grey[400],
                                size: 16,
                              ),
                        onTap: () => widget.onCurrencySelected(currency),
                      ),
                    );
                  },
                ),
        ),

        // Bottom padding
        const SizedBox(height: 20),
      ],
    );
  }

  /// Build empty state when no currencies found
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'currencySelection.noResults'.tr,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'currencySelection.tryDifferentKeywords'.tr,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          TextButton.icon(
            onPressed: () {
              _searchController.clear();
            },
            icon: const Icon(Icons.refresh),
            label: Text('currencySelection.clearSearch'.tr),
            style: TextButton.styleFrom(
              foregroundColor: ColorManager.primary,
            ),
          ),
        ],
      ),
    );
  }
}
