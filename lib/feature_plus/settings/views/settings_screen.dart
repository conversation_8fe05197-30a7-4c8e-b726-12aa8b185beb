import 'package:fandooq/core/router/app_pages.dart';
import 'package:fandooq/feature_plus/auth/routes/auth_routes.dart';
import 'package:fandooq/feature_plus/profile/views/edit_profile_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/theme/color_manager.dart';
import '../controllers/settings_controller.dart';
import '../../user/controllers/user_controller_plus.dart';
import 'widgets/settings_section.dart';
import 'widgets/settings_tile.dart';
import 'widgets/user_profile_header.dart';
import '../../profile/views/edit_profile_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize controller
    final controller = Get.put(SettingsController());

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Text('settings.title'.tr),
        backgroundColor: ColorManager.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // User Profile Header
              UserProfileHeader(controller: controller),

              const SizedBox(height: 24),

              // Account Section
              GetBuilder<UserControllerPlus>(
                builder: (userController) {
                  return SettingsSection(
                    title: 'settings.account'.tr,
                    icon: Icons.person,
                    children: [
                      if (!userController.isAuthenticated) ...[
                        SettingsTile(
                          title: 'settings.login'.tr,
                          subtitle: 'settings.loginToAccessFeatures'.tr,
                          icon: Icons.login,
                          onTap: () => Get.toNamed('/auth-plus'),
                        ),
                      ] else ...[
                        SettingsTile(
                          title: 'settings.editProfile'.tr,
                          subtitle: 'settings.managePersonalInfo'.tr,
                          icon: Icons.person_outline,
                          onTap: () {
                            Get.toNamed(Routes.EDIT_PROFILE);
                          },
                        ),
                        SettingsTile(
                          title: 'settings.bookings'.tr,
                          subtitle: 'settings.viewAllBookings'.tr,
                          icon: Icons.book_online,
                          onTap: controller.showBookings,
                        ),
                        SettingsTile(
                          title: 'settings.logout'.tr,
                          subtitle: 'settings.logoutFromAccount'.tr,
                          icon: Icons.logout,
                          onTap: () async {
                            await userController.logoutWithConfirmation();
                          },
                          isDestructive: true,
                        ),
                        SettingsTile(
                          title: 'deleteAccount.title'.tr,
                          subtitle: 'settings.deleteAccountSubtitle'.tr,
                          icon: Icons.delete_forever,
                          onTap: (){
                            AuthNavigator.navigateToDeleteAccount();
                          },
                          isDestructive: true,
                        ),
                      ],
                    ],
                  );
                },
              ),

              const SizedBox(height: 16),

              // App Settings Section
              SettingsSection(
                title: 'settings.appSettings'.tr,
                icon: Icons.settings,
                children: [
                  Obx(() => SettingsTile(
                        title: 'settings.language'.tr,
                        subtitle: controller.selectedLanguage.value.nativeName,
                        icon: Icons.language,
                        trailing: Text(
                          controller.selectedLanguage.value.flag,
                          style: const TextStyle(fontSize: 20),
                        ),
                        onTap: controller.showLanguageSelection,
                      )),
                  SettingsTile(
                    title: 'settings.currency'.tr,
                    subtitle: controller.appSettings.currency,
                    icon: Icons.attach_money,
                    onTap: controller.changeCurrency,
                  ),
                  SettingsTile(
                    title: 'settings.notifications'.tr,
                    subtitle: 'settings.manageAppNotifications'.tr,
                    icon: Icons.notifications,
                    trailing: Switch(
                      value: controller.appSettings.notificationsEnabled,
                      onChanged: controller.toggleNotifications,
                      activeColor: ColorManager.primary,
                    ),
                  ),
                  SettingsTile(
                    title: 'settings.locationServices'.tr,
                    subtitle: 'settings.allowLocationAccess'.tr,
                    icon: Icons.location_on,
                    trailing: Switch(
                      value: controller.appSettings.locationServicesEnabled,
                      onChanged: controller.toggleLocationServices,
                      activeColor: ColorManager.primary,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Support Section
              SettingsSection(
                title: 'settings.supportAndHelp'.tr,
                icon: Icons.help,
                children: [
                  SettingsTile(
                    title: 'settings.contactUs'.tr,
                    subtitle: 'settings.getHelpAndSupport'.tr,
                    icon: Icons.contact_support,
                    onTap: controller.contactSupport,
                  ),
                  SettingsTile(
                    title: 'settings.rateApp'.tr,
                    subtitle: 'settings.rateYourExperience'.tr,
                    icon: Icons.star_rate,
                    onTap: controller.rateApp,
                  ),
                  SettingsTile(
                    title: 'settings.shareApp'.tr,
                    subtitle: 'settings.shareWithFriends'.tr,
                    icon: Icons.share,
                    onTap: controller.shareApp,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Legal Section
              SettingsSection(
                title: 'settings.legalAndPrivacy'.tr,
                icon: Icons.gavel,
                children: [
                  SettingsTile(
                    title: 'settings.privacyPolicy'.tr,
                    subtitle: 'settings.howWeProtectYourData'.tr,
                    icon: Icons.privacy_tip,
                    onTap: controller.showPrivacyPolicy,
                  ),
                  SettingsTile(
                    title: 'settings.termsAndConditions'.tr,
                    subtitle: 'settings.appUsageTerms'.tr,
                    icon: Icons.description,
                    onTap: controller.showTermsOfService,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // About Section
              SettingsSection(
                title: 'settings.aboutApp'.tr,
                icon: Icons.info,
                children: [
                  SettingsTile(
                    title: 'settings.appInfo'.tr,
                    subtitle:
                        '${'settings.version'.tr} ${controller.appInfo.version} (${controller.appInfo.buildNumber})',
                    icon: Icons.info_outline,
                    onTap: controller.showAboutApp,
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // App Version Footer
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      controller.appInfo.appName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${'settings.version'.tr} ${controller.appInfo.version} (${controller.appInfo.buildNumber})',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'settings.copyright'.tr,
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
