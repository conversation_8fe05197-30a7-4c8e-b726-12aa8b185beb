part of '../../about.dart';

class AboutBinding extends Bindings {
  @override
  void dependencies() {
    // Remote data source
    Get.lazyPut<AboutRemoteDataSource>(() => AboutRemoteDataSourceImp());

    // Local data source
    Get.lazyPut<AboutLocalDataSource>(() => AboutLocalDataSourceImp());

    // Repository
    Get.lazyPut<AboutRepository>(() => AboutRepositoryImp());

    // Use cases
    Get.put(TermsDataUseCase());
    // Controller
    Get.lazyPut(() => AboutController());
  }

  void delete() {
    Get.delete<AboutRemoteDataSourceImp>();
    Get.delete<AboutLocalDataSourceImp>();
    Get.delete<AboutRepositoryImp>();
    Get.delete<TermsDataUseCase>();
    Get.delete<AboutController>();
  }
}
