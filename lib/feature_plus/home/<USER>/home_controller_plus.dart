import 'package:fandooq/core/data_base/features/global_app/last_location.dart';
import 'package:fandooq/feature_plus/feature_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/home_models_plus.dart';
import '../services/home_service_plus.dart';

import '../../hotels/models/hotel_models.dart';
import '../../hotels/models/hotels_data_search.dart';
import '../models/guests_search.dart';
import '../../../core/components/free_place_search/lib/models/location_info.dart';
import '../../../core/components/free_place_search/lib/models/geo_point.dart';
import '../../../core/components/free_place_search/lib/models/address.dart';
import 'package:uuid/uuid.dart';
import '../../../core/models/availability/request/guest_date_request.dart'
    as core_guest;

class HomeControllerPlus extends GetxController {

  static HomeControllerPlus get to => Get.find();

  // Services
  late final HomeServicePlus _homeService;

  static String locationUpdateBuilder = "locationUpdateBuilder";
  static String datesUpdateBuilder = "datesUpdateBuilder";
  static String guestsUpdateBuilder = "guestsUpdateBuilder";

  static String searchBoxBuilder = "searchBoxBuilder";

  // Loading states
  bool _isLoading = false;
  bool _isLoadingPopularDestinations = false;
  bool _isLoadingNearbyHotels = false;
  bool _isInitialDataLoaded = false;
  bool _isInitialized = false;

  // Data
  HotelRequest _hotelRequest = HotelRequest.defaultRequest();
  List<PopularDestinationPlus> _popularDestinations = [];
  List<HotelPlus> _nearbyHotels = [];
  List<RecentSearchPlus> _recentSearches = [];

  HotelRequest get getRequest => _hotelRequest;


  // Getters
  bool get isLoading => _isLoading;
  bool get isLoadingPopularDestinations => _isLoadingPopularDestinations;
  bool get isLoadingNearbyHotels => _isLoadingNearbyHotels;
  HotelRequest get hotelRequest => _hotelRequest;
  List<PopularDestinationPlus> get popularDestinations => _popularDestinations;
  List<HotelPlus> get nearbyHotels => _nearbyHotels;
  List<RecentSearchPlus> get recentSearches => _recentSearches;

  // Search validation
  bool get canSearch =>
      _hotelRequest.location != null &&
      _hotelRequest.stay?.checkIn != null &&
      _hotelRequest.stay?.checkOut != null &&
      _hotelRequest.rooms.isNotEmpty;

  // Helper getters for UI compatibility
  String? get locationName => _hotelRequest.location?.name;
  DateTime? get checkInDate => _hotelRequest.stay?.checkIn;
  DateTime? get checkOutDate => _hotelRequest.stay?.checkOut;
  int get totalAdults =>
      _hotelRequest.rooms.fold(0, (sum, room) => sum + room.adults.length);
  int get totalChildren =>
      _hotelRequest.rooms.fold(0, (sum, room) => sum + room.children.length);
  int get totalRooms => _hotelRequest.rooms.length;

  String get guestsText {
    final adults = totalAdults;
    final children = totalChildren;
    final rooms = totalRooms;

    String text = [
      adults,
      "home.adults".tr
    ].join(" ");
    if (children > 0) {
      text += [
        '، ',
        children,
        "home.children".tr
      ].join(" ");
    }
    text += [
      '، ',
      rooms,
      "home.rooms".tr
    ].join(" ");

    return text;
  }

  @override
  void onInit() {
    if (_isInitialized) {
      print(
          '⚠️ [HOME_CONTROLLER] onInit called again, skipping initialization');
      return;
    }

    super.onInit();
    print('🚀 [HOME_CONTROLLER] onInit called - initializing...');
    _isInitialized = true;

    _initializeDependencies();
    _initializeDefaultSearch();

    // Load data only once
    if (!_isInitialDataLoaded) {
      _loadInitialData();
    }
  }

  /// Initialize dependencies
  void _initializeDependencies() {
    try {
      _homeService = Get.find<HomeServicePlus>();
      print('✅ [HOME_CONTROLLER] Found existing HomeServicePlus');
    } catch (e) {
      // If not found, put it ONLY if not already registered
      if (!Get.isRegistered<HomeServicePlus>()) {
        Get.put(HomeServicePlus(), permanent: true);
        print('✅ [HOME_CONTROLLER] Created new HomeServicePlus');
      }
      _homeService = Get.find<HomeServicePlus>();
    }
  }

  /// Initialize default search parameters
  void _initializeDefaultSearch() {
    final now = DateTime.now();
    _hotelRequest = HotelRequest(
      location: DBLastLocation.getLastLocation(),
      stay: StayRequest(
        checkIn: now.add(const Duration(days: 1)),
        checkOut: now.add(const Duration(days: 2)),
      ),
      rooms: [
        RoomRequestModel.defaultRoom(),
      ],
    );
    update();
  }

  /// Load initial data
  Future<void> _loadInitialData() async {
    if (_isInitialDataLoaded) {
      print('🔄 [HOME_CONTROLLER] Initial data already loaded, skipping...');
      return;
    }

    print('🚀 [HOME_CONTROLLER] Loading initial data...');
    _isInitialDataLoaded = true;

    try {
      await Future.wait([
        // _loadPopularDestinations(), // مخفي مؤقتاً
        _loadRecentSearches(),
        _loadNearbyHotels(),
      ]);
      print('✅ [HOME_CONTROLLER] Initial data loaded successfully');
    } catch (e) {
      print('❌ [HOME_CONTROLLER] Error loading initial data: $e');
      _isInitialDataLoaded = false; // Reset on error
    }
  }

  /// Load popular destinations
  Future<void> _loadPopularDestinations() async {
    if (_isLoadingPopularDestinations) {
      print(
          '🔄 [HOME_CONTROLLER] Popular destinations already loading, skipping...');
      return;
    }

    try {
      _isLoadingPopularDestinations = true;
      update();

      print('🌍 [HOME_CONTROLLER] Starting to load popular destinations...');
      final destinations = await _homeService.getPopularDestinations();
      _popularDestinations = destinations;
      print(
          '✅ [HOME_CONTROLLER] Popular destinations loaded: ${destinations.length}');
    } catch (e) {
      print('❌ [HOME_CONTROLLER] Error loading popular destinations: $e');
      _popularDestinations = [];
    } finally {
      _isLoadingPopularDestinations = false;
      update();
    }
  }

  /// Load nearby hotels
  Future<void> _loadNearbyHotels() async {
    if (_isLoadingNearbyHotels) {
      print('🔄 [HOME_CONTROLLER] Nearby hotels already loading, skipping...');
      return;
    }

    try {
      _isLoadingNearbyHotels = true;
      update();

      print('🏨 [HOME_CONTROLLER] Starting to load nearby hotels...');
      // Get user location if available
      // For now, use Cairo coordinates as default
      final hotels = await _homeService.getNearbyHotels(
        latitude: 30.033333, // Cairo coordinates from your API
        longitude: 31.233334,
        limit: 20,
      );
      _nearbyHotels = hotels;
      print('✅ [HOME_CONTROLLER] Nearby hotels loaded: ${hotels.length}');
    } catch (e) {
      print('❌ [HOME_CONTROLLER] Error loading nearby hotels: $e');
      _nearbyHotels = [];
    } finally {
      _isLoadingNearbyHotels = false;
      update();
    }
  }

  /// Load recent searches
  Future<void> _loadRecentSearches() async {
    try {
      final searches = await _homeService.getRecentSearches();
      _recentSearches = searches;
      update();
    } catch (e) {
      print('❌ [HOME_CONTROLLER] Error loading recent searches: $e');
    }
  }

  /// Update search location
  void updateLocation(LocationPlace location) {
    // Parse coordinates from locationId if it contains lat,lng format
    _hotelRequest = _hotelRequest.copyWith(
      location: location,
    );
    update([locationUpdateBuilder, searchBoxBuilder]);
  }

  /// Update check-in date
  void updateCheckDates(StayRequest stay) {
    // Ensure check-out is after check-in
    _hotelRequest = _hotelRequest.copyWith(
      stay: stay,
    );
    update([datesUpdateBuilder, searchBoxBuilder]);
  }

  int get getCountOfSelectedRooms => _hotelRequest.rooms.length;
  List<RoomRequestModel> get getRooms => _hotelRequest.rooms;

  void onGustsChanged(List<RoomRequestModel> result) {
    _hotelRequest = _hotelRequest.copyWith(rooms: result);
    update([guestsUpdateBuilder, searchBoxBuilder]);
  }

  /// Search hotels
  Future<void> searchHotels() async {
    if (!canSearch) {
      Get.snackbar(
        'خطأ في البحث',
        'يرجى إكمال جميع بيانات البحث',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
      return;
    }

    try {
      _isLoading = true;
      update();

      // Save search to recent searches
      await _saveRecentSearch();

      // Update HotelsDataSearch with the current request
      try {
        final hotelsDataSearch = Get.find<HotelsDataSearch>();
        hotelsDataSearch.updateRequest(_hotelRequest);
      } catch (e) {
        // If HotelsDataSearch is not found, put it ONLY if not already registered
        if (!Get.isRegistered<HotelsDataSearch>()) {
          Get.put(HotelsDataSearch());
        }
        final hotelsDataSearch = Get.find<HotelsDataSearch>();
        hotelsDataSearch.updateRequest(_hotelRequest);
      }

      // Navigate to hotels screen
      Get.toNamed('/hotels-plus');
    } catch (e) {
      print('❌ [HOME_CONTROLLER] Error searching hotels: $e');
      Get.snackbar(
        'خطأ في البحث',
        'حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    } finally {
      _isLoading = false;
      update();
    }
  }

  /// Save recent search
  Future<void> _saveRecentSearch() async {
    try {
      // Convert HotelRequest to SearchRequestPlus for compatibility
      final searchRequestPlus = SearchRequestPlus(
        location: _hotelRequest.location?.name,
        locationId: _hotelRequest.location?.name,
        checkIn: _hotelRequest.stay?.checkIn,
        checkOut: _hotelRequest.stay?.checkOut,
        adults: _hotelRequest.rooms
            .fold(0, (sum, room) => sum + room.adults.length),
        children: _hotelRequest.rooms
            .fold(0, (sum, room) => sum + room.children.length),
        rooms: _hotelRequest.rooms.length,
      );
      await _homeService.saveRecentSearch(searchRequestPlus);
      await _loadRecentSearches(); // Refresh recent searches
    } catch (e) {
      print('❌ [HOME_CONTROLLER] Error saving recent search: $e');
    }
  }

  /// Use recent search
  void useRecentSearch(RecentSearchPlus recentSearch) {
    final searchRequest = recentSearch.searchRequest;

    // Convert SearchRequestPlus to HotelRequest
    _hotelRequest = HotelRequest(
      location: searchRequest.locationId != null
          ? LocationPlace(
        locationId: searchRequest.locationId!,
        name: searchRequest.location ?? "",
        country: '',
        lat: '',
        lon: '',
        displayName: '',
        language: '',
        countryCode: '',
            )
          : null,
      stay: StayRequest(
        checkIn: searchRequest.checkIn,
        checkOut: searchRequest.checkOut,
      ),
      rooms: List.generate(
        searchRequest.rooms,
        (index) => RoomRequestModel(
          uuid: const Uuid().v4(),
          rooms: 1,
          adults: List.generate(
            searchRequest.adults ~/ searchRequest.rooms,
            (index) => GuestDataRequest.adult(),
          ),
          children: List.generate(
            searchRequest.children ~/ searchRequest.rooms,
            (index) => GuestDataRequest.child(age: 5),
          ),
        ),
      ),
    );
    update();
  }

  /// Search from popular destination
  void searchFromDestination(PopularDestinationPlus destination) {
    // updateLocation(destination.name, destination.id);
    searchHotels();
  }

  Future<void> refreshData() async {
    print('🔄 [HOME_CONTROLLER] Refreshing data...');
    _isInitialDataLoaded = false; // Reset to allow refresh
    await _loadInitialData();
  }
}
