import 'package:fandooq/core/const/assets.dart';
import 'package:fandooq/core/extensions/assets_extension.dart';
import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/feature_plus/home/<USER>/change_guests_info_controller.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../../core/components/app_button.dart';
import '../../../core/components/ui/app_text.dart';
import '../../../core/core.dart';
import '../../../core/lang/app_strings.dart';
import '../../../core/responsive/app_size.dart';
import '../../../core/theme/color_manager.dart';
import '../models/guests_search.dart';
import '../widgets/room_guest_card.dart';

class AgeOfChildBottomSheet extends StatelessWidget with AppSize {
  final int? initialAge;

  const AgeOfChildBottomSheet(this.initialAge);

  static Future<int?> show(BuildContext context, {int? age}) async {
    return await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      ),
      builder: (_) => AgeOfChildBottomSheet(age),
      // scrollControlDisabledMaxHeightRatio: 0.7,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        minHeight: MediaQuery.of(context).size.height * 0.5,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 5,
          width: widthScreen * 0.4,
          margin: const EdgeInsets.only(top: 10),
          color: Colors.grey.withOpacity(.2),
        ),
        AppBar(
          centerTitle: true,
          title: AppText.large(
            AppStrings.ageOfChild.tr,
            fontSize: 16,
          ).font,
          elevation: 0,
          scrolledUnderElevation: 0,
          backgroundColor: ColorManager.transparent ,
          leading: IconButton(
            icon: Assets.icon_close_svg.svg(color: Colors.black, width: 16),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: 18,
            padding: const EdgeInsets.only(right: 15,left: 15,bottom: 50),
            physics: const BouncingScrollPhysics(),
            itemBuilder: (context, index) {
              bool isSelect = initialAge == index;
              var age = index;
              if (age == 0) {
                return CheckboxListTile(
                  title: Text(AppStrings.under1YearOld.tr).font,
                  value: isSelect,
                  shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
                  onChanged: (bool? value) {
                    Navigator.pop(context,age);
                  },
                );
              }
              return CheckboxListTile(
                title: Text("$age ${AppStrings.yearOld.tr}"),
                value: isSelect,
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)
                    ),
                ),
                onChanged: (bool? value) {
                  Navigator.pop(context,age);
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
