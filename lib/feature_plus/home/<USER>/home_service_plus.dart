import 'package:get/get.dart';
import '../models/home_models_plus.dart';
import '../../hotels/models/hotel_models.dart';
import '../../../core/network_provider/networking.dart';

class HomeServicePlus extends GetxService {
  static HomeServicePlus get instance => Get.find<HomeServicePlus>();

  final NetworkHandler _networkHandler = NetworkHandler();

  // Prevent multiple calls
  bool _isLoadingDestinations = false;
  bool _isLoadingHotels = false;
  bool _isServiceInitialized = false;

  @override
  void onInit() {
    if (_isServiceInitialized) {
      print(
          '⚠️ [HOME_SERVICE] HomeServicePlus already initialized, skipping...');
      return;
    }

    super.onInit();
    _isServiceInitialized = true;
    print('✅ [HOME_SERVICE] HomeServicePlus initialized');
  }

  /// Get popular destinations
  Future<List<PopularDestinationPlus>> getPopularDestinations() async {
    if (_isLoadingDestinations) {
      print(
          '🔄 [HOME_SERVICE] Popular destinations already loading, returning empty...');
      return [];
    }

    try {
      _isLoadingDestinations = true;
      print('🌍 [HOME_SERVICE] Loading popular destinations...');

      // TODO: Implement real API call to /location/popular-destinations?countryCode=EG&limit=10
      // For now, return enhanced mock data based on your API response structure
      await Future.delayed(
          const Duration(milliseconds: 800)); // Simulate network delay

      final mockDestinations = _getMockPopularDestinations();
      print(
          '✅ [HOME_SERVICE] Loaded ${mockDestinations.length} popular destinations (mock data)');
      return mockDestinations;
    } catch (e) {
      print('❌ [HOME_SERVICE] Error getting popular destinations: $e');
      return [];
    } finally {
      _isLoadingDestinations = false;
    }
  }

  /// Get nearby hotels
  Future<List<HotelPlus>> getNearbyHotels({
    required double latitude,
    required double longitude,
    int limit = 10,
  }) async {
    if (_isLoadingHotels) {
      print(
          '🔄 [HOME_SERVICE] Nearby hotels already loading, returning empty...');
      return [];
    }

    try {
      _isLoadingHotels = true;
      print(
          '🏨 [HOME_SERVICE] Loading nearby hotels for lat: $latitude, lng: $longitude');

      // استخدام API الحقيقي للفنادق القريبة
      final response = await _networkHandler.get(
        ObjectResponse(),
        'location/nearby-hotels?maxDistance=50&limit=$limit&sortBy=distance&minRating=4',
        dioType: DioType.api,
      );

      print('🔍 [HOME_SERVICE] API Response: ${response.isRequestSuccess}');

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true && responseData['data'] != null) {
          final hotelsData = responseData['data'] as List<dynamic>;
          print(
              '✅ [HOME_SERVICE] Loaded ${hotelsData.length} nearby hotels from API');

          // تحويل البيانات إلى HotelPlus
          final hotelsPlusData = hotelsData.map((hotelJson) {
            return HotelPlus.fromNearbyApi(hotelJson as Map<String, dynamic>);
          }).toList();

          return hotelsPlusData;
        } else {
          print(
              '❌ [HOME_SERVICE] API returned success: false, using mock data');
          final mockHotels = _getMockNearbyHotels();
          return mockHotels;
        }
      } else {
        print('❌ [HOME_SERVICE] API request failed, using mock data');
        final mockHotels = _getMockNearbyHotels();
        return mockHotels;
      }
    } catch (e) {
      print(
          '❌ [HOME_SERVICE] Error getting nearby hotels: $e, using mock data');
      // Fallback to mock data in case of error
      final mockHotels = _getMockNearbyHotels();
      return mockHotels;
    } finally {
      _isLoadingHotels = false;
    }
  }

  /// Get recent searches
  Future<List<RecentSearchPlus>> getRecentSearches() async {
    try {
      // For now, return empty list
      // In production, this would get from cache or storage
      return [];
    } catch (e) {
      print('❌ [HOME_SERVICE] Error getting recent searches: $e');
      return [];
    }
  }

  /// Save recent search
  Future<void> saveRecentSearch(SearchRequestPlus searchRequest) async {
    try {
      if (searchRequest.location == null) return;

      final recentSearches = await getRecentSearches();

      // Create new recent search
      final newSearch = RecentSearchPlus(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        location: searchRequest.location!,
        locationId: searchRequest.locationId ?? '',
        searchDate: DateTime.now(),
        searchRequest: searchRequest,
      );

      // Remove duplicate if exists
      recentSearches.removeWhere((search) =>
          search.location == newSearch.location &&
          search.searchRequest.checkIn?.day ==
              newSearch.searchRequest.checkIn?.day);

      // Add to beginning
      recentSearches.insert(0, newSearch);

      // Keep only last 10 searches
      if (recentSearches.length > 10) {
        recentSearches.removeRange(10, recentSearches.length);
      }

      // For now, do nothing
      // In production, this would save to cache or storage
    } catch (e) {
      print('❌ [HOME_SERVICE] Error saving recent search: $e');
    }
  }

  /// Clear recent searches
  Future<void> clearRecentSearches() async {
    try {
      // For now, do nothing
      // In production, this would clear cache or storage
    } catch (e) {
      print('❌ [HOME_SERVICE] Error clearing recent searches: $e');
    }
  }

  /// Mock data for popular destinations
  List<PopularDestinationPlus> _getMockPopularDestinations() {
    return [
      PopularDestinationPlus(
        id: '1',
        name: 'الرياض',
        image:
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
        country: 'السعودية',
        hotelsCount: 245,
      ),
      PopularDestinationPlus(
        id: '2',
        name: 'جدة',
        image:
            'https://images.unsplash.com/photo-1591608971362-f08b2a75731a?w=400',
        country: 'السعودية',
        hotelsCount: 189,
      ),
      PopularDestinationPlus(
        id: '3',
        name: 'مكة المكرمة',
        image:
            'https://images.unsplash.com/photo-1564769625392-651b2c0b8b6b?w=400',
        country: 'السعودية',
        hotelsCount: 156,
      ),
      PopularDestinationPlus(
        id: '4',
        name: 'المدينة المنورة',
        image:
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
        country: 'السعودية',
        hotelsCount: 98,
      ),
    ];
  }

  /// Mock data for nearby hotels
  List<HotelPlus> _getMockNearbyHotels() {
    return [
      HotelPlus(
        code: 'HOTEL001',
        name: 'فندق الريتز كارلتون',
        description: 'فندق فاخر في قلب المدينة',
        starRating: 5,
        address: 'شارع الملك فهد، الرياض',
        city: 'الرياض',
        country: 'السعودية',
        currency: 'SAR',
        latitude: 24.7136,
        longitude: 46.6753,
        images: [
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400',
          'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=400',
        ],
        amenities: ['واي فاي مجاني', 'مسبح', 'جيم', 'سبا'],
        facilities: ['مطعم', 'خدمة الغرف', 'موقف سيارات'],
      ),
      HotelPlus(
        code: 'HOTEL002',
        name: 'فندق فورسيزونز',
        description: 'إقامة مريحة مع خدمة ممتازة',
        starRating: 5,
        address: 'برج المملكة، الرياض',
        city: 'الرياض',
        country: 'السعودية',
        currency: 'SAR',
        latitude: 24.7136,
        longitude: 46.6753,
        images: [
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=400',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
        ],
        amenities: ['واي فاي مجاني', 'مسبح', 'جيم'],
        facilities: ['مطعم', 'خدمة الغرف'],
      ),
    ];
  }
}
