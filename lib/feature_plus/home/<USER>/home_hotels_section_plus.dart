import 'package:fandooq/feature_plus/feature_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../controllers/home_controller_plus.dart';
import '../../hotels/models/hotel_models.dart';

class HomeHotelsSectionPlus extends StatelessWidget {
  const HomeHotelsSectionPlus({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeControllerPlus>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'homeHotelsSectionPlus.nearbyHotels'.tr,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  // TextButton(
                  //   onPressed: () {
                  //     // TODO: Navigate to all nearby hotels
                  //   },
                  //   child: Text(
                  //     'homeHotelsSectionPlus.showAll'.tr,
                  //     style: TextStyle(
                  //       color: ColorManager.primary,
                  //       fontWeight: FontWeight.w600,
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Hotels List
            if (controller.isLoadingNearbyHotels)
              _buildLoadingState()
            else if (controller.nearbyHotels.isEmpty)
              _buildEmptyState()
            else
              _buildHotelsList(controller.nearbyHotels),
          ],
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 3,
        itemBuilder: (context, index) {
          return Container(
            width: 250,
            margin: const EdgeInsets.only(right: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 200,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.hotel_outlined,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 8),
            Text(
              'homeHotelsSectionPlus.noNearbyHotels'.tr,
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
            SizedBox(height: 4),
            Text(
              'homeHotelsSectionPlus.enableLocationToShowNearby'.tr,
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotelsList(List<HotelPlus> hotels) {
    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.only(
          right: 16,
          left: 16,
          bottom: 16
        ),
        itemCount: hotels.length,
        itemBuilder: (context, index) {
          final hotel = hotels[index];
          return _buildHotelCard(hotel);
        },
      ),
    );
  }

  Widget _buildHotelCard(HotelPlus hotel) {
    return GestureDetector(
      onTap: () {
        // TODO: Navigate to hotel details
        // HotelsNavigation.toHotelDetails(hotel: hotel);
        var request = HomeControllerPlus.to.getRequest;

        HotelsNavigation.toHotelDetails(
          hotel: hotel,
          scrollToRooms: false,
          additionalArguments: {
            "fromDeepLink": true,
            "checkInDate": request.stay?.checkIn,
            "checkOutDate": request.stay?.checkOut,
            "paxRooms": request.paxRooms, // Add paxRooms to arguments
            "searchWithAvailability": true, // Flag to trigger availability search
          },
        );

      },
      child: Container(
        width: 250,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hotel Image
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: Stack(
                children: [
                  Container(
                    height: 160,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      image: hotel.images.isNotEmpty
                          ? DecorationImage(
                              image: NetworkImage(hotel.images.first),
                              fit: BoxFit.cover,
                              onError: (error, stackTrace) {
                                // Handle image loading error
                              },
                            )
                          : null,
                      color: Colors.grey[300],
                    ),
                    child: hotel.images.isEmpty
                        ? const Center(
                            child: Icon(
                              Icons.hotel,
                              size: 48,
                              color: Colors.grey,
                            ),
                          )
                        : null,
                  ),

                ],
              ),
            ),

            // Hotel Info
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Hotel Name
                    Text(
                      hotel.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // Location
                    Row(
                      children: [
                        Icon(
                          Icons.location_on_outlined,
                          color: Colors.grey[600],
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            hotel.city,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // Star Rating
                    Row(
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            index < hotel.starRating
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 16,
                          );
                        }),
                        const SizedBox(width: 8),
                        Text(
                          '${hotel.starRating} ${'homeHotelsSectionPlus.stars'.tr}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),

                    // const Spacer(),

                    // Price (placeholder)
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //   children: [
                    //     Text(
                    //       'homeHotelsSectionPlus.startingFrom'.tr,
                    //       style: const TextStyle(
                    //         fontSize: 12,
                    //         color: Colors.grey,
                    //       ),
                    //     ),
                    //     Text(
                    //       '299 ${hotel.currency}',
                    //       style: TextStyle(
                    //         fontSize: 16,
                    //         fontWeight: FontWeight.bold,
                    //         color: ColorManager.primary,
                    //       ),
                    //     ),
                    //   ],
                    // ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
