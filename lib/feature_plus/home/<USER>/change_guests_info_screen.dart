


import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/feature_plus/home/<USER>/change_guests_info_controller.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../../core/components/app_button.dart';
import '../../../core/components/ui/app_text.dart';
import '../../../core/core.dart';
import '../../../core/lang/app_strings.dart';
import '../../../core/theme/color_manager.dart';
import '../models/guests_search.dart';
import '../widgets/room_guest_card.dart';

class ChangeGuestsInfoScreen extends StatefulWidget {
  final List<RoomRequestModel> rooms;
  final ScrollController? scrollController;
  const ChangeGuestsInfoScreen({super.key, required this.rooms, this.scrollController});
  @override
  State<ChangeGuestsInfoScreen> createState() => _ChangeGuestsInfoScreenState();
}

class _ChangeGuestsInfoScreenState extends State<ChangeGuestsInfoScreen> {

  late ChangeGuestsInfoController controller = Get.put(ChangeGuestsInfoController());

  @override
  void initState() {
    controller.init(arguments: widget.rooms);
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<ChangeGuestsInfoController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    return Column(
      children: [

        // Handle bar
        Container(
          margin: const EdgeInsets.only(top: 8),
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(2),
          ),
        ),

        // Header
        Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              HugeIcon(
                icon: HugeIcons.strokeRoundedUserGroup,
                color: ColorManager.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  AppStrings.guestsInformation,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
        ),

        // DefaultAppBarMobile(title: AppStrings.guestsInformation),

        Expanded(
          child: Obx(() {

            final controller = ChangeGuestsInfoController.to;
            final state = controller.state;

            return SingleChildScrollView(
              controller: widget.scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                children: [

                  const SizedBox(height: 20),

                  Opacity(
                    opacity: 0.5,
                    child: AppText.small(
                        AppStrings.maximumNumberOfGuestsPerRoomIs8.tr).font,
                  ),

                  const SizedBox(height: 20),

                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      AnimatedList(
                        key: state.roomsListKey,
                        initialItemCount: state.rooms.length,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index, animation) {
                          final room = state.rooms[index];
                          return FadeTransition(
                            opacity: animation,
                            child: SlideTransition(
                              position: Tween<Offset>(
                                begin: const Offset(1, 0),
                                end: Offset.zero,
                              ).animate(animation),
                              child: RoomGuestCard(
                                roomModel: room,
                                removeRoom: () {
                                  controller.removeRoom(room);
                                },
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 20),

                      Offstage(
                        offstage: state.rooms.length >= 4,
                        child: AppButton(
                          isOutlineBorder: true,
                          elevation: 0,
                          borderColor: ColorManager.primary,
                          horizontalPadding: 20,
                          onPressed: controller.addRoom,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.add_circle_outlined,
                                color: ColorManager.primary,
                                size: 18,
                              ),
                              const SizedBox(width: 5),
                              AppText.medium(
                                AppStrings.addRoom.tr,
                                color: ColorManager.primary,
                                fontWeight: FontWeight.w600,
                              ).font,
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 150),
                    ],
                  ),



                ],
              ),
            );
          }),
        ),


        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: AppButton(
            title: AppStrings.apply.tr,
            horizontalPadding: 0,
            onPressed: () {
              Navigator.pop(context,controller.state.rooms);
            },
          ),
        ),

        // Bottom padding
        const SizedBox(height: 20),

      ],
    );

  }

}


