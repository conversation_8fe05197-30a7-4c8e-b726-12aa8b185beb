import 'package:fandooq/feature_plus/settings/routes/settings_routes_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import 'package:fandooq/core/lang/app_strings.dart';
import '../../user/controllers/user_controller_plus.dart';
import '../../settings/controllers/settings_controller.dart';
import '../../notifications/views/widgets/notification_bell_widget.dart';

class HomeAppBarPlus extends StatelessWidget {
  const HomeAppBarPlus({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: ColorManager.primary,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                ColorManager.primary,
                ColorManager.primary.withOpacity(0.8),
              ],
            ),
          ),
          child: Safe<PERSON><PERSON>(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      // Welcome Text and Currency
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // User Welcome
                            GetBuilder<UserControllerPlus>(
                              builder: (userController) {
                                final user = userController.userProfile;
                                final userName = user?.firstName ?? 'ضيف';
                                return Text(
                                  'مرحباً، $userName',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 2),
                            // Currency Display
                            Text(
                              AppStrings.whereToStay,
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Notification Icon
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const NotificationBellWidget(),
                      ),

                      const SizedBox(width: 8),

                      // Profile Icon
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          onPressed: () {
                            // Navigate to settings screen
                            SettingsNavigator.navigateToSettings();
                          },
                          icon: const Icon(
                            Icons.person_outline,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
