import 'package:fandooq/feature_plus/feature_plus.dart';
import 'package:fandooq/feature_plus/home/<USER>/guests_search.dart';
import 'package:fandooq/feature_plus/home/<USER>/change_guests_info_screen.dart';
import 'package:fandooq/feature_plus/location_search/views/location_search_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import 'package:fandooq/core/lang/app_strings.dart';
import 'package:intl/intl.dart';
import '../../../core/components/free_place_search/lib/models/address.dart';
import '../../../core/components/free_place_search/lib/models/geo_point.dart';
import '../../../core/components/free_place_search/lib/models/location_info.dart';
import '../controllers/home_controller_plus.dart';
import '../../hotels/views/widgets/enhanced_date_range_picker.dart';

class HomeSearchSectionPlus extends StatelessWidget {
  const HomeSearchSectionPlus({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeControllerPlus>(
      builder: (controller) {
        return Container(
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 32),
          child: Column(
            children: [
              // Main Search Card
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.04),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Location Field
                    _buildLocationField(controller),

                    // Divider
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 24),
                      height: 1,
                      color: Colors.grey[100],
                    ),

                    // Dates Row - Check-in and Check-out
                    Row(
                      children: [
                        // Check-in Date
                        Expanded(
                          child: _buildCheckInField(controller),
                        ),

                        // Vertical Divider
                        Container(
                          width: 1,
                          height: 60,
                          color: Colors.grey[100],
                        ),

                        // Check-out Date
                        Expanded(
                          child: _buildCheckOutField(controller),
                        ),
                      ],
                    ),

                    // Divider
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 24),
                      height: 1,
                      color: Colors.grey[100],
                    ),

                    // Guests Field - Full Width
                    _buildGuestsField(controller),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Search Button
              GetBuilder<HomeControllerPlus>(
                id: HomeControllerPlus.searchBoxBuilder,
                builder: (_){
                  return _buildSearchButton(controller);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLocationField(HomeControllerPlus controller) {
    return InkWell(
      onTap: () async {
        final result = await LocationSearchScreen.showBottomSheet(
          Get.context!,
          title: 'locationPicker.chooseDestination'.tr,
          hint: 'locationPicker.searchHint'.tr,
        );
        if (result != null) {
          // Convert LocationPlace to LocationInfo for compatibility
          controller.updateLocation(result);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: ColorManager.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.location_on,
                color: ColorManager.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.whereToStay,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  GetBuilder<HomeControllerPlus>(
                    id: HomeControllerPlus.locationUpdateBuilder,
                    builder: (_) {
                      return Text(
                        controller.locationName ?? AppStrings.chooseDestination,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: controller.locationName != null
                              ? Colors.black87
                              : Colors.grey[500],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      );
                    },
                  ),
                ],
              ),
            ),
            Icon(
              Icons.search,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatesField(HomeControllerPlus controller) {
    return InkWell(
      onTap: () => _showDatePicker(controller),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  color: ColorManager.primary,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'التواريخ',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            GetBuilder<HomeControllerPlus>(
              id: HomeControllerPlus.datesUpdateBuilder,
              builder: (_) {
                return Text(
                  _formatDateRange(controller),
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateRange(HomeControllerPlus controller) {
    final start = controller.checkInDate;
    final end = controller.checkOutDate;

    if (start == null || end == null) return AppStrings.selectDate;

    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];

    final nights = end.difference(start).inDays;
    String formatDate(DateTime date) => '${date.day} ${months[date.month - 1]}';

    return '${formatDate(start)} - ${formatDate(end)} • $nights ${AppStrings.nights}';
  }

  Widget _buildCheckInField(HomeControllerPlus controller) {
    return InkWell(
      onTap: () => _showDatePicker(controller),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.login,
                  color: ColorManager.primary,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  AppStrings.checkIn,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            GetBuilder<HomeControllerPlus>(
              id: HomeControllerPlus.datesUpdateBuilder,
              builder: (_) {
                return Text(
                  controller.checkInDate != null
                      ? _formatSingleDate(controller.checkInDate!)
                      : AppStrings.selectDate,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: controller.checkInDate != null
                        ? Colors.black87
                        : Colors.grey[500],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckOutField(HomeControllerPlus controller) {
    return InkWell(
      onTap: () => _showDatePicker(controller),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.logout,
                  color: ColorManager.primary,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  AppStrings.checkOut,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            GetBuilder<HomeControllerPlus>(
              id: HomeControllerPlus.datesUpdateBuilder,
              builder: (_) {
                return Text(
                  controller.checkOutDate != null
                      ? _formatSingleDate(controller.checkOutDate!)
                      : AppStrings.selectDate,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: controller.checkOutDate != null
                        ? Colors.black87
                        : Colors.grey[500],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }


  String _formatSingleDate(DateTime date) {
    final formatter = DateFormat('d MMMM', Get.locale?.languageCode); // مثل: 1 أغسطس
    return formatter.format(date);
  }


  Widget _buildGuestsField(HomeControllerPlus controller) {
    return InkWell(
      onTap: () => _showGuestsPicker(controller),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: ColorManager.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.people_outline,
                color: ColorManager.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.guestsAndRooms,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  GetBuilder<HomeControllerPlus>(
                    id: HomeControllerPlus.guestsUpdateBuilder,
                    builder: (_) {
                      return Text(
                        controller.guestsText,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      );
                    },
                  ),
                ],
              ),
            ),
            Icon(
              Icons.edit_outlined,
              color: Colors.grey[400],
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchButton(HomeControllerPlus controller) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 500),
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: controller.canSearch
            ? LinearGradient(
                colors: [
                  ColorManager.buttonColor,
                  ColorManager.buttonColor.withOpacity(0.0),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: controller.canSearch ? null : Colors.grey[300],
        boxShadow: controller.canSearch
            ? [
                BoxShadow(
                  color: ColorManager.primary.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                  spreadRadius: 0,
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: controller.canSearch ? controller.searchHotels : null,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: controller.isLoading
                ? const Center(
                    child: SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_rounded,
                        size: 22,
                        color: controller.canSearch
                            ? Colors.white
                            : Colors.grey[600],
                      ),
                      const SizedBox(width: 12),
                      Text(
                        AppStrings.searchHotels,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: controller.canSearch
                              ? Colors.white
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  // String _formatDateRange(HomeControllerPlus controller) {
  //   if (controller.checkInDate == null || controller.checkOutDate == null) {
  //     return 'اختر التواريخ';
  //   }
  //
  //   final checkIn = controller.checkInDate!;
  //   final checkOut = controller.checkOutDate!;
  //   final nights = checkOut.difference(checkIn).inDays;
  //
  //   return '${_formatDate(checkIn)} - ${_formatDate(checkOut)} ($nights ليالي)';
  // }
  //
  // String _formatDate(DateTime date) {
  //   return '${date.day}/${date.month}';
  // }

  void _showDatePicker(HomeControllerPlus controller) async {
    await EnhancedDateRangePicker.show(
      context: Get.context!,
      initialCheckIn: controller.checkInDate,
      initialCheckOut: controller.checkOutDate,
      title: 'اختر تواريخ الإقامة',
      onDateRangeSelected: (checkIn, checkOut) {
        StayRequest stayRequest =
            StayRequest(checkIn: checkIn, checkOut: checkOut);
        controller.updateCheckDates(stayRequest);
      },
    );
  }

  void _showGuestsPicker(HomeControllerPlus controller) async {
    final result = await showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: controller.getCountOfSelectedRooms > 1 ? 0.9 : 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: ChangeGuestsInfoScreen(
            rooms: controller.getRooms,
            scrollController: scrollController,
          ),
        ),
      ),
    );
    if (result != null) {
      controller.onGustsChanged((result as List<RoomRequestModel>));
    }
  }
}
