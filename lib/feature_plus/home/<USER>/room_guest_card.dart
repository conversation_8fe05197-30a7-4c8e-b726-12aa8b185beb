import 'package:dotted_border/dotted_border.dart';
import 'package:fandooq/core/const/assets.dart';
import 'package:fandooq/core/extensions/assets_extension.dart';
import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/feature_plus/home/<USER>/change_guests_info_controller.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../../core/components/app_button.dart';
import '../../../core/components/ui/app_text.dart';
import '../../../core/core.dart';
import '../../../core/lang/app_strings.dart';
import '../../../core/theme/color_manager.dart';
import '../models/guests_search.dart';
import '../widgets/room_guest_card.dart';
import 'adult_counter.dart';
import 'children_age_chips.dart';
import 'children_counter.dart';


class RoomGuestCard extends StatelessWidget {
  final RoomRequestModel roomModel;
  final GestureTapCallback? removeRoom;
  const RoomGuestCard({super.key, required this.roomModel, this.removeRoom});

  @override
  Widget build(BuildContext context) {

    return Obx(() {
      final state = ChangeGuestsInfoController.to.state;
      return Padding(
        padding: const EdgeInsets.only(bottom: 20.0),
        child: DottedBorder(
          color: Theme.of(context).primaryColor.withOpacity(.5),
          strokeWidth: 1,
          dashPattern: const [10, 5],
          radius: const Radius.circular(8.0),
          padding: const EdgeInsets.all(0),
          borderType: BorderType.RRect,
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(8.0)),
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(8.0)),
              ),
              width: MediaQuery.of(context).size.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //TODO :: Header Room & Remove Button
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 2, vertical: 4),
                    decoration: BoxDecoration(
                      color: ColorManager.lightGrey.withOpacity(.1),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.all(10),
                            child: Row(
                              children: [
                                Assets.icon_bed_svg.svg(color: ColorManager.blue, width: 15),
                                const SizedBox(width: 7),
                                Expanded(
                                  child: AppText.medium(
                                    "${AppStrings.room.tr} ${state.rooms.indexOf(roomModel) + 1}",
                                    fontWeight: FontWeight.w600,
                                  ).font,
                                ),
                              ],
                            ),
                          ),
                        ),
                        Offstage(
                          offstage: state.rooms.length <= 1,
                          child: InkWell(
                            onTap: removeRoom,
                            borderRadius: BorderRadius.circular(10),
                            child: Padding(
                              padding: EdgeInsets.all(10),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Assets.icon_delete_2_svg.svg(color: Colors.red, width: 15),
                                  const SizedBox(width: 7),
                                  AppText.medium(
                                    AppStrings.remove.tr,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.red,
                                  ).font,
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  //TODO :: Adults Counter
                  AdultCounter(model: roomModel),
                  Divider(height: 0.1),

                  //TODO :: Children Counter
                  ChildrenCounter(model: roomModel),

                  // TODO :: Children Age Chips
                  ChildrenAgeChips(model: roomModel),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}