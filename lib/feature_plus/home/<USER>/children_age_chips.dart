import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/feature_plus/home/<USER>/change_guests_info_controller.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../../core/components/app_button.dart';
import '../../../core/components/ui/app_text.dart';
import '../../../core/core.dart';
import '../../../core/lang/app_strings.dart';
import '../../../core/theme/color_manager.dart';
import '../models/guests_search.dart';
import '../widgets/room_guest_card.dart';
import 'age_of_child_bottom_sheet.dart';

class ChildrenAgeChips extends StatelessWidget {
  RoomRequestModel model;

  ChildrenAgeChips({super.key, required this.model});

  String getName(int age) {
    String name = "";
    if (age == 0) {
      name = AppStrings.under1YearOldChild.tr;
    } else if (age == 1) {
      name = "1" + " " + AppStrings.yearOld.tr;
    } else {
      name = age.toString() + " " + AppStrings.yearsOld.tr;
    }
    return name;
  }

  @override
  Widget build(BuildContext context) {
    final state = ChangeGuestsInfoController.to.state;
    return Offstage(
      offstage: model.children.isEmpty,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(height: 0.1),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Wrap(
              crossAxisAlignment: WrapCrossAlignment.start,
              spacing: 10,
              runSpacing: 8,
              children: model.children.map((e) {
                int index = model.children.indexOf(e);
                return ActionChip(
                  visualDensity: VisualDensity.compact,
                  padding: EdgeInsets.zero,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 1,
                    ),
                  ),
                  onPressed: () async {
                    int? age = await AgeOfChildBottomSheet.show(context, age: e.age);
                    if (age == null) return;
                    model = model.copyWith(
                      children: [
                        ...model.children
                          ..removeAt(index)
                          ..insert(index, GuestDataRequest.child(age: age)),
                      ],
                    );
                    state.rooms[state.rooms.indexOf(model)] = model;
                  },
                  label: AppText.small(
                    "${AppStrings.child.tr} ${index + 1} : ${getName(e.age ?? 0)}",
                  ).font,
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
