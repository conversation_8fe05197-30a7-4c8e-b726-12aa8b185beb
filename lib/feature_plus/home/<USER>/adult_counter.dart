import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/feature_plus/home/<USER>/change_guests_info_controller.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../../core/components/app_button.dart';
import '../../../core/components/buttons/counter.dart';
import '../../../core/components/ui/app_text.dart';
import '../../../core/core.dart';
import '../../../core/lang/app_strings.dart';
import '../../../core/theme/color_manager.dart';
import '../models/guests_search.dart';
import '../widgets/room_guest_card.dart';

class AdultCounter extends StatelessWidget {
  final RoomRequestModel model;

  AdultCounter({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.only(start: 15, top: 5, end: 5, bottom: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppText.small(
                AppStrings.adults.tr,
                fontWeight: FontWeight.w400,
              ).font,
              const SizedBox(height: 3),
              Opacity(
                opacity: 0.5,
                child: AppText.small(
                  AppStrings.agePlus12.tr,
                  fontWeight: FontWeight.w400,
                  fontSize: 8,
                ).font,
              ),
            ],
          ),
          CustomizableCounter(
            count: model.adults.length,
            minCount: 1,
            maxCount: model.maxAdults,
            onIncrement: (count) {
              ChangeGuestsInfoController.to.addAdult(model);
            },
            onDecrement: (count) {
              ChangeGuestsInfoController.to.removeAdult(model);
            },
          ),
        ],
      ),
    );
  }
}
