import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../controllers/home_controller_plus.dart';
import '../services/home_service_plus.dart';

import '../widgets/home_app_bar_plus.dart';
import '../widgets/home_search_section_plus.dart';
import '../widgets/home_hotels_section_plus.dart';
import '../widgets/popular_destinations_section_plus.dart';
import '../widgets/recent_searches_section_plus.dart';
import '../../notifications/views/widgets/notification_bell_widget.dart';

class HomeScreenPlus extends StatelessWidget {
  const HomeScreenPlus({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: GetBuilder<HomeControllerPlus>(
        builder: (controller) {
          return RefreshIndicator(
            onRefresh: controller.refreshData,
            child: CustomScrollView(
              slivers: [
                // App Bar
                const HomeAppBarPlus(),

                // Search Section
                SliverToBoxAdapter(
                  child: Container(
                    color: ColorManager.primary,
                    child: const HomeSearchSectionPlus(),
                  ),
                ),

                // Content
                SliverToBoxAdapter(
                  child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                    ),
                    child: Column(
                      children: [
                        const SizedBox(height: 24),

                        // Recent Searches
                        if (controller.recentSearches.isNotEmpty) ...[
                          const RecentSearchesSectionPlus(),
                          const SizedBox(height: 24),
                        ],

                        // Popular Destinations - Hidden temporarily
                        // const PopularDestinationsSectionPlus(),
                        // const SizedBox(height: 24),

                        // Nearby Hotels
                        const HomeHotelsSectionPlus(),
                        const SizedBox(height: 100), // Bottom padding
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
