import 'dart:developer';
import 'package:fandooq/core/components/free_place_search/lib/models/address.dart';
import 'package:fandooq/core/components/free_place_search/lib/models/geo_point.dart';
import 'package:fandooq/core/components/free_place_search/lib/models/location_info.dart';
import 'package:fandooq/core/data_base/features/global_app/last_location.dart';
import 'package:fandooq/feature_plus/home/<USER>/guests_search.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:fandooq/feature_plus/hotels/models/hotels_data_search.dart';
import 'package:fandooq/feature_plus/location_search/models/location_models.dart';
import 'package:fandooq/feature_plus/hotels/routes/hotels_routes_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:google_map_location_picker/google_map_location_picker.dart';

class GuestsController extends GetxController {
  static GuestsController get to => Get.find<GuestsController>();

  static const String dateBuilder = "dateBuilder";
  static const String guestBuilder = "guestBuilder";
  static const String locationBuilder = "locationBuilder";
  static const String builder = "builder";

  int unratedCount = 0;
  int star1Count = 0;
  int star2Count = 0;
  int star3Count = 0;
  int star4Count = 0;
  int star5Count = 0;

  GuestsController() {
    HotelsDataSearch.to.updateRequest(HotelRequest.defaultRequest());
  }

  String get locationText {
    try {
      // استخدام DBLastLocation مباشرة بدلاً من LocationSearchController
      final lastLocation = DBLastLocation.getLastLocation();
      if (lastLocation != null) {
        return lastLocation.name;
      }
    } catch (e) {
      print('⚠️ Error getting location text from DB: $e');
    }

    // Fallback to current location from HotelsData
    final currentLocation = HotelsDataSearch.to.request?.location;
    if (currentLocation?.name != null) {
      return currentLocation!.name;
    }

    return 'القاهرة'; // Default location text
  }

  @override
  void onReady() {
    // TODO: implement onReady
    initLocation();
    super.onReady();
  }

  void initLocation() {
    // Try to get last location from DBLastLocation directly
    try {
      final lastLocation = DBLastLocation.getLastLocation();
      if (lastLocation != null) {
        // Convert LocationPlace to LocationInfo for compatibility
        onLocationChanged(lastLocation);
        print('🏠 Loaded last location from DB: ${lastLocation.name}');
        return;
      }
    } catch (e) {
      print('⚠️ Error getting location from DB: $e');
    }

    // Final fallback to default location (Cairo instead of UAE)
    final defaultLocation = LocationPlace(
      locationId: "default_cairo",
      lat: "30.044420",
      lon: "31.235712",
      name: "القاهرة",
      country: "مصر",
      countryCode: "EG",
      displayName: 'مصر ,القاهرة',
      language: 'ar',
    );
    onLocationChanged(defaultLocation);
    print('🏠 Using default location: Cairo instead of UAE');
  }

  //? **************** Change Range Date ************************//
  void onStayChanged(StayRequest stay) {
    HotelsDataSearch.to
        .updateRequest(HotelsDataSearch.to.request!.copyWith(stay: stay));
    update([dateBuilder, builder]);
  }

  //? ***************** Change Guests Info [Rooms] ************************//
  void onRoomsChanged(List<RoomRequestModel> rooms) {
    HotelsDataSearch.to
        .updateRequest(HotelsDataSearch.to.request!.copyWith(rooms: rooms));
    update([guestBuilder, builder]);
  }

  //? **************** Change Guests Location ************************//
  // String get locationText {
  //   final LocationInfo? searchInfo = HotelsData.to.request?.location;
  //   if (searchInfo == null || searchInfo.address == null) {
  //     return AppStrings.whereAreYouGoing.tt;
  //   }
  //   return searchInfo.address!.formate1();
  // }

  void onLocationChanged(LocationPlace searchInfo) {
    HotelsDataSearch.to.updateRequest(
        HotelsDataSearch.to.request!.copyWith(location: searchInfo));

    // Save location to DB when it changes
    DBLastLocation.saveLastLocation(searchInfo);

    update([locationBuilder, builder]);
  }

  void newArguments(Map<String, String?> queryParams) {
    var stay = queryParams["request"];
    if (stay != null) {
      HotelsDataSearch.to.updateRequest(HotelRequest.fromJson(stay));
    } else {
      HotelsDataSearch.to.updateRequest(HotelRequest.defaultRequest());
    }
  }

  void onChanged(HotelRequest copyWith) {
    HotelsDataSearch.to.updateRequest(copyWith);
  }

  void onHotels(List<HotelData> hotels) {
    star5Count = hotels.where((e) => e.getStars == 5).length;
    star4Count = hotels.where((e) => e.getStars == 4).length;
    star3Count = hotels.where((e) => e.getStars == 3).length;
    star2Count = hotels.where((e) => e.getStars == 2).length;
    star1Count = hotels.where((e) => e.getStars == 1).length;
    unratedCount = hotels.where((e) => e.getStars == 0).length;
  }

  void onChangeHotelsIds(String value) {
    List<String> parts = value.split(',');
    bool allNumbers = parts.every((part) => int.tryParse(part) != null);

    if (allNumbers) {
      var hotelIds = parts.map((e) => e).toList();
      HotelsDataSearch.to.updateRequest(
          HotelsDataSearch.to.request!.copyWith(hotelsIds: hotelIds));
      log(hotelIds.toString());
      update([guestBuilder, builder]);
    } else {
      HotelsDataSearch.to
          .updateRequest(HotelsDataSearch.to.request!.copyWith(hotelsIds: []));
    }
  }

  void onSubmit(BuildContext context) {
    // التنقل إلى Hotels Plus الجديدة
    _navigateToHotelsPlus(context);
  }

  void _navigateToHotelsPlus(BuildContext context) {
    // التنقل إلى صفحة الفنادق الجديدة باستخدام Named Routes
    HotelsNavigation.toHotels();
  }
}
