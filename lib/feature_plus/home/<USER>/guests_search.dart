
import 'dart:developer';

import 'package:fandooq/core/lang/app_strings.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';



class StayRequest {

  final DateTime? checkIn;
  final DateTime? checkOut;

  StayRequest({this.checkIn, this.checkOut});

  StayRequest copyWith({
    DateTime? checkIn,
    DateTime? checkOut,
  }) {
    return StayRequest(
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
    );
  }
  // Calculate the number of nights for the stay
  int get countDays => checkOut!.difference(checkIn!).inDays + 1;

  factory StayRequest.fromJson(dynamic json) {
    return StayRequest(
      checkIn: json['checkIn'] != null ? DateTime.parse(json['checkIn']) : null,
      checkOut: json['checkOut'] != null ? DateTime.parse(json['checkOut']) : null,
    );
  }

  factory StayRequest.defaultStay() {
    return StayRequest(
      checkIn: DateTime.now().add(Duration(days: 1)),
      checkOut: DateTime.now().add(Duration(days: 2)),
    );
  }

  Map<String, dynamic> toApi() {
    return {
      'checkIn': checkIn != null ? checkIn!.toString().split(" ").first : null,
      'checkOut': checkOut != null ? checkOut!.toString().split(" ").first : null,
    };
  }

}



enum GuestType {
  ADULT,
  CHILD;
  String get key {
    switch (this) {
      case GuestType.ADULT:
        return 'AD';
      case GuestType.CHILD:
        return 'CH';
    }
  }

}

class GuestDataRequest {

  final String? uuid;
  final String? mrMs;
  final String? type;
  final int? age;
  final String? name;
  final String? surname;


  GuestDataRequest({this.uuid,this.mrMs,this.type, this.age, this.name, this.surname});


  factory GuestDataRequest.adult({int? age}) {
    return GuestDataRequest(
      uuid: Uuid().v4(),
      type: GuestType.ADULT.key,
      age: age,
    );
  }

  factory GuestDataRequest.child({required int age}) {
    return GuestDataRequest(
      uuid: Uuid().v4(),
      type: GuestType.CHILD.key,
      age: age,
    );
  }

  GuestDataRequest copyWith({
    String? uuid,
    String? mrMs,
    String? type,
    int? age,
    String? name,
    String? surname,
  }) {
    return GuestDataRequest(
      uuid: uuid ?? this.uuid,
      mrMs: mrMs ?? this.mrMs,
      type: type ?? this.type,
      age: age ?? this.age,
      name: name ?? this.name,
      surname: surname ?? this.surname,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': this.type,
      'age': this.age,
    };
  }

  factory GuestDataRequest.fromJson(Map<String, dynamic> map) {
    return GuestDataRequest(
      type: map['type'],
      age: map['age'],
    );
  }

}

class RoomRequestModel {

  final int? rooms;
  final List<GuestDataRequest> adults;
  final List<GuestDataRequest> children;
  final String uuid;

  static const int MAX_GUESTS = 5;

  int get totalGuests => adults.length + children.length;


  int get maxAdults {
    if (children.isEmpty) return MAX_GUESTS;
    return MAX_GUESTS - children.length;
  }

  int get maxChildren {
    if (adults.isEmpty) return MAX_GUESTS;
    return MAX_GUESTS - adults.length;
  }

  String get info {

    // حساب إجمالي عدد البالغين
    final totalAdults = adults.length;

    // جمع أعمار جميع الأطفال
    final childrenAges = children.map((child) => child.age).toList();

    // حساب إجمالي عدد الأطفال
    final totalChildren = childrenAges.length;

    // إنشاء نص الأعمار بجانب كل طفل
    String childrenInfo = "";
    if (totalChildren > 0) {
      childrenInfo = "(";
      for (int i = 0; i < childrenAges.length; i++) {
        childrenInfo += "${AppStrings.child.tr} ${i + 1}: ${childrenAges[i]} ${AppStrings.yearsOld.tr}";
        if (i != childrenAges.length - 1) {
          childrenInfo += ", ";
        }
      }
      childrenInfo += ")";
    } else {
      childrenInfo = "0 ${AppStrings.children.tr}";
    }

    return [
      "$totalAdults ${AppStrings.adults.tr},",
      childrenInfo
    ].join(" ");
  }

  RoomRequestModel({
    required this.uuid,
    this.rooms,
    this.adults = const [],
    this.children = const [],
  });

  factory RoomRequestModel.defaultRoom() {
    return RoomRequestModel(
      uuid: const Uuid().v4(),
      rooms: 1,
      adults: [
        GuestDataRequest.adult(),
        GuestDataRequest.adult()
      ],
      children: [],
    );
  }

  RoomRequestModel copyWith({
    String? uuid,
    int? rooms,
    List<GuestDataRequest>? adults,
    List<GuestDataRequest>? children,
  }) {
    return RoomRequestModel(
      uuid: uuid ?? this.uuid,
      rooms: rooms ?? this.rooms,
      adults: adults ?? this.adults.map((e) => e.copyWith()).toList(),
      children: children ?? this.children.map((e) => e.copyWith()).toList(),
    );
  }

  RoomRequestModel copy() {
    return RoomRequestModel(
      uuid: uuid,
      rooms: rooms,
      adults: adults.map((e) => e.copyWith()).toList(),
      children: children.map((e) => e.copyWith()).toList(),
    );
  }


  Map<String, dynamic> toApi() {
    return {
      'Adults': adults.length,
      'Children': children.length,
      'ChildrenAges': children.map((e)=> e.age).toList(),
    };
  }


  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'rooms': rooms,
      'adults': adults.map((e) => e.toJson()).toList(),
      'children': children.map((e) => e.toJson()).toList(),
    };
  }

  factory RoomRequestModel.fromJson(dynamic json) {
    return RoomRequestModel(
      uuid: json['uuid'],
      rooms: json['rooms'],
      adults: ((json['adults'] ?? []) as List).map((map) => GuestDataRequest.fromJson(map)).toList(),
      children: ((json['children'] ?? []) as List).map((map) => GuestDataRequest.fromJson(map)).toList(),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is RoomRequestModel &&
              runtimeType == other.runtimeType &&
              uuid == other.uuid &&
              rooms == other.rooms &&
              adults == other.adults &&
              children == other.children;

  @override
  int get hashCode => uuid.hashCode ^ rooms.hashCode ^ adults.hashCode ^ children.hashCode;

  bool isMaxGuests() {
    var count = adults.length + children.length;
    if(count >= 5){
      return true;
    }
    return false;
  }

}


class HotelSearchBody {
  final String? checkIn;
  final String? checkOut;
  final List<String>? hotelCodes;
  final String? guestNationality;
  final List<PaxRoom>? paxRooms;
  final num? responseTime;
  final bool? isDetailedResponse;
  final SearchFilters? filters;

  HotelSearchBody({
    this.checkIn,
    this.checkOut,
    this.hotelCodes,
    this.guestNationality,
    this.paxRooms,
    this.responseTime,
    this.isDetailedResponse,
    this.filters,
  });

  factory HotelSearchBody.fromJson(Map<String, dynamic> json) {
    return HotelSearchBody(
      checkIn: json['CheckIn'],
      checkOut: json['CheckOut'],
      hotelCodes: (json['HotelCodes'] as String?)?.split(','),
      guestNationality: json['GuestNationality'],
      responseTime: json['ResponseTime'],
      isDetailedResponse: json['IsDetailedResponse'],
      filters: json['Filters'] != null ? SearchFilters.fromJson(json['Filters']) : null,
      paxRooms: (json['PaxRooms'] as List?)?.map((e) => PaxRoom.fromJson(e)).toList(),
    );
  }

  HotelSearchBody copyWith({
    String? checkIn,
    String? checkOut,
    List<String>? hotelCodes,
    String? guestNationality,
    List<PaxRoom>? paxRooms,
    int? responseTime,
    bool? isDetailedResponse,
    SearchFilters? filters,
  }) {
    return HotelSearchBody(
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
      hotelCodes: hotelCodes ?? this.hotelCodes,
      guestNationality: guestNationality ?? this.guestNationality,
      paxRooms: paxRooms ?? this.paxRooms,
      responseTime: responseTime ?? this.responseTime,
      isDetailedResponse: isDetailedResponse ?? this.isDetailedResponse,
      filters: filters ?? this.filters,
    );
  }

  Map<String, dynamic> toJson() => {
    "CheckIn": checkIn,
    "CheckOut": checkOut,
    "HotelCodes": hotelCodes?.join(','),
    "GuestNationality": guestNationality,
    "ResponseTime": responseTime,
    "IsDetailedResponse": isDetailedResponse,
    "Filters": filters?.toJson(),
    "PaxRooms": paxRooms?.map((e) => e.toJson()).toList(),
  };
}

class PaxRoom {
  final int? adults;
  final int? children;
  final List<int>? childrenAges;

  PaxRoom({this.adults, this.children, this.childrenAges});

  factory PaxRoom.fromJson(Map<String, dynamic> json) => PaxRoom(
    adults: json['Adults'],
    children: json['Children'],
    childrenAges: (json['ChildrenAges'] as List?)?.map((e) => int.tryParse(e.toString()) ?? 0).toList(),
  );

  Map<String, dynamic> toJson() => {
    "Adults": adults,
    "Children": children,
    "ChildrenAges": childrenAges ?? [],
  };
}

class SearchFilters {
  final bool? refundable;
  final int? noOfRooms;
  final String? mealType;

  SearchFilters({this.refundable, this.noOfRooms, this.mealType});

  factory SearchFilters.fromJson(Map<String, dynamic> json) => SearchFilters(
    refundable: json['Refundable'],
    noOfRooms: json['NoOfRooms'],
    mealType: json['MealType'],
  );

  Map<String, dynamic> toJson() => {
    "Refundable": refundable,
    "NoOfRooms": noOfRooms,
    "MealType": mealType,
  };
}