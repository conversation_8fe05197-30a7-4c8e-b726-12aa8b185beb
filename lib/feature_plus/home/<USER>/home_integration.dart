import 'package:get/get.dart';
import '../controllers/home_controller_plus.dart';
import '../services/home_service_plus.dart';
import '../services/quick_payment_service_plus.dart';
import '../routes/home_routes_plus.dart';

/// Home Integration - تكامل نظام الصفحة الرئيسية
class HomeIntegration {

  /// Initialize Home system
  static void initialize() {
    try {
      // Register services
      if (!Get.isRegistered<HomeServicePlus>()) {
        Get.put(HomeServicePlus(), permanent: true);
      }
      if (!Get.isRegistered<HomeControllerPlus>()) {
        Get.put(HomeControllerPlus(), permanent: true);
      }
      // Controllers
      print('✅ [HOME_INTEGRATION] Home system initialized successfully');
    } catch (e) {
      print('❌ [HOME_INTEGRATION] Error initializing Home system: $e');
    }
  }

  /// Get Home routes
  static List<GetPage> getRoutes() {
    return HomeNavigator.routes;
  }

  /// Navigate to Home
  static void navigateToHome() {
    Get.toNamed(HomeNavigator.home);
  }

  static void offAndHome() {
    Get.offNamed(HomeNavigator.home);
  }

  /// Check if Home controller is ready
  static bool isHomeControllerReady() {
    return Get.isRegistered<HomeControllerPlus>();
  }

  /// Get Home controller instance
  static HomeControllerPlus? getHomeController() {
    try {
      return Get.find<HomeControllerPlus>();
    } catch (e) {
      return null;
    }
  }

}
