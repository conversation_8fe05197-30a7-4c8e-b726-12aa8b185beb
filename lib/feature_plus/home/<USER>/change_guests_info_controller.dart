
import 'package:flutter/cupertino.dart';

import '../../../core/core.dart';
import '../models/guests_search.dart';
import '../widgets/room_guest_card.dart';
import 'change_guests_info_state.dart';

class ChangeGuestsInfoController extends GetxController {
  final ChangeGuestsInfoState state = ChangeGuestsInfoState();


  void init({
    required dynamic arguments,
  }) {
    this.arguments = arguments;

    state.rooms.value = (arguments as List<RoomRequestModel>).map((e) => e.copy()).toList();
  }

  static ChangeGuestsInfoController get to =>
      Get.find<ChangeGuestsInfoController>();

  dynamic arguments;


  void removeRoom(RoomRequestModel room) {
    int roomIndex = state.rooms.indexWhere((e) => e.uuid == room.uuid);
    if (roomIndex != -1) {
      state.roomsListKey.currentState!.removeItem(
        roomIndex,
        (context, animation) {
          return FadeTransition(
            opacity: animation,
            child: SizeTransition(
              sizeFactor: animation,
              axis: Axis.vertical,
              child: RoomGuestCard(
                roomModel: room,
                removeRoom: () => removeRoom(room),
              ),
            ),
          );
        }
      );
      state.rooms.removeWhere((e) => e.uuid == room.uuid);
      state.rooms.refresh();
    }
  }

  void addRoom() {
    if (state.rooms.length >= 3) return;
    state.rooms.add(RoomRequestModel.defaultRoom());
    state.roomsListKey.currentState!.insertItem(state.rooms.length - 1);
  }


  void addAdult(RoomRequestModel room) {
    if(room.isMaxGuests()){
      return;
    }
    final roomIndex = state.rooms.indexOf(room);
    if (roomIndex != -1) {
      final roomModel = state.rooms[roomIndex];
      roomModel.adults.add(GuestDataRequest.adult());
      state.rooms[roomIndex] = roomModel;
    }
  }

  void removeAdult(RoomRequestModel room) {
    final roomIndex = state.rooms.indexOf(room);
    if (roomIndex != -1) {
      final roomModel = state.rooms[roomIndex];
      roomModel.adults.removeLast();
      state.rooms[roomIndex] = roomModel;
    }
  }

  void addChild(RoomRequestModel room, int age) {
    final roomIndex = state.rooms.indexOf(room);
    if (roomIndex != -1) {
      final roomModel = state.rooms[roomIndex];
      roomModel.children.add(GuestDataRequest.child(age: age));
      state.rooms[roomIndex] = roomModel;
    }
  }

  void removeChild(RoomRequestModel room) {
    final roomIndex = state.rooms.indexOf(room);
    if (roomIndex != -1) {
      final roomModel = state.rooms[roomIndex];
      roomModel.children.removeLast();
      state.rooms[roomIndex] = roomModel;
    }
  }


}
