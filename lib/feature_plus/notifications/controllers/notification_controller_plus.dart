import 'package:get/get.dart';
import '../models/notification_models.dart';
import '../services/notification_service_plus.dart';

/// كونترولر الإشعارات
class NotificationControllerPlus extends GetxController {
  static NotificationControllerPlus get instance =>
      Get.find<NotificationControllerPlus>();

  late final NotificationServicePlus _notificationService;

  // حالات التحميل
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingMore = false.obs;
  final RxBool _isRefreshing = false.obs;

  // فلاتر البحث
  final RxBool _showUnreadOnly = false.obs;
  final RxString _selectedTypeId = ''.obs;
  final RxInt _currentPage = 1.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  bool get isRefreshing => _isRefreshing.value;
  bool get showUnreadOnly => _showUnreadOnly.value;
  String get selectedTypeId => _selectedTypeId.value;
  int get currentPage => _currentPage.value;

  // البيانات من الخدمة
  List<NotificationPlus> get notifications =>
      _notificationService.notifications;
  List<NotificationPreferencePlus> get preferences =>
      _notificationService.preferences;
  int get unreadCount => _notificationService.unreadCount;
  Map<String, List<NotificationPlus>> get groupedNotifications =>
      _notificationService.groupedNotifications;
  bool get isLoadingPreferences => _notificationService.isLoadingPreferences;

  // الإشعارات المفلترة
  List<NotificationPlus> get filteredNotifications {
    var filtered = notifications;

    if (_showUnreadOnly.value) {
      filtered = filtered.where((notif) => !notif.inAppRead).toList();
    }

    if (_selectedTypeId.value.isNotEmpty) {
      filtered = filtered
          .where((notif) => notif.typeId == _selectedTypeId.value)
          .toList();
    }

    return filtered;
  }

  // أنواع الإشعارات المتاحة
  List<String> get availableTypes {
    final types = notifications.map((notif) => notif.typeId).toSet().toList();
    types.sort();
    return types;
  }

  @override
  void onInit() {
    super.onInit();
    print(
        '🚀 [NOTIFICATION_CONTROLLER] NotificationControllerPlus initialized');
    _initializeDependencies();
    _loadInitialData();
  }

  /// تهيئة التبعيات
  void _initializeDependencies() {
    try {
      _notificationService = Get.find<NotificationServicePlus>();
      print(
          '✅ [NOTIFICATION_CONTROLLER] Found existing NotificationServicePlus');
    } catch (e) {
      if (!Get.isRegistered<NotificationServicePlus>()) {
        Get.put(NotificationServicePlus(), permanent: true);
        print(
            '✅ [NOTIFICATION_CONTROLLER] Created new NotificationServicePlus');
      }
      _notificationService = Get.find<NotificationServicePlus>();
    }
  }

  /// تحميل البيانات الأولية
  Future<void> _loadInitialData() async {
    if (_isLoading.value) return;

    try {
      _isLoading.value = true;
      print('🚀 [NOTIFICATION_CONTROLLER] Loading initial data...');

      await Future.wait([
        loadNotifications(refresh: true),
        loadPreferences(),
      ]);

      print('✅ [NOTIFICATION_CONTROLLER] Initial data loaded successfully');
    } catch (e) {
      print('❌ [NOTIFICATION_CONTROLLER] Error loading initial data: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الإشعارات
  Future<void> loadNotifications({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage.value = 1;
        print('🔄 [NOTIFICATION_CONTROLLER] Refreshing notifications...');
      } else {
        _isLoadingMore.value = true;
        print('📄 [NOTIFICATION_CONTROLLER] Loading more notifications...');
      }

      await _notificationService.getUserNotifications(
        page: _currentPage.value,
        limit: 20,
        unreadOnly: _showUnreadOnly.value,
        typeId: _selectedTypeId.value.isEmpty ? null : _selectedTypeId.value,
      );

      if (!refresh) {
        _currentPage.value++;
      }

      print('✅ [NOTIFICATION_CONTROLLER] Notifications loaded');
    } catch (e) {
      print('❌ [NOTIFICATION_CONTROLLER] Error loading notifications: $e');
    } finally {
      _isLoadingMore.value = false;
    }
  }

  /// تحميل الإعدادات
  Future<void> loadPreferences() async {
    try {
      print('⚙️ [NOTIFICATION_CONTROLLER] Loading preferences...');
      await _notificationService.getUserPreferences();
      print('✅ [NOTIFICATION_CONTROLLER] Preferences loaded');
    } catch (e) {
      print('❌ [NOTIFICATION_CONTROLLER] Error loading preferences: $e');
    }
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    if (_isRefreshing.value) return;

    try {
      _isRefreshing.value = true;
      print('🔄 [NOTIFICATION_CONTROLLER] Refreshing all data...');

      await _notificationService.refresh();
      _currentPage.value = 1;

      print('✅ [NOTIFICATION_CONTROLLER] Data refreshed successfully');
    } catch (e) {
      print('❌ [NOTIFICATION_CONTROLLER] Error refreshing data: $e');
    } finally {
      _isRefreshing.value = false;
    }
  }

  /// تعليم إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      print(
          '📖 [NOTIFICATION_CONTROLLER] Marking notification as read: $notificationId');

      final success =
          await _notificationService.markNotificationAsRead(notificationId);

      if (success) {
        print('✅ [NOTIFICATION_CONTROLLER] Notification marked as read');
        // تحديث عدد الإشعارات غير المقروءة
        await _notificationService.getUnreadCount();
      } else {
        print(
            '❌ [NOTIFICATION_CONTROLLER] Failed to mark notification as read');
      }
    } catch (e) {
      print(
          '❌ [NOTIFICATION_CONTROLLER] Error marking notification as read: $e');
    }
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    try {
      print(
          '📖 [NOTIFICATION_CONTROLLER] Marking all notifications as read...');

      final success = await _notificationService.markAllNotificationsAsRead(
        typeId: _selectedTypeId.value.isEmpty ? null : _selectedTypeId.value,
      );

      if (success) {
        print('✅ [NOTIFICATION_CONTROLLER] All notifications marked as read');
        Get.snackbar(
          'notifications.success'.tr,
          'notifications.allMarkedAsRead'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        print(
            '❌ [NOTIFICATION_CONTROLLER] Failed to mark all notifications as read');
        Get.snackbar(
          'notifications.error'.tr,
          'notifications.failedToMarkAsRead'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      print(
          '❌ [NOTIFICATION_CONTROLLER] Error marking all notifications as read: $e');
      Get.snackbar(
        'notifications.error'.tr,
        'notifications.unexpectedError'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      print(
          '🗑️ [NOTIFICATION_CONTROLLER] Deleting notification: $notificationId');

      final success =
          await _notificationService.deleteNotification(notificationId);

      if (success) {
        print('✅ [NOTIFICATION_CONTROLLER] Notification deleted');
        Get.snackbar(
          'notifications.success'.tr,
          'notifications.notificationDeleted'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        print('❌ [NOTIFICATION_CONTROLLER] Failed to delete notification');
        Get.snackbar(
          'notifications.error'.tr,
          'notifications.failedToDelete'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      print('❌ [NOTIFICATION_CONTROLLER] Error deleting notification: $e');
      Get.snackbar(
        'notifications.error'.tr,
        'notifications.unexpectedError'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updatePreference(
    String typeId,
    NotificationPreferencePlus preference,
  ) async {
    try {
      print('⚙️ [NOTIFICATION_CONTROLLER] Updating preference: $typeId');

      final success = await _notificationService.updateNotificationPreference(
        typeId,
        preference,
      );

      if (success) {
        print('✅ [NOTIFICATION_CONTROLLER] Preference updated');
        Get.snackbar(
          'notifications.success'.tr,
          'notifications.preferencesUpdated'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        print('❌ [NOTIFICATION_CONTROLLER] Failed to update preference');
        Get.snackbar(
          'notifications.error'.tr,
          'notifications.failedToUpdatePreferences'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      print('❌ [NOTIFICATION_CONTROLLER] Error updating preference: $e');
      Get.snackbar(
        'notifications.error'.tr,
        'notifications.unexpectedError'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// تسجيل device token
  Future<void> registerDeviceToken(String fcmToken, String platform) async {
    try {
      print('📱 [NOTIFICATION_CONTROLLER] Registering device token...');

      final success =
          await _notificationService.registerDeviceToken(fcmToken, platform);

      if (success) {
        print('✅ [NOTIFICATION_CONTROLLER] Device token registered');
      } else {
        print('❌ [NOTIFICATION_CONTROLLER] Failed to register device token');
      }
    } catch (e) {
      print('❌ [NOTIFICATION_CONTROLLER] Error registering device token: $e');
    }
  }

  /// تغيير فلتر الإشعارات غير المقروءة
  void toggleUnreadOnly() {
    _showUnreadOnly.value = !_showUnreadOnly.value;
    print(
        '🔍 [NOTIFICATION_CONTROLLER] Unread only filter: ${_showUnreadOnly.value}');
    loadNotifications(refresh: true);
  }

  /// تغيير فلتر نوع الإشعار
  void setTypeFilter(String typeId) {
    _selectedTypeId.value = typeId;
    print('🔍 [NOTIFICATION_CONTROLLER] Type filter: $typeId');
    loadNotifications(refresh: true);
  }

  /// مسح جميع الفلاتر
  void clearFilters() {
    _showUnreadOnly.value = false;
    _selectedTypeId.value = '';
    print('🔍 [NOTIFICATION_CONTROLLER] Filters cleared');
    loadNotifications(refresh: true);
  }

  /// معالجة النقر على إشعار
  void onNotificationTap(NotificationPlus notification) {
    // تعليم الإشعار كمقروء إذا لم يكن مقروءاً
    if (!notification.inAppRead) {
      markAsRead(notification.id);
    }

    // التنقل حسب نوع الإشعار
    _handleNotificationNavigation(notification);
  }

  /// معالجة التنقل حسب نوع الإشعار
  void _handleNotificationNavigation(NotificationPlus notification) {
    final data = notification.data;

    switch (notification.type.name) {
      case 'booking_confirmation':
      case 'booking_reminder':
      case 'booking_cancellation':
        if (data.containsKey('bookingId')) {
          // التنقل لتفاصيل الحجز
          Get.toNamed('/booking-details', arguments: data['bookingId']);
        }
        break;

      case 'payment_confirmation':
      case 'payment_failed':
        if (data.containsKey('paymentId')) {
          // التنقل لتاريخ المدفوعات
          Get.toNamed('/payment-history', arguments: data['paymentId']);
        }
        break;

      case 'special_offer':
        if (data.containsKey('offerId')) {
          // التنقل للعروض الخاصة
          Get.toNamed('/offers', arguments: data['offerId']);
        }
        break;

      case 'hotel_review_request':
        if (data.containsKey('hotelId')) {
          // التنقل لصفحة تقييم الفندق
          Get.toNamed('/hotel-review', arguments: data['hotelId']);
        }
        break;

      default:
        // التنقل لصفحة الإشعارات
        print(
            '🔗 [NOTIFICATION_CONTROLLER] Unknown notification type: ${notification.type.name}');
    }
  }

  /// إضافة إشعار جديد (للاستخدام مع Push Notifications)
  void addNewNotification(NotificationPlus notification) {
    _notificationService.addNotification(notification);
    print(
        '📢 [NOTIFICATION_CONTROLLER] New notification added: ${notification.title}');
  }
}
