import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/notification_controller_plus.dart';
import '../models/notification_models.dart';
import 'widgets/notification_item_widget.dart';
import 'widgets/notification_filter_widget.dart';
import '../../../core/theme/color_manager.dart';

/// صفحة الإشعارات
class NotificationsScreenPlus extends StatelessWidget {
  const NotificationsScreenPlus({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NotificationControllerPlus>(
      init: NotificationControllerPlus(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: ColorManager.backgroundColor,
          appBar: _buildAppBar(controller),
          body: _buildBody(controller),
        );
      },
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar(NotificationControllerPlus controller) {
    return AppBar(
      backgroundColor: ColorManager.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      title: Row(
        children: [
          Text(
            'notifications.title'.tr,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          Obx(() {
            final unreadCount = controller.unreadCount;
            if (unreadCount > 0) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: ColorManager.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
      actions: [
        // زر تعليم الكل كمقروء
        Obx(() {
          final hasUnread = controller.unreadCount > 0;
          return hasUnread
              ? IconButton(
                  onPressed: () => _showMarkAllAsReadDialog(controller),
                  icon: const Icon(Icons.done_all),
                  tooltip: 'notifications.markAllAsRead'.tr,
                )
              : const SizedBox.shrink();
        }),

        // زر الفلاتر
        IconButton(
          onPressed: () => _showFiltersBottomSheet(controller),
          icon: const Icon(Icons.filter_list),
          tooltip: 'notifications.filters'.tr,
        ),

        // زر الإعدادات
        IconButton(
          onPressed: () => Get.toNamed('/notification-preferences'),
          icon: const Icon(Icons.settings),
          tooltip: 'notifications.settings'.tr,
        ),
      ],
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody(NotificationControllerPlus controller) {
    return Obx(() {
      if (controller.isLoading) {
        return _buildLoadingState();
      }

      final notifications = controller.filteredNotifications;

      if (notifications.isEmpty) {
        return _buildEmptyState(controller);
      }

      return _buildNotificationsList(controller, notifications);
    });
  }

  /// حالة التحميل
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('notifications.loading'),
        ],
      ),
    );
  }

  /// حالة عدم وجود إشعارات
  Widget _buildEmptyState(NotificationControllerPlus controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            controller.showUnreadOnly
                ? 'notifications.noUnreadNotifications'.tr
                : 'notifications.noNotifications'.tr,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'notifications.noNotificationsSubtitle'.tr,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          if (controller.showUnreadOnly || controller.selectedTypeId.isNotEmpty)
            ElevatedButton(
              onPressed: controller.clearFilters,
              child: Text('notifications.clearFilters'.tr),
            ),
        ],
      ),
    );
  }

  /// قائمة الإشعارات
  Widget _buildNotificationsList(
    NotificationControllerPlus controller,
    List<NotificationPlus> notifications,
  ) {
    return RefreshIndicator(
      onRefresh: controller.refresh,
      child: CustomScrollView(
        slivers: [
          // فلاتر الإشعارات
          SliverToBoxAdapter(
            child: NotificationFilterWidget(controller: controller),
          ),

          // قائمة الإشعارات
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index == notifications.length) {
                  // زر تحميل المزيد
                  return _buildLoadMoreButton(controller);
                }

                final notification = notifications[index];
                return NotificationItemWidget(
                  notification: notification,
                  onTap: () => controller.onNotificationTap(notification),
                  onMarkAsRead: () => controller.markAsRead(notification.id),
                  onDelete: () => _showDeleteDialog(controller, notification),
                );
              },
              childCount: notifications.length + 1,
            ),
          ),
        ],
      ),
    );
  }

  /// زر تحميل المزيد
  Widget _buildLoadMoreButton(NotificationControllerPlus controller) {
    return Obx(() {
      if (controller.isLoadingMore) {
        return const Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        );
      }

      return Padding(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: () => controller.loadNotifications(),
          child: Text('notifications.loadMore'.tr),
        ),
      );
    });
  }

  /// عرض حوار تأكيد تعليم الكل كمقروء
  void _showMarkAllAsReadDialog(NotificationControllerPlus controller) {
    Get.dialog(
      AlertDialog(
        title: Text('notifications.markAllAsRead'.tr),
        content: Text('notifications.markAllAsReadConfirmation'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('common.cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.markAllAsRead();
            },
            child: Text('common.confirm'.tr),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تأكيد الحذف
  void _showDeleteDialog(
    NotificationControllerPlus controller,
    NotificationPlus notification,
  ) {
    Get.dialog(
      AlertDialog(
        title: Text('notifications.deleteNotification'.tr),
        content: Text('notifications.deleteConfirmation'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('common.cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteNotification(notification.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorManager.red,
            ),
            child: Text('common.delete'.tr),
          ),
        ],
      ),
    );
  }

  /// عرض BottomSheet للفلاتر
  void _showFiltersBottomSheet(NotificationControllerPlus controller) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'notifications.filters'.tr,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // فلتر الإشعارات غير المقروءة
            Obx(() => SwitchListTile(
                  title: Text('notifications.showUnreadOnly'.tr),
                  subtitle: Text('notifications.showUnreadOnlySubtitle'.tr),
                  value: controller.showUnreadOnly,
                  onChanged: (_) => controller.toggleUnreadOnly(),
                )),

            const Divider(),

            // فلتر نوع الإشعار
            Text(
              'notifications.notificationType'.tr,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),

            Obx(() {
              final types = controller.availableTypes;
              return Wrap(
                spacing: 8,
                children: [
                  // خيار "الكل"
                  FilterChip(
                    label: Text('notifications.all'.tr),
                    selected: controller.selectedTypeId.isEmpty,
                    onSelected: (_) => controller.setTypeFilter(''),
                  ),

                  // أنواع الإشعارات
                  ...types.map((type) => FilterChip(
                        label: Text(_getTypeDisplayName(type)),
                        selected: controller.selectedTypeId == type,
                        onSelected: (_) => controller.setTypeFilter(type),
                      )),
                ],
              );
            }),

            const SizedBox(height: 20),

            // أزرار الإجراءات
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: controller.clearFilters,
                    child: Text('notifications.clearFilters'.tr),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Get.back(),
                    child: Text('common.apply'.tr),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على اسم نوع الإشعار للعرض
  String _getTypeDisplayName(String typeId) {
    switch (typeId) {
      case 'booking_confirmation':
        return 'notifications.types.bookingConfirmation'.tr;
      case 'booking_reminder':
        return 'notifications.types.bookingReminder'.tr;
      case 'special_offer':
        return 'notifications.types.specialOffer'.tr;
      case 'booking_cancellation':
        return 'notifications.types.bookingCancellation'.tr;
      case 'payment_confirmation':
        return 'notifications.types.paymentConfirmation'.tr;
      case 'payment_failed':
        return 'notifications.types.paymentFailed'.tr;
      case 'hotel_review_request':
        return 'notifications.types.hotelReviewRequest'.tr;
      default:
        return typeId;
    }
  }
}
