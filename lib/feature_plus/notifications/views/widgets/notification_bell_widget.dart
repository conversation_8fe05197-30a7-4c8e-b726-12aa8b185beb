import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/notification_controller_plus.dart';
import '../../../../core/theme/color_manager.dart';

/// أيقونة الإشعارات في شريط التطبيق
class NotificationBellWidget extends StatelessWidget {
  final Color? iconColor;
  final double? iconSize;

  const NotificationBellWidget({
    super.key,
    this.iconColor,
    this.iconSize = 24,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NotificationControllerPlus>(
      init: NotificationControllerPlus(),
      builder: (controller) {
        return Stack(
          children: [
            IconButton(
              onPressed: () => Get.toNamed('/notifications'),
              icon: Icon(
                Icons.notifications_outlined,
                color: iconColor ?? Colors.white,
                size: iconSize,
              ),
              tooltip: 'notifications.title'.tr,
            ),

            // Badge للإشعارات غير المقروءة
            Obx(() {
              final unreadCount = controller.unreadCount;
              if (unreadCount > 0) {
                return Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: ColorManager.red,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: Colors.white,
                        width: 1,
                      ),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      unreadCount > 99 ? '99+' : unreadCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        );
      },
    );
  }
}
