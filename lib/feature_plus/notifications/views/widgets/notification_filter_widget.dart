import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/notification_controller_plus.dart';
import '../../../../core/theme/color_manager.dart';

/// عنصر فلاتر الإشعارات
class NotificationFilterWidget extends StatelessWidget {
  final NotificationControllerPlus controller;

  const NotificationFilterWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final hasActiveFilters =
          controller.showUnreadOnly || controller.selectedTypeId.isNotEmpty;

      if (!hasActiveFilters) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ColorManager.primary.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: ColorManager.primary.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الفلاتر
            Row(
              children: [
                Icon(
                  Icons.filter_list,
                  size: 20,
                  color: ColorManager.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'notifications.activeFilters'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: ColorManager.primary,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: controller.clearFilters,
                  child: Text(
                    'notifications.clearAll'.tr,
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorManager.primary,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // الفلاتر النشطة
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                // فلتر الإشعارات غير المقروءة
                if (controller.showUnreadOnly)
                  _buildFilterChip(
                    label: 'notifications.unreadOnly'.tr,
                    onRemove: controller.toggleUnreadOnly,
                  ),

                // فلتر نوع الإشعار
                if (controller.selectedTypeId.isNotEmpty)
                  _buildFilterChip(
                    label: _getTypeDisplayName(controller.selectedTypeId),
                    onRemove: () => controller.setTypeFilter(''),
                  ),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// بناء chip الفلتر
  Widget _buildFilterChip({
    required String label,
    required VoidCallback onRemove,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: ColorManager.primary,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: onRemove,
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم نوع الإشعار للعرض
  String _getTypeDisplayName(String typeId) {
    switch (typeId) {
      case 'booking_confirmation':
        return 'notifications.types.bookingConfirmation'.tr;
      case 'booking_reminder':
        return 'notifications.types.bookingReminder'.tr;
      case 'special_offer':
        return 'notifications.types.specialOffer'.tr;
      case 'booking_cancellation':
        return 'notifications.types.bookingCancellation'.tr;
      case 'payment_confirmation':
        return 'notifications.types.paymentConfirmation'.tr;
      case 'payment_failed':
        return 'notifications.types.paymentFailed'.tr;
      case 'hotel_review_request':
        return 'notifications.types.hotelReviewRequest'.tr;
      default:
        return typeId;
    }
  }
}
