import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/notification_models.dart';
import '../../../../core/theme/color_manager.dart';

/// عنصر الإشعار في القائمة
class NotificationItemWidget extends StatelessWidget {
  final NotificationPlus notification;
  final VoidCallback onTap;
  final VoidCallback onMarkAsRead;
  final VoidCallback onDelete;

  const NotificationItemWidget({
    super.key,
    required this.notification,
    required this.onTap,
    required this.onMarkAsRead,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: notification.inAppRead
            ? Colors.white
            : ColorManager.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.inAppRead
              ? Colors.grey.withOpacity(0.2)
              : ColorManager.primary.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // أيقونة الإشعار
                _buildNotificationIcon(),
                const SizedBox(width: 12),

                // محتوى الإشعار
                Expanded(
                  child: _buildNotificationContent(),
                ),

                // إجراءات الإشعار
                _buildNotificationActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء أيقونة الإشعار
  Widget _buildNotificationIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: _getNotificationColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Center(
        child: Text(
          notification.icon,
          style: const TextStyle(fontSize: 24),
        ),
      ),
    );
  }

  /// بناء محتوى الإشعار
  Widget _buildNotificationContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان الإشعار
        Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: notification.inAppRead
                      ? FontWeight.w500
                      : FontWeight.bold,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // مؤشر عدم القراءة
            if (!notification.inAppRead)
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: ColorManager.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
          ],
        ),

        const SizedBox(height: 4),

        // رسالة الإشعار
        Text(
          notification.message,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
            height: 1.4,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),

        const SizedBox(height: 8),

        // معلومات إضافية
        Row(
          children: [
            // نوع الإشعار
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getNotificationColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                notification.type.displayName,
                style: TextStyle(
                  fontSize: 12,
                  color: _getNotificationColor(),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            const Spacer(),

            // وقت الإشعار
            Text(
              notification.timeAgo,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),

        // أولوية الإشعار
        if (notification.priority == NotificationPriority.high ||
            notification.priority == NotificationPriority.urgent)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              children: [
                Icon(
                  notification.priority == NotificationPriority.urgent
                      ? Icons.priority_high
                      : Icons.flag,
                  size: 16,
                  color: notification.priority == NotificationPriority.urgent
                      ? ColorManager.red
                      : Colors.orange,
                ),
                const SizedBox(width: 4),
                Text(
                  notification.priority == NotificationPriority.urgent
                      ? 'notifications.priority.urgent'.tr
                      : 'notifications.priority.high'.tr,
                  style: TextStyle(
                    fontSize: 12,
                    color: notification.priority == NotificationPriority.urgent
                        ? ColorManager.red
                        : Colors.orange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// بناء إجراءات الإشعار
  Widget _buildNotificationActions() {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: Colors.grey[600],
        size: 20,
      ),
      onSelected: (value) {
        switch (value) {
          case 'mark_read':
            onMarkAsRead();
            break;
          case 'delete':
            onDelete();
            break;
        }
      },
      itemBuilder: (context) => [
        if (!notification.inAppRead)
          PopupMenuItem<String>(
            value: 'mark_read',
            child: Row(
              children: [
                const Icon(Icons.done, size: 20),
                const SizedBox(width: 8),
                Text('notifications.markAsRead'.tr),
              ],
            ),
          ),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 20, color: ColorManager.red),
              const SizedBox(width: 8),
              Text(
                'notifications.delete'.tr,
                style: TextStyle(color: ColorManager.red),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// الحصول على لون الإشعار حسب النوع
  Color _getNotificationColor() {
    switch (notification.type.name) {
      case 'booking_confirmation':
        return Colors.green;
      case 'booking_reminder':
        return Colors.blue;
      case 'special_offer':
        return Colors.purple;
      case 'booking_cancellation':
        return ColorManager.red;
      case 'payment_confirmation':
        return Colors.teal;
      case 'payment_failed':
        return ColorManager.red;
      case 'hotel_review_request':
        return Colors.orange;
      default:
        return ColorManager.primary;
    }
  }
}
