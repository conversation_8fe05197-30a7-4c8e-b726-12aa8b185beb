import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/notification_controller_plus.dart';
import '../models/notification_models.dart';
import '../../../core/theme/color_manager.dart';

/// صفحة إعدادات الإشعارات
class NotificationPreferencesScreenPlus extends StatelessWidget {
  const NotificationPreferencesScreenPlus({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NotificationControllerPlus>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: ColorManager.backgroundColor,
          appBar: _buildAppBar(),
          body: _buildBody(controller),
        );
      },
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: ColorManager.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      title: Text(
        'notifications.preferences.title'.tr,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody(NotificationControllerPlus controller) {
    return Obx(() {
      if (controller.isLoadingPreferences) {
        return _buildLoadingState();
      }

      final preferences = controller.preferences;

      if (preferences.isEmpty) {
        return _buildEmptyState();
      }

      return _buildPreferencesList(controller, preferences);
    });
  }

  /// حالة التحميل
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('notifications.preferences.loading'),
        ],
      ),
    );
  }

  /// حالة عدم وجود إعدادات
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.settings_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'notifications.preferences.noPreferences'.tr,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// قائمة الإعدادات
  Widget _buildPreferencesList(
    NotificationControllerPlus controller,
    List<NotificationPreferencePlus> preferences,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: preferences.length,
      itemBuilder: (context, index) {
        final preference = preferences[index];
        return _buildPreferenceCard(controller, preference);
      },
    );
  }

  /// بطاقة إعدادات نوع إشعار
  Widget _buildPreferenceCard(
    NotificationControllerPlus controller,
    NotificationPreferencePlus preference,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان نوع الإشعار
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        preference.typeName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getCategoryColor(preference.category)
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getCategoryDisplayName(preference.category),
                          style: TextStyle(
                            fontSize: 12,
                            color: _getCategoryColor(preference.category),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // إعدادات الإشعارات
            _buildNotificationToggle(
              controller,
              preference,
              'notifications.preferences.pushNotifications'.tr,
              'notifications.preferences.pushNotificationsSubtitle'.tr,
              preference.pushEnabled,
              (value) => _updatePreference(
                controller,
                preference.copyWith(pushEnabled: value),
              ),
              Icons.notifications_active,
            ),

            const SizedBox(height: 16),

            _buildNotificationToggle(
              controller,
              preference,
              'notifications.preferences.emailNotifications'.tr,
              'notifications.preferences.emailNotificationsSubtitle'.tr,
              preference.emailEnabled,
              (value) => _updatePreference(
                controller,
                preference.copyWith(emailEnabled: value),
              ),
              Icons.email,
            ),

            const SizedBox(height: 16),

            _buildNotificationToggle(
              controller,
              preference,
              'notifications.preferences.inAppNotifications'.tr,
              'notifications.preferences.inAppNotificationsSubtitle'.tr,
              preference.inAppEnabled,
              (value) => _updatePreference(
                controller,
                preference.copyWith(inAppEnabled: value),
              ),
              Icons.app_registration,
            ),

            // الساعات الهادئة
            if (preference.pushEnabled) ...[
              const SizedBox(height: 20),
              const Divider(),
              const SizedBox(height: 16),
              Text(
                'notifications.preferences.quietHours'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'notifications.preferences.quietHoursSubtitle'.tr,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildTimeSelector(
                      controller,
                      preference,
                      'notifications.preferences.from'.tr,
                      preference.quietHoursStart,
                      (time) => _updatePreference(
                        controller,
                        preference.copyWith(quietHoursStart: time),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildTimeSelector(
                      controller,
                      preference,
                      'notifications.preferences.to'.tr,
                      preference.quietHoursEnd,
                      (time) => _updatePreference(
                        controller,
                        preference.copyWith(quietHoursEnd: time),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء مفتاح تبديل الإشعارات
  Widget _buildNotificationToggle(
    NotificationControllerPlus controller,
    NotificationPreferencePlus preference,
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          color: value ? ColorManager.primary : Colors.grey,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: ColorManager.primary,
        ),
      ],
    );
  }

  /// بناء محدد الوقت
  Widget _buildTimeSelector(
    NotificationControllerPlus controller,
    NotificationPreferencePlus preference,
    String label,
    String currentTime,
    Function(String) onTimeChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _showTimePicker(currentTime, onTimeChanged),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.access_time, size: 20),
                const SizedBox(width: 8),
                Text(
                  currentTime,
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// عرض منتقي الوقت
  void _showTimePicker(
      String currentTime, Function(String) onTimeChanged) async {
    final timeParts = currentTime.split(':');
    final initialTime = TimeOfDay(
      hour: int.parse(timeParts[0]),
      minute: int.parse(timeParts[1]),
    );

    final selectedTime = await showTimePicker(
      context: Get.context!,
      initialTime: initialTime,
    );

    if (selectedTime != null) {
      final formattedTime =
          '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
      onTimeChanged(formattedTime);
    }
  }

  /// تحديث الإعدادات
  void _updatePreference(
    NotificationControllerPlus controller,
    NotificationPreferencePlus preference,
  ) {
    controller.updatePreference(preference.typeId, preference);
  }

  /// الحصول على لون الفئة
  Color _getCategoryColor(String category) {
    switch (category.toUpperCase()) {
      case 'BOOKING':
        return Colors.blue;
      case 'PAYMENT':
        return Colors.green;
      case 'MARKETING':
        return Colors.purple;
      case 'SYSTEM':
        return Colors.orange;
      default:
        return ColorManager.primary;
    }
  }

  /// الحصول على اسم الفئة للعرض
  String _getCategoryDisplayName(String category) {
    switch (category.toUpperCase()) {
      case 'BOOKING':
        return 'notifications.categories.booking'.tr;
      case 'PAYMENT':
        return 'notifications.categories.payment'.tr;
      case 'MARKETING':
        return 'notifications.categories.marketing'.tr;
      case 'SYSTEM':
        return 'notifications.categories.system'.tr;
      default:
        return category;
    }
  }
}
