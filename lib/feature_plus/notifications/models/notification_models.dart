// نماذج البيانات لنظام الإشعارات
import 'package:get/get.dart';

/// نموذج الإشعار الأساسي
class NotificationPlus {
  final String id;
  final String typeId;
  final String title;
  final String message;
  final Map<String, dynamic> data;
  final NotificationPriority priority;
  final NotificationStatus status;
  final bool inAppRead;
  final DateTime? inAppReadAt;
  final DateTime createdAt;
  final NotificationTypePlus type;

  const NotificationPlus({
    required this.id,
    required this.typeId,
    required this.title,
    required this.message,
    required this.data,
    required this.priority,
    required this.status,
    required this.inAppRead,
    this.inAppReadAt,
    required this.createdAt,
    required this.type,
  });

  factory NotificationPlus.fromJson(Map<String, dynamic> json) {
    return NotificationPlus(
      id: json['id'] ?? '',
      typeId: json['typeId'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      priority: NotificationPriority.fromString(json['priority'] ?? 'MEDIUM'),
      status: NotificationStatus.fromString(json['status'] ?? 'DELIVERED'),
      inAppRead: json['inAppRead'] ?? false,
      inAppReadAt: json['inAppReadAt'] != null
          ? DateTime.parse(json['inAppReadAt'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      type: NotificationTypePlus.fromJson(json['type'] ?? {}),
    );
  }

  /// إنشاء من استجابة API الجديدة
  factory NotificationPlus.fromApiJson(Map<String, dynamic> json) {
    return NotificationPlus(
      id: json['id'] ?? '',
      typeId: json['typeId'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      priority: NotificationPriority.fromString(json['priority'] ?? 'NORMAL'),
      status: NotificationStatus.fromString(json['status'] ?? 'SENT'),
      inAppRead: json['inAppRead'] ?? false,
      inAppReadAt: json['inAppReadAt'] != null
          ? DateTime.parse(json['inAppReadAt'])
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      type: json['type'] != null
          ? NotificationTypePlus(
              name: json['type']['name'] ?? '',
              category: json['type']['category'] ?? 'SYSTEM',
              displayName: json['type']['description'] ?? json['title'] ?? '',
            )
          : NotificationTypePlus(
              name: json['typeId'] ?? 'general',
              category: 'SYSTEM',
              displayName: json['title'] ?? 'Notification',
            ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'typeId': typeId,
      'title': title,
      'message': message,
      'data': data,
      'priority': priority.name,
      'status': status.name,
      'inAppRead': inAppRead,
      'inAppReadAt': inAppReadAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'type': type.toJson(),
    };
  }

  /// نسخ الإشعار مع تحديث بعض الخصائص
  NotificationPlus copyWith({
    String? id,
    String? typeId,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    NotificationPriority? priority,
    NotificationStatus? status,
    bool? inAppRead,
    DateTime? inAppReadAt,
    DateTime? createdAt,
    NotificationTypePlus? type,
  }) {
    return NotificationPlus(
      id: id ?? this.id,
      typeId: typeId ?? this.typeId,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      inAppRead: inAppRead ?? this.inAppRead,
      inAppReadAt: inAppReadAt ?? this.inAppReadAt,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
    );
  }

  /// الحصول على أيقونة الإشعار
  String get icon {
    switch (type.name) {
      case 'booking_confirmation':
        return '✅';
      case 'booking_reminder':
        return '⏰';
      case 'special_offer':
        return '🎉';
      case 'booking_cancellation':
        return '❌';
      case 'payment_confirmation':
        return '💳';
      case 'payment_failed':
        return '⚠️';
      case 'hotel_review_request':
        return '⭐';
      default:
        return '📢';
    }
  }

  /// تنسيق الوقت منذ الإنشاء
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'notifications.timeAgo.now'.tr;
    } else if (difference.inMinutes < 60) {
      return 'notifications.timeAgo.minutes'
          .trParams({'minutes': difference.inMinutes.toString()});
    } else if (difference.inHours < 24) {
      return 'notifications.timeAgo.hours'
          .trParams({'hours': difference.inHours.toString()});
    } else {
      return 'notifications.timeAgo.days'
          .trParams({'days': difference.inDays.toString()});
    }
  }
}

/// نوع الإشعار
class NotificationTypePlus {
  final String name;
  final String category;
  final String displayName;

  const NotificationTypePlus({
    required this.name,
    required this.category,
    required this.displayName,
  });

  factory NotificationTypePlus.fromJson(Map<String, dynamic> json) {
    return NotificationTypePlus(
      name: json['name'] ?? '',
      category: json['category'] ?? '',
      displayName: json['displayName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'category': category,
      'displayName': displayName,
    };
  }
}

/// أولوية الإشعار
enum NotificationPriority {
  low('LOW'),
  medium('MEDIUM'),
  high('HIGH'),
  urgent('URGENT');

  const NotificationPriority(this.value);
  final String value;

  String get name => value;

  static NotificationPriority fromString(String value) {
    return NotificationPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => NotificationPriority.medium,
    );
  }
}

/// حالة الإشعار
enum NotificationStatus {
  pending('PENDING'),
  delivered('DELIVERED'),
  failed('FAILED'),
  read('READ');

  const NotificationStatus(this.value);
  final String value;

  String get name => value;

  static NotificationStatus fromString(String value) {
    return NotificationStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => NotificationStatus.delivered,
    );
  }
}

/// استجابة قائمة الإشعارات
class NotificationsResponse {
  final bool success;
  final List<NotificationPlus> notifications;
  final NotificationPagination pagination;
  final String message;

  const NotificationsResponse({
    required this.success,
    required this.notifications,
    required this.pagination,
    required this.message,
  });

  factory NotificationsResponse.fromJson(Map<String, dynamic> json) {
    return NotificationsResponse(
      success: json['success'] ?? false,
      notifications: (json['data'] as List? ?? [])
          .map((notif) => NotificationPlus.fromJson(notif))
          .toList(),
      pagination: NotificationPagination.fromJson(json['pagination'] ?? {}),
      message: json['message'] ?? '',
    );
  }
}

/// معلومات التصفح
class NotificationPagination {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final bool hasNext;
  final bool hasPrevious;

  const NotificationPagination({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory NotificationPagination.fromJson(Map<String, dynamic> json) {
    return NotificationPagination(
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      totalItems: json['totalItems'] ?? 0,
      hasNext: json['hasNext'] ?? false,
      hasPrevious: json['hasPrevious'] ?? false,
    );
  }
}

/// إعدادات الإشعارات
class NotificationPreferencePlus {
  final String typeId;
  final String typeName;
  final String category;
  final bool pushEnabled;
  final bool emailEnabled;
  final bool inAppEnabled;
  final String quietHoursStart;
  final String quietHoursEnd;
  final String timezone;

  const NotificationPreferencePlus({
    required this.typeId,
    required this.typeName,
    required this.category,
    required this.pushEnabled,
    required this.emailEnabled,
    required this.inAppEnabled,
    required this.quietHoursStart,
    required this.quietHoursEnd,
    required this.timezone,
  });

  factory NotificationPreferencePlus.fromJson(Map<String, dynamic> json) {
    return NotificationPreferencePlus(
      typeId: json['typeId'] ?? '',
      typeName: json['typeName'] ?? '',
      category: json['category'] ?? '',
      pushEnabled: json['pushEnabled'] ?? true,
      emailEnabled: json['emailEnabled'] ?? true,
      inAppEnabled: json['inAppEnabled'] ?? true,
      quietHoursStart: json['quietHoursStart'] ?? '22:00',
      quietHoursEnd: json['quietHoursEnd'] ?? '08:00',
      timezone: json['timezone'] ?? 'Asia/Riyadh',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'typeId': typeId,
      'typeName': typeName,
      'category': category,
      'pushEnabled': pushEnabled,
      'emailEnabled': emailEnabled,
      'inAppEnabled': inAppEnabled,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
      'timezone': timezone,
    };
  }

  NotificationPreferencePlus copyWith({
    String? typeId,
    String? typeName,
    String? category,
    bool? pushEnabled,
    bool? emailEnabled,
    bool? inAppEnabled,
    String? quietHoursStart,
    String? quietHoursEnd,
    String? timezone,
  }) {
    return NotificationPreferencePlus(
      typeId: typeId ?? this.typeId,
      typeName: typeName ?? this.typeName,
      category: category ?? this.category,
      pushEnabled: pushEnabled ?? this.pushEnabled,
      emailEnabled: emailEnabled ?? this.emailEnabled,
      inAppEnabled: inAppEnabled ?? this.inAppEnabled,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      timezone: timezone ?? this.timezone,
    );
  }
}
