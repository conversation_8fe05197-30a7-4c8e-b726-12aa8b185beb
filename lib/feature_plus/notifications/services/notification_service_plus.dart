import 'package:get/get.dart';
import '../models/notification_models.dart';
import '../../../core/network_provider/networking.dart';

/// خدمة الإشعارات
class NotificationServicePlus extends GetxService {
  static NotificationServicePlus get instance =>
      Get.find<NotificationServicePlus>();

  final NetworkHandler _networkHandler = NetworkHandler();

  // Cache للإشعارات
  final RxList<NotificationPlus> _notifications = <NotificationPlus>[].obs;
  final RxList<NotificationPreferencePlus> _preferences =
      <NotificationPreferencePlus>[].obs;
  final RxInt _unreadCount = 0.obs;

  // حالات التحميل
  final RxBool _isLoadingNotifications = false.obs;
  final RxBool _isLoadingPreferences = false.obs;

  // Getters
  List<NotificationPlus> get notifications => _notifications;
  List<NotificationPreferencePlus> get preferences => _preferences;
  int get unreadCount => _unreadCount.value;
  bool get isLoadingNotifications => _isLoadingNotifications.value;
  bool get isLoadingPreferences => _isLoadingPreferences.value;

  @override
  void onInit() {
    super.onInit();
    print('✅ [NOTIFICATION_SERVICE] NotificationServicePlus initialized');

    // تحميل البيانات الأولية
    _loadInitialData();
  }

  /// تحميل البيانات الأولية
  Future<void> _loadInitialData() async {
    await Future.wait([
      getUserNotifications(),
      getUnreadCount(),
      getUserPreferences(),
    ]);
  }

  /// الحصول على إشعارات المستخدم
  Future<List<NotificationPlus>> getUserNotifications({
    int page = 1,
    int limit = 20,
    bool unreadOnly = false,
    String? typeId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _isLoadingNotifications.value = true;
      print('📢 [NOTIFICATION_SERVICE] Loading user notifications...');

      // لا نحتاج للتحقق من المستخدم لأن الـ API يستخدم التوكن

      // بناء query parameters
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        'unreadOnly': unreadOnly,
      };

      if (typeId != null) queryParams['typeId'] = typeId;
      if (startDate != null)
        queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      final queryString = queryParams.isNotEmpty
          ? '?${Uri(queryParameters: queryParams.map((k, v) => MapEntry(k, v.toString()))).query}'
          : '';

      final response = await _networkHandler.get(
        ObjectResponse(),
        'notifications$queryString',
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true) {
          final data = responseData['data'];
          final notificationsList = data['notifications'] as List? ?? [];

          final notifications = notificationsList.map((notifJson) {
            return NotificationPlus.fromApiJson(notifJson);
          }).toList();

          if (page == 1) {
            // إذا كانت الصفحة الأولى، استبدل القائمة
            _notifications.value = notifications;
          } else {
            // إذا كانت صفحة إضافية، أضف للقائمة
            _notifications.addAll(notifications);
          }

          print(
              '✅ [NOTIFICATION_SERVICE] Loaded ${notifications.length} notifications');
          return notifications;
        }
      }

      print('❌ [NOTIFICATION_SERVICE] Failed to load notifications');
      return [];
    } catch (e) {
      print('❌ [NOTIFICATION_SERVICE] Error loading notifications: $e');
      return [];
    } finally {
      _isLoadingNotifications.value = false;
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadCount() async {
    try {
      print('🔢 [NOTIFICATION_SERVICE] Getting unread count...');

      final response = await _networkHandler.get(
        ObjectResponse(),
        'notifications/unread-count',
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true) {
          final count = responseData['count'] ?? 0;
          _unreadCount.value = count;
          print('✅ [NOTIFICATION_SERVICE] Unread count: $count');
          return count;
        }
      }

      print('❌ [NOTIFICATION_SERVICE] Failed to get unread count');
      return 0;
    } catch (e) {
      print('❌ [NOTIFICATION_SERVICE] Error getting unread count: $e');
      return 0;
    }
  }

  /// تعليم إشعار كمقروء
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      print(
          '📖 [NOTIFICATION_SERVICE] Marking notification as read: $notificationId');

      final response = await _networkHandler.put(
        ObjectResponse(),
        'notifications/$notificationId/mark-read',
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true) {
          // تحديث الإشعار في القائمة المحلية
          final index =
              _notifications.indexWhere((notif) => notif.id == notificationId);
          if (index != -1) {
            _notifications[index] = _notifications[index].copyWith(
              inAppRead: true,
              inAppReadAt: DateTime.now(),
            );
          }

          // تقليل عدد الإشعارات غير المقروءة
          if (_unreadCount.value > 0) {
            _unreadCount.value--;
          }

          print('✅ [NOTIFICATION_SERVICE] Notification marked as read');
          return true;
        }
      }

      print('❌ [NOTIFICATION_SERVICE] Failed to mark notification as read');
      return false;
    } catch (e) {
      print('❌ [NOTIFICATION_SERVICE] Error marking notification as read: $e');
      return false;
    }
  }

  /// تعليم جميع الإشعارات كمقروءة
  Future<bool> markAllNotificationsAsRead({String? typeId}) async {
    try {
      print('📖 [NOTIFICATION_SERVICE] Marking all notifications as read...');

      final body = typeId != null ? {'typeId': typeId} : <String, dynamic>{};

      final response = await _networkHandler.put(
        ObjectResponse(),
        'notifications/mark-all-read',
        body: body,
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true) {
          // تحديث جميع الإشعارات في القائمة المحلية
          _notifications.value = _notifications.map((notif) {
            if (typeId == null || notif.typeId == typeId) {
              return notif.copyWith(
                inAppRead: true,
                inAppReadAt: DateTime.now(),
              );
            }
            return notif;
          }).toList();

          // إعادة تعيين عدد الإشعارات غير المقروءة
          _unreadCount.value = 0;

          print('✅ [NOTIFICATION_SERVICE] All notifications marked as read');
          return true;
        }
      }

      print(
          '❌ [NOTIFICATION_SERVICE] Failed to mark all notifications as read');
      return false;
    } catch (e) {
      print(
          '❌ [NOTIFICATION_SERVICE] Error marking all notifications as read: $e');
      return false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(String notificationId) async {
    try {
      print(
          '🗑️ [NOTIFICATION_SERVICE] Deleting notification: $notificationId');

      final response = await _networkHandler.delete(
        ObjectResponse(),
        'notifications/$notificationId',
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true) {
          // إزالة الإشعار من القائمة المحلية
          final removedNotification = _notifications
              .firstWhereOrNull((notif) => notif.id == notificationId);
          _notifications.removeWhere((notif) => notif.id == notificationId);

          // تقليل عدد الإشعارات غير المقروءة إذا كان الإشعار غير مقروء
          if (removedNotification != null &&
              !removedNotification.inAppRead &&
              _unreadCount.value > 0) {
            _unreadCount.value--;
          }

          print('✅ [NOTIFICATION_SERVICE] Notification deleted');
          return true;
        }
      }

      print('❌ [NOTIFICATION_SERVICE] Failed to delete notification');
      return false;
    } catch (e) {
      print('❌ [NOTIFICATION_SERVICE] Error deleting notification: $e');
      return false;
    }
  }

  /// الحصول على إعدادات الإشعارات
  Future<List<NotificationPreferencePlus>> getUserPreferences() async {
    try {
      _isLoadingPreferences.value = true;
      print('⚙️ [NOTIFICATION_SERVICE] Loading user preferences...');

      final response = await _networkHandler.get(
        ObjectResponse(),
        'notifications/preferences',
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true) {
          final preferencesData = responseData['data'] as List? ?? [];
          final preferences = preferencesData
              .map((pref) => NotificationPreferencePlus.fromJson(pref))
              .toList();

          _preferences.value = preferences;
          print(
              '✅ [NOTIFICATION_SERVICE] Loaded ${preferences.length} preferences');
          return preferences;
        }
      }

      print('❌ [NOTIFICATION_SERVICE] Failed to load preferences');
      return [];
    } catch (e) {
      print('❌ [NOTIFICATION_SERVICE] Error loading preferences: $e');
      return [];
    } finally {
      _isLoadingPreferences.value = false;
    }
  }

  /// تحديث إعدادات إشعار معين
  Future<bool> updateNotificationPreference(
    String typeId,
    NotificationPreferencePlus preference,
  ) async {
    try {
      print('⚙️ [NOTIFICATION_SERVICE] Updating preference for: $typeId');

      final response = await _networkHandler.put(
        ObjectResponse(),
        'notifications/preferences/$typeId',
        body: preference.toJson(),
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true) {
          // تحديث الإعداد في القائمة المحلية
          final index =
              _preferences.indexWhere((pref) => pref.typeId == typeId);
          if (index != -1) {
            _preferences[index] = preference;
          }

          print('✅ [NOTIFICATION_SERVICE] Preference updated');
          return true;
        }
      }

      print('❌ [NOTIFICATION_SERVICE] Failed to update preference');
      return false;
    } catch (e) {
      print('❌ [NOTIFICATION_SERVICE] Error updating preference: $e');
      return false;
    }
  }

  /// تسجيل device token للإشعارات الفورية
  Future<bool> registerDeviceToken(String fcmToken, String platform) async {
    try {
      print('📱 [NOTIFICATION_SERVICE] Registering device token...');

      final response = await _networkHandler.post(
        ObjectResponse(),
        'notifications/preferences/device-tokens',
        body: {
          'token': fcmToken,
          'platform': platform, // 'ANDROID', 'IOS', 'WEB'
          'deviceInfo': {
            'model': 'Flutter App',
            'osVersion': '1.0',
            'appVersion': '1.0.0',
          },
        },
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;

        if (responseData['success'] == true) {
          print('✅ [NOTIFICATION_SERVICE] Device token registered');
          return true;
        }
      }

      print('❌ [NOTIFICATION_SERVICE] Failed to register device token');
      return false;
    } catch (e) {
      print('❌ [NOTIFICATION_SERVICE] Error registering device token: $e');
      return false;
    }
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    await _loadInitialData();
  }

  /// إضافة إشعار جديد (للاستخدام مع WebSocket أو Push Notifications)
  void addNotification(NotificationPlus notification) {
    _notifications.insert(0, notification);
    if (!notification.inAppRead) {
      _unreadCount.value++;
    }
    print(
        '📢 [NOTIFICATION_SERVICE] New notification added: ${notification.title}');
  }

  /// تجميع الإشعارات حسب التاريخ
  Map<String, List<NotificationPlus>> get groupedNotifications {
    final grouped = <String, List<NotificationPlus>>{};

    for (final notification in _notifications) {
      final dateKey = _formatDateKey(notification.createdAt);
      if (!grouped.containsKey(dateKey)) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(notification);
    }

    return grouped;
  }

  /// تنسيق مفتاح التاريخ
  String _formatDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final notificationDate = DateTime(date.year, date.month, date.day);

    if (notificationDate == today) {
      return 'notifications.today'.tr;
    } else if (notificationDate == yesterday) {
      return 'notifications.yesterday'.tr;
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
