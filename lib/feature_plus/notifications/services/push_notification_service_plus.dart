import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/notification_models.dart';
import '../controllers/notification_controller_plus.dart';
import 'notification_service_plus.dart';

/// خدمة الإشعارات الفورية
class PushNotificationServicePlus extends GetxService {
  static PushNotificationServicePlus get instance =>
      Get.find<PushNotificationServicePlus>();

  // Controllers
  late final NotificationControllerPlus _notificationController;
  late final NotificationServicePlus _notificationService;

  // FCM Token
  String? _fcmToken;

  @override
  void onInit() {
    super.onInit();
    print(
        '✅ [PUSH_NOTIFICATION_SERVICE] PushNotificationServicePlus initialized');
    _initializeDependencies();
    _initializePushNotifications();
  }

  /// تهيئة التبعيات
  void _initializeDependencies() {
    try {
      _notificationController = Get.find<NotificationControllerPlus>();
      _notificationService = Get.find<NotificationServicePlus>();
      print('✅ [PUSH_NOTIFICATION_SERVICE] Dependencies found');
    } catch (e) {
      print('❌ [PUSH_NOTIFICATION_SERVICE] Error finding dependencies: $e');
    }
  }

  /// تهيئة الإشعارات الفورية
  Future<void> _initializePushNotifications() async {
    try {
      print(
          '🔔 [PUSH_NOTIFICATION_SERVICE] Initializing push notifications...');

      // طلب الإذن للإشعارات
      await _requestNotificationPermission();

      // الحصول على FCM Token
      await _getFCMToken();

      // إعداد معالجات الإشعارات
      _setupNotificationHandlers();

      print('✅ [PUSH_NOTIFICATION_SERVICE] Push notifications initialized');
    } catch (e) {
      print(
          '❌ [PUSH_NOTIFICATION_SERVICE] Error initializing push notifications: $e');
    }
  }

  /// طلب الإذن للإشعارات
  Future<void> _requestNotificationPermission() async {
    try {
      print(
          '🔐 [PUSH_NOTIFICATION_SERVICE] Requesting notification permission...');

      // TODO: Implement Firebase Messaging permission request
      // final messaging = FirebaseMessaging.instance;
      // final settings = await messaging.requestPermission(
      //   alert: true,
      //   announcement: false,
      //   badge: true,
      //   carPlay: false,
      //   criticalAlert: false,
      //   provisional: false,
      //   sound: true,
      // );

      // if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      //   print('✅ [PUSH_NOTIFICATION_SERVICE] Notification permission granted');
      // } else {
      //   print('❌ [PUSH_NOTIFICATION_SERVICE] Notification permission denied');
      // }

      // Mock permission granted for now
      print(
          '✅ [PUSH_NOTIFICATION_SERVICE] Notification permission granted (mock)');
    } catch (e) {
      print('❌ [PUSH_NOTIFICATION_SERVICE] Error requesting permission: $e');
    }
  }

  /// الحصول على FCM Token
  Future<void> _getFCMToken() async {
    try {
      print('🔑 [PUSH_NOTIFICATION_SERVICE] Getting FCM token...');

      // TODO: Implement Firebase Messaging token retrieval
      // final messaging = FirebaseMessaging.instance;
      // _fcmToken = await messaging.getToken();

      // Mock FCM token for now
      _fcmToken = 'mock_fcm_token_${DateTime.now().millisecondsSinceEpoch}';

      if (_fcmToken != null) {
        print(
            '✅ [PUSH_NOTIFICATION_SERVICE] FCM token obtained: ${_fcmToken!.substring(0, 20)}...');

        // تسجيل التوكن مع الخادم
        await _registerTokenWithServer();
      } else {
        print('❌ [PUSH_NOTIFICATION_SERVICE] Failed to get FCM token');
      }
    } catch (e) {
      print('❌ [PUSH_NOTIFICATION_SERVICE] Error getting FCM token: $e');
    }
  }

  /// تسجيل التوكن مع الخادم
  Future<void> _registerTokenWithServer() async {
    if (_fcmToken == null) return;

    try {
      print('📡 [PUSH_NOTIFICATION_SERVICE] Registering token with server...');

      String platform = 'WEB';
      if (defaultTargetPlatform == TargetPlatform.android) {
        platform = 'ANDROID';
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        platform = 'IOS';
      }

      await _notificationController.registerDeviceToken(_fcmToken!, platform);
      print('✅ [PUSH_NOTIFICATION_SERVICE] Token registered with server');
    } catch (e) {
      print('❌ [PUSH_NOTIFICATION_SERVICE] Error registering token: $e');
    }
  }

  /// إعداد معالجات الإشعارات
  void _setupNotificationHandlers() {
    try {
      print(
          '🔧 [PUSH_NOTIFICATION_SERVICE] Setting up notification handlers...');

      // TODO: Implement Firebase Messaging handlers
      // final messaging = FirebaseMessaging.instance;

      // معالج الإشعارات عندما يكون التطبيق في المقدمة
      // FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // معالج الإشعارات عندما يكون التطبيق في الخلفية
      // FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

      // معالج الإشعارات عندما يكون التطبيق مغلق
      // FirebaseMessaging.onBackgroundMessage(_handleTerminatedMessage);

      // معالج تحديث التوكن
      // messaging.onTokenRefresh.listen(_handleTokenRefresh);

      print('✅ [PUSH_NOTIFICATION_SERVICE] Notification handlers set up');
    } catch (e) {
      print('❌ [PUSH_NOTIFICATION_SERVICE] Error setting up handlers: $e');
    }
  }

  /// معالجة الإشعارات في المقدمة
  void _handleForegroundMessage(Map<String, dynamic> message) {
    try {
      print(
          '📱 [PUSH_NOTIFICATION_SERVICE] Foreground message received: ${message['notification']?['title']}');

      final notification = _parseNotificationFromMessage(message);
      if (notification != null) {
        // إضافة الإشعار للقائمة
        _notificationController.addNewNotification(notification);

        // عرض إشعار محلي
        _showLocalNotification(notification);
      }
    } catch (e) {
      print(
          '❌ [PUSH_NOTIFICATION_SERVICE] Error handling foreground message: $e');
    }
  }

  /// معالجة الإشعارات في الخلفية
  void _handleBackgroundMessage(Map<String, dynamic> message) {
    try {
      print(
          '📱 [PUSH_NOTIFICATION_SERVICE] Background message opened: ${message['notification']?['title']}');

      final notification = _parseNotificationFromMessage(message);
      if (notification != null) {
        // التنقل للإشعار
        _notificationController.onNotificationTap(notification);
      }
    } catch (e) {
      print(
          '❌ [PUSH_NOTIFICATION_SERVICE] Error handling background message: $e');
    }
  }

  /// معالجة تحديث التوكن
  void _handleTokenRefresh(String newToken) {
    try {
      print(
          '🔄 [PUSH_NOTIFICATION_SERVICE] Token refreshed: ${newToken.substring(0, 20)}...');
      _fcmToken = newToken;
      _registerTokenWithServer();
    } catch (e) {
      print('❌ [PUSH_NOTIFICATION_SERVICE] Error handling token refresh: $e');
    }
  }

  /// تحليل الإشعار من الرسالة
  NotificationPlus? _parseNotificationFromMessage(
      Map<String, dynamic> message) {
    try {
      final data = message['data'] ?? {};
      final notification = message['notification'] ?? {};

      return NotificationPlus(
        id: data['notificationId'] ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        typeId: data['type'] ?? 'general',
        title: notification['title'] ?? '',
        message: notification['body'] ?? '',
        data: Map<String, dynamic>.from(data),
        priority: NotificationPriority.fromString(data['priority'] ?? 'MEDIUM'),
        status: NotificationStatus.delivered,
        inAppRead: false,
        createdAt: DateTime.now(),
        type: NotificationTypePlus(
          name: data['type'] ?? 'general',
          category: data['category'] ?? 'SYSTEM',
          displayName: data['typeDisplayName'] ?? 'General',
        ),
      );
    } catch (e) {
      print('❌ [PUSH_NOTIFICATION_SERVICE] Error parsing notification: $e');
      return null;
    }
  }

  /// عرض إشعار محلي
  void _showLocalNotification(NotificationPlus notification) {
    try {
      print(
          '🔔 [PUSH_NOTIFICATION_SERVICE] Showing local notification: ${notification.title}');

      // TODO: Implement local notification display
      // يمكن استخدام flutter_local_notifications package

      // عرض SnackBar كبديل مؤقت
      Get.snackbar(
        notification.title,
        notification.message,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 4),
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
        icon: Text(notification.icon, style: const TextStyle(fontSize: 20)),
        onTap: (_) => _notificationController.onNotificationTap(notification),
      );
    } catch (e) {
      print(
          '❌ [PUSH_NOTIFICATION_SERVICE] Error showing local notification: $e');
    }
  }

  /// إرسال إشعار تجريبي
  void sendTestNotification() {
    final testNotification = NotificationPlus(
      id: 'test_${DateTime.now().millisecondsSinceEpoch}',
      typeId: 'booking_confirmation',
      title: 'تأكيد الحجز',
      message: 'تم تأكيد حجزك في فندق برج العرب بنجاح',
      data: {
        'bookingId': 'booking_123',
        'hotelName': 'فندق برج العرب',
        'checkIn': '2025-01-15',
        'checkOut': '2025-01-17',
      },
      priority: NotificationPriority.high,
      status: NotificationStatus.delivered,
      inAppRead: false,
      createdAt: DateTime.now(),
      type: const NotificationTypePlus(
        name: 'booking_confirmation',
        category: 'BOOKING',
        displayName: 'تأكيد الحجز',
      ),
    );

    _notificationController.addNewNotification(testNotification);
    _showLocalNotification(testNotification);
  }

  /// الحصول على FCM Token الحالي
  String? get fcmToken => _fcmToken;

  /// التحقق من حالة الإذن
  Future<bool> isPermissionGranted() async {
    // TODO: Implement permission check
    // final messaging = FirebaseMessaging.instance;
    // final settings = await messaging.getNotificationSettings();
    // return settings.authorizationStatus == AuthorizationStatus.authorized;

    return true; // Mock permission granted
  }
}
