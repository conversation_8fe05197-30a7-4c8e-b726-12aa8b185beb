import 'package:get/get.dart';
import '../controllers/notification_controller_plus.dart';
import '../services/notification_service_plus.dart';

/// Binding للإشعارات
class NotificationBindingPlus extends Bindings {
  @override
  void dependencies() {
    // تسجيل الخدمة
    Get.lazyPut<NotificationServicePlus>(
      () => NotificationServicePlus(),
      fenix: true,
    );
    
    // تسجيل الكونترولر
    Get.lazyPut<NotificationControllerPlus>(
      () => NotificationControllerPlus(),
      fenix: true,
    );
    
    print('✅ [NOTIFICATION_BINDING] NotificationBindingPlus dependencies registered');
  }
}
