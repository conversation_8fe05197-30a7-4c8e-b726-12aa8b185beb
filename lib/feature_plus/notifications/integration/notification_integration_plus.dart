import 'package:get/get.dart';
import '../services/notification_service_plus.dart';
import '../services/push_notification_service_plus.dart';
import '../controllers/notification_controller_plus.dart';

/// تكامل نظام الإشعارات
class NotificationIntegrationPlus {
  static bool _isInitialized = false;

  /// تهيئة نظام الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) {
      print('⚠️ [NOTIFICATION_INTEGRATION] Already initialized');
      return;
    }

    try {
      print(
          '🚀 [NOTIFICATION_INTEGRATION] Initializing notification system...');

      // تسجيل الخدمات
      _registerServices();

      // تهيئة الخدمات
      await _initializeServices();

      _isInitialized = true;
      print(
          '✅ [NOTIFICATION_INTEGRATION] Notification system initialized successfully');
    } catch (e) {
      print(
          '❌ [NOTIFICATION_INTEGRATION] Error initializing notification system: $e');
    }
  }

  /// تسجيل الخدمات
  static void _registerServices() {
    try {
      print('📝 [NOTIFICATION_INTEGRATION] Registering services...');

      // تسجيل NotificationServicePlus
      if (!Get.isRegistered<NotificationServicePlus>()) {
        Get.put<NotificationServicePlus>(
          NotificationServicePlus(),
          permanent: true,
        );
        print(
            '✅ [NOTIFICATION_INTEGRATION] NotificationServicePlus registered');
      }

      // تسجيل PushNotificationServicePlus
      if (!Get.isRegistered<PushNotificationServicePlus>()) {
        Get.put<PushNotificationServicePlus>(
          PushNotificationServicePlus(),
          permanent: true,
        );
        print(
            '✅ [NOTIFICATION_INTEGRATION] PushNotificationServicePlus registered');
      }

      // تسجيل NotificationControllerPlus
      if (!Get.isRegistered<NotificationControllerPlus>()) {
        Get.put<NotificationControllerPlus>(
          NotificationControllerPlus(),
          permanent: true,
        );
        print(
            '✅ [NOTIFICATION_INTEGRATION] NotificationControllerPlus registered');
      }

      print('✅ [NOTIFICATION_INTEGRATION] All services registered');
    } catch (e) {
      print('❌ [NOTIFICATION_INTEGRATION] Error registering services: $e');
    }
  }

  /// تهيئة الخدمات
  static Future<void> _initializeServices() async {
    try {
      print('⚙️ [NOTIFICATION_INTEGRATION] Initializing services...');

      // تهيئة NotificationServicePlus
      final notificationService = Get.find<NotificationServicePlus>();
      notificationService.onInit();

      // تهيئة PushNotificationServicePlus
      final pushNotificationService = Get.find<PushNotificationServicePlus>();
      pushNotificationService.onInit();

      // تهيئة NotificationControllerPlus
      final notificationController = Get.find<NotificationControllerPlus>();
      notificationController.onInit();

      print('✅ [NOTIFICATION_INTEGRATION] All services initialized');
    } catch (e) {
      print('❌ [NOTIFICATION_INTEGRATION] Error initializing services: $e');
    }
  }

  /// إرسال إشعار تجريبي
  static void sendTestNotification() {
    try {
      print('🧪 [NOTIFICATION_INTEGRATION] Sending test notification...');

      final pushNotificationService = Get.find<PushNotificationServicePlus>();
      pushNotificationService.sendTestNotification();

      print('✅ [NOTIFICATION_INTEGRATION] Test notification sent');
    } catch (e) {
      print('❌ [NOTIFICATION_INTEGRATION] Error sending test notification: $e');
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  static int getUnreadCount() {
    try {
      final notificationController = Get.find<NotificationControllerPlus>();
      return notificationController.unreadCount;
    } catch (e) {
      print('❌ [NOTIFICATION_INTEGRATION] Error getting unread count: $e');
      return 0;
    }
  }

  /// تحديث البيانات
  static Future<void> refresh() async {
    try {
      print('🔄 [NOTIFICATION_INTEGRATION] Refreshing notification data...');

      final notificationController = Get.find<NotificationControllerPlus>();
      await notificationController.refresh();

      print('✅ [NOTIFICATION_INTEGRATION] Notification data refreshed');
    } catch (e) {
      print('❌ [NOTIFICATION_INTEGRATION] Error refreshing data: $e');
    }
  }

  /// تنظيف الموارد
  static void dispose() {
    try {
      print('🧹 [NOTIFICATION_INTEGRATION] Disposing notification system...');

      // لا نحذف الخدمات المسجلة كـ permanent
      // فقط نطبع رسالة للتأكيد

      _isInitialized = false;
      print('✅ [NOTIFICATION_INTEGRATION] Notification system disposed');
    } catch (e) {
      print(
          '❌ [NOTIFICATION_INTEGRATION] Error disposing notification system: $e');
    }
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized;

  /// الحصول على خدمة الإشعارات
  static NotificationServicePlus get notificationService {
    return Get.find<NotificationServicePlus>();
  }

  /// الحصول على خدمة الإشعارات الفورية
  static PushNotificationServicePlus get pushNotificationService {
    return Get.find<PushNotificationServicePlus>();
  }

  /// الحصول على كونترولر الإشعارات
  static NotificationControllerPlus get notificationController {
    return Get.find<NotificationControllerPlus>();
  }
}
