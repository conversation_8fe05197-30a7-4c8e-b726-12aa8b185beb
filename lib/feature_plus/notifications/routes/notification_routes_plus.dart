import 'package:get/get.dart';
import '../views/notifications_screen_plus.dart';
import '../views/notification_preferences_screen_plus.dart';
import '../bindings/notification_binding_plus.dart';

/// مسارات الإشعارات
class NotificationRoutesPlus {
  static const String notifications = '/notifications';
  static const String notificationPreferences = '/notification-preferences';

  static List<GetPage> routes = [
    // صفحة الإشعارات
    GetPage(
      name: notifications,
      page: () => const NotificationsScreenPlus(),
      binding: NotificationBindingPlus(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // صفحة إعدادات الإشعارات
    GetPage(
      name: notificationPreferences,
      page: () => const NotificationPreferencesScreenPlus(),
      binding: NotificationBindingPlus(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
  ];
}

/// Navigator للإشعارات
class NotificationNavigator {
  /// الانتقال لصفحة الإشعارات
  static Future<void> navigateToNotifications() async {
    await Get.toNamed(NotificationRoutesPlus.notifications);
  }

  /// الانتقال لصفحة إعدادات الإشعارات
  static Future<void> navigateToNotificationPreferences() async {
    await Get.toNamed(NotificationRoutesPlus.notificationPreferences);
  }

  /// الانتقال لإشعار معين
  static Future<void> navigateToNotification(String notificationId) async {
    await Get.toNamed(
      NotificationRoutesPlus.notifications,
      arguments: {'notificationId': notificationId},
    );
  }
}
