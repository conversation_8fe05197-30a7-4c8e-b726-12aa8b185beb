import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'settings/controllers/settings_controller.dart';

/// Test screen to verify language selection bottom sheet
class TestLanguageSheet extends StatelessWidget {
  const TestLanguageSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Language Selection'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () async {
                try {
                  final settingsController = Get.find<SettingsController>();
                  await settingsController.showLanguageSelection();
                } catch (e) {
                  print('❌ Error: $e');
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error: $e')),
                  );
                }
              },
              child: const Text('Test Language Selection'),
            ),
            const SizedBox(height: 20),
            GetBuilder<SettingsController>(
              builder: (controller) {
                return Text(
                  'Current Language: ${controller.selectedLanguage.value.nativeName}',
                  style: const TextStyle(fontSize: 16),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
