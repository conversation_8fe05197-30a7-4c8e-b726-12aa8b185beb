import 'package:get/get.dart';
import '../models/booking_models_plus.dart';
import '../views/my_bookings_screen_plus.dart';
import '../views/booking_details_screen_plus.dart';
import '../bindings/booking_bindings_plus.dart';

/// Booking Route Constants
class BookingRoutes {
  static const String myBookings = '/my-bookings-plus';
  static const String bookingDetails = '/booking-details-plus';
}

/// Booking Navigation Helper Class
class BookingNavigation {
  /// Navigate to My Bookings Screen
  static Future<T?>? toMyBookings<T>({
    Map<String, dynamic>? arguments,
  }) {
    return Get.toNamed<T>(BookingRoutes.myBookings, arguments: arguments);
  }

  /// Navigate to Booking Details Screen
  static Future<T?>? toBookingDetails<T>(
    String bookingId,
    String bookingReferenceId, {
    Map<String, dynamic>? additionalArguments,
  }) {
    final arguments = {
      'bookingId': bookingId,
      'bookingReferenceId': bookingReferenceId,
      ...?additionalArguments,
    };
    return Get.toNamed<T>(BookingRoutes.bookingDetails, arguments: arguments);
  }

  /// Navigate to Booking Details Screen with parameters
  static Future<T?>? toBookingDetailsWithParams<T>({
    required String bookingReferenceId,
    Map<String, dynamic>? arguments,
  }) {
    final finalArguments = {
      'bookingReferenceId': bookingReferenceId,
      ...?arguments,
    };
    return Get.toNamed<T>(BookingRoutes.bookingDetails,
        arguments: finalArguments);
  }

  /// Navigate back
  static void back<T>([T? result]) {
    Get.back<T>(result: result);
  }

  /// Navigate back to My Bookings
  static Future<T?>? backToMyBookings<T>() {
    return Get.offAllNamed<T>(BookingRoutes.myBookings);
  }
}

/// Booking Routes List for GetPages
class BookingRoutesConfig {
  static final List<GetPage> routes = [
    // My Bookings Screen
    GetPage(
      name: BookingRoutes.myBookings,
      page: () => const MyBookingsScreenPlus(),
      binding: MyBookingsBindingPlus(),
      transition: Transition.cupertino,
    ),

    // Booking Details Screen
    GetPage(
      name: BookingRoutes.bookingDetails,
      page: () {
        final args = Get.arguments as Map<String, dynamic>?;
        final bookingReferenceId = args?['bookingReferenceId'] as String?;

        if (bookingReferenceId == null) {
          throw ArgumentError(
              'bookingReferenceId is required for BookingDetailsScreenPlus');
        }

        return const BookingDetailsScreenPlus();
      },
      binding: BookingDetailsBindingPlus(),
      transition: Transition.cupertino,
    ),
  ];
}
