import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:get/get.dart';

part 'cancel_booking_models.freezed.dart';
part 'cancel_booking_models.g.dart';

/// Request model for cancelling a booking
@freezed
class CancelBookingRequest with _$CancelBookingRequest {
  const factory CancelBookingRequest({
    required String bookingReferenceId,
    required String cancellationReason,
  }) = _CancelBookingRequest;

  factory CancelBookingRequest.fromJson(Map<String, dynamic> json) =>
      _$CancelBookingRequestFromJson(json);
}

/// Response model for booking cancellation
@freezed
class CancelBookingResponse with _$CancelBookingResponse {
  const factory CancelBookingResponse({
    required bool success,
    required String message,
    CancelBookingData? data,
    CancelBookingMeta? meta,
  }) = _CancelBookingResponse;

  factory CancelBookingResponse.fromJson(Map<String, dynamic> json) =>
      _$CancelBookingResponseFromJson(json);
}

/// Cancellation data from the response
@freezed
class CancelBookingData with _$CancelBookingData {
  const factory CancelBookingData({
    required String id,
    required String bookingCode,
    required String clientReferenceId,
    required String bookingReferenceId,
    required String confirmationNumber,
    required String status,
    required DateTime checkIn,
    required DateTime checkOut,
    required double totalFare,
    required String currency,
    required String guestNationality,
    required String emailId,
    required String phoneNumber,
    required String bookingType,
    required String paymentMode,
    required String paymentStatus,
    required String userId,
    required String hotelId,
    String? voucherPath,
    double? cancellationAmount,
    String? cancellationId,
    required double invoiceAmount,
    String? notes,
    List<String>? rateConditions,
    DateTime? confirmedAt,
    DateTime? cancelledAt,
    required DateTime createdAt,
    required DateTime updatedAt,
    CancellationDetails? cancellationDetails,
  }) = _CancelBookingData;

  factory CancelBookingData.fromJson(Map<String, dynamic> json) =>
      _$CancelBookingDataFromJson(json);
}

/// Cancellation details
@freezed
class CancellationDetails with _$CancellationDetails {
  const factory CancellationDetails({
    required DateTime cancellationDate,
    required String cancellationRefNo,
    required double refundAmount,
    required double cancellationCharges,
    required String reason,
  }) = _CancellationDetails;

  factory CancellationDetails.fromJson(Map<String, dynamic> json) =>
      _$CancellationDetailsFromJson(json);
}

/// Meta information from the response
@freezed
class CancelBookingMeta with _$CancelBookingMeta {
  const factory CancelBookingMeta({
    required String apiProvider,
    required String version,
    required DateTime timestamp,
  }) = _CancelBookingMeta;

  factory CancelBookingMeta.fromJson(Map<String, dynamic> json) =>
      _$CancelBookingMetaFromJson(json);
}

/// Cancellation reasons for the bottom sheet
enum CancellationReason {
  changeOfPlans('cancellation.changeOfPlans'),
  emergencyTravel('cancellation.emergencyTravel'),
  healthIssues('cancellation.healthIssues'),
  workCommitments('cancellation.workCommitments'),
  financialReasons('cancellation.financialReasons'),
  weatherConditions('cancellation.weatherConditions'),
  familyEmergency('cancellation.familyEmergency'),
  betterDeal('cancellation.betterDeal'),
  hotelIssues('cancellation.hotelIssues'),
  other('cancellation.other');

  final String key;
  const CancellationReason(this.key);

  String get localized => key.tr; // ترجمة المفتاح تلقائياً حسب لغة التطبيق
}

/// Error response model
@freezed
class CancelBookingError with _$CancelBookingError {
  const factory CancelBookingError({
    required String message,
    required String error,
    required int statusCode,
  }) = _CancelBookingError;

  factory CancelBookingError.fromJson(Map<String, dynamic> json) =>
      _$CancelBookingErrorFromJson(json);
}
