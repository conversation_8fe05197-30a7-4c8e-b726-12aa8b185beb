import 'package:fandooq/feature_plus/hotels/models/hotel_models.dart';
import 'package:get/get.dart';

import '../../../core/models/booking/booking_models.dart';

/// Enhanced booking summary model with additional features
class BookingSummaryPlus {
  String? id;
  String? bookingReferenceId;
  String? status;
  String? hotelName;
  String? hotelImage;
  String? hotelAddress;
  String? checkIn;
  String? checkOut;
  String? currency;
  String? createdAt;
  int? numberOfRooms;
  int? numberOfGuests;
  String? paymentMethod;
  bool? canCancel;
  String? cancellationDeadline;
  List<String>? amenities;
  double? rating;
  String? confirmationNumber;

  // Additional fields from API
  int? nights;
  String? paymentMode;
  String? bookingDate;
  HotelDetailsPlus? hotel;
  List<RoomDetailsPlus>? rooms;
  String? contactEmail;
  String? contactPhone;
  bool? hasVoucher;
  List<String>? rateConditions;

  // Hotel details from nested hotel object
  List<String>? facilities;
  double? latitude;
  double? longitude;
  String? contactNumber;
  String? website;

  // Pricing breakdown
  PricingPlus? pricing;

  BookingSummaryPlus({
    this.id,
    this.bookingReferenceId,
    this.status,
    this.hotelName,
    this.hotelImage,
    this.hotelAddress,
    this.checkIn,
    this.checkOut,
    this.createdAt,
    this.numberOfRooms,
    this.numberOfGuests,
    this.paymentMethod,
    this.canCancel,
    this.cancellationDeadline,
    this.amenities,
    this.rating,
    this.confirmationNumber,
    this.nights,
    this.paymentMode,
    this.bookingDate,
    this.hotel,
    this.rooms,
    this.contactEmail,
    this.contactPhone,
    this.hasVoucher,
    this.rateConditions,
    this.facilities,
    this.latitude,
    this.longitude,
    this.contactNumber,
    this.website,
    this.pricing,
  });

  factory BookingSummaryPlus.fromJson(Map<String, dynamic> json) {
    // Extract hotel data
    final hotelData = json['hotel'] as Map<String, dynamic>?;

    print(
        '🔍 [BOOKING_SUMMARY] Parsing booking: ${json['bookingReferenceId']}');
    print('🏨 [BOOKING_SUMMARY] Hotel data: $hotelData');
    print('🛏️ [BOOKING_SUMMARY] Rooms data: ${json['rooms']}');

    return BookingSummaryPlus(
      id: json['id'],
      bookingReferenceId: json['bookingReferenceId'],
      status: json['status'],
      hotelName: hotelData?['name'] ?? json['hotelName'],
      hotelImage: json['hotelImage'],
      hotelAddress: hotelData?['address'] ?? json['hotelAddress'],
      checkIn: json['checkIn'],
      checkOut: json['checkOut'],
      createdAt: json['createdAt'] ?? json['bookingDate'],
      numberOfRooms: json['numberOfRooms'],
      numberOfGuests: json['numberOfGuests'],
      paymentMethod: json['paymentMethod'] ?? json['paymentMode'],
      canCancel: json['canCancel'],
      cancellationDeadline: json['cancellationDeadline'],
      amenities: hotelData?['amenities'] != null
          ? List<String>.from(hotelData!['amenities'])
          : (json['amenities'] != null
              ? List<String>.from(json['amenities'])
              : null),
      rating:
          hotelData?['starRating']?.toDouble() ?? json['rating']?.toDouble(),
      confirmationNumber: json['confirmationNumber'],
      nights: json['nights'],
      paymentMode: json['paymentMode'],
      bookingDate: json['bookingDate'],
      hotel: hotelData != null ? HotelDetailsPlus.fromJson(hotelData) : null,
      rooms: json['rooms'] != null
          ? (json['rooms'] as List)
              .map((room) => RoomDetailsPlus.fromJson(room))
              .toList()
          : null,
      contactEmail: json['contactEmail'],
      contactPhone: json['contactPhone'],
      hasVoucher: json['hasVoucher'],
      rateConditions: json['rateConditions'] != null
          ? List<String>.from(json['rateConditions'])
          : null,
      facilities: hotelData?['facilities'] != null
          ? List<String>.from(hotelData!['facilities'])
          : null,
      latitude: hotelData?['latitude']?.toDouble(),
      longitude: hotelData?['longitude']?.toDouble(),
      contactNumber: hotelData?['contactNumber'],
      website: hotelData?['website'],
      pricing: json['pricing'] != null
          ? PricingPlus.fromJson(json['pricing'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bookingReferenceId': bookingReferenceId,
      'status': status,
      'hotelName': hotelName,
      'hotelImage': hotelImage,
      'hotelAddress': hotelAddress,
      'checkIn': checkIn,
      'checkOut': checkOut,
      'createdAt': createdAt,
      'numberOfRooms': numberOfRooms,
      'numberOfGuests': numberOfGuests,
      'paymentMethod': paymentMethod,
      'canCancel': canCancel,
      'cancellationDeadline': cancellationDeadline,
      'amenities': amenities,
      'rating': rating,
      'confirmationNumber': confirmationNumber,
    };
  }

  // Helper methods
  String get formattedCheckIn {
    if (checkIn == null) return '';
    try {
      final date = DateTime.parse(checkIn!);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return checkIn!;
    }
  }

  String get formattedCheckOut {
    if (checkOut == null) return '';
    try {
      final date = DateTime.parse(checkOut!);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return checkOut!;
    }
  }

  String get formattedCreatedAt {
    if (createdAt == null) return '';
    try {
      final date = DateTime.parse(createdAt!);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return createdAt!;
    }
  }

  int get numberOfNights {
    if (checkIn == null || checkOut == null) return 0;
    try {
      final checkInDate = DateTime.parse(checkIn!);
      final checkOutDate = DateTime.parse(checkOut!);
      return checkOutDate.difference(checkInDate).inDays;
    } catch (e) {
      return 0;
    }
  }

  String get formattedTotalAmount {
    if (pricing?.converted.price == null) return '';
    return '${pricing!.converted.price.toStringAsFixed(2)} ${pricing!.converted.currency ?? 'USD'}';
  }

  bool get isActive {
    return status?.toUpperCase() == 'CONFIRMED' ||
        status?.toUpperCase() == 'PENDING';
  }

  bool get isCancellable {
    if (canCancel != true) return false;
    if (cancellationDeadline == null) return true;

    try {
      final deadline = DateTime.parse(cancellationDeadline!);
      return DateTime.now().isBefore(deadline);
    } catch (e) {
      return true;
    }
  }

  String get statusDisplayText {
    switch (status?.toUpperCase()) {
      case 'CONFIRMED':
        return 'bookingStatus.confirmed'.tr;
      case 'PENDING':
        return 'bookingStatus.pending'.tr;
      case 'CANCELLED':
        return 'bookingStatus.cancelled'.tr;
      case 'FAILED':
        return 'bookingStatus.failed'.tr;
      case 'CANCELLATION_IN_PROGRESS':
        return 'bookingStatus.cancellationInProgress'.tr;
      case 'REFUNDED':
        return 'bookingStatus.refunded'.tr;
      default:
        return status ?? 'bookingStatus.unknown'.tr;
    }
  }

  List<GuestDetailsPlus> get guests =>
      (rooms ?? []).map((e) => e.guests).toList().expand((e) => e!).toList();

  List<CancellationPolicyItemPlus> get cancellationPolicies => (rooms ?? [])
      .where((e) => e.cancellationPolicies != null)
      .map((e) => e.cancellationPolicies!)
      .toList()
      .expand((e) => e)
      .toList();

  /// Create a copy of this booking with updated fields
  BookingSummaryPlus copyWith({
    String? bookingReferenceId,
    String? status,
    String? hotelName,
    String? hotelImage,
    String? hotelAddress,
    String? checkIn,
    String? checkOut,
    String? createdAt,
    int? numberOfRooms,
    int? numberOfGuests,
    String? paymentMethod,
    bool? canCancel,
    String? cancellationDeadline,
    List<String>? amenities,
    double? rating,
    String? confirmationNumber,
    int? nights,
    String? paymentMode,
    String? bookingDate,
    HotelDetailsPlus? hotel,
    List<RoomDetailsPlus>? rooms,
    String? contactEmail,
    String? contactPhone,
    bool? hasVoucher,
    List<String>? rateConditions,
    List<String>? facilities,
    double? latitude,
    double? longitude,
    String? contactNumber,
    String? website,
  }) {
    return BookingSummaryPlus(
      bookingReferenceId: bookingReferenceId ?? this.bookingReferenceId,
      status: status ?? this.status,
      hotelName: hotelName ?? this.hotelName,
      hotelImage: hotelImage ?? this.hotelImage,
      hotelAddress: hotelAddress ?? this.hotelAddress,
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
      createdAt: createdAt ?? this.createdAt,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      numberOfGuests: numberOfGuests ?? this.numberOfGuests,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      canCancel: canCancel ?? this.canCancel,
      cancellationDeadline: cancellationDeadline ?? this.cancellationDeadline,
      amenities: amenities ?? this.amenities,
      rating: rating ?? this.rating,
      confirmationNumber: confirmationNumber ?? this.confirmationNumber,
      nights: nights ?? this.nights,
      paymentMode: paymentMode ?? this.paymentMode,
      bookingDate: bookingDate ?? this.bookingDate,
      hotel: hotel ?? this.hotel,
      rooms: rooms ?? this.rooms,
      contactEmail: contactEmail ?? this.contactEmail,
      contactPhone: contactPhone ?? this.contactPhone,
      hasVoucher: hasVoucher ?? this.hasVoucher,
      rateConditions: rateConditions ?? this.rateConditions,
      facilities: facilities ?? this.facilities,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      contactNumber: contactNumber ?? this.contactNumber,
      website: website ?? this.website,
    );
  }
}

/// Hotel details for booking
class HotelDetailsPlus {
  String? name;
  String? description;
  String? address;
  String? city;
  String? country;
  List<String>? images;
  double? rating;
  String? phone;
  String? email;
  List<String>? amenities;
  List<String>? facilities;
  String? website;
  double? latitude;
  double? longitude;
  String? contactNumber;
  String? tboCode;
  int? starRating;
  String? cityName;
  String? countryName;

  HotelDetailsPlus({
    this.name,
    this.description,
    this.address,
    this.city,
    this.country,
    this.images,
    this.rating,
    this.phone,
    this.email,
    this.amenities,
    this.facilities,
    this.website,
    this.latitude,
    this.longitude,
    this.contactNumber,
    this.tboCode,
    this.starRating,
    this.cityName,
    this.countryName,
  });

  factory HotelDetailsPlus.fromJson(Map<String, dynamic> json) {
    return HotelDetailsPlus(
      name: json['name'],
      description: json['description'],
      address: json['address'],
      city: json['city'] ?? json['cityName'],
      country: json['country'] ?? json['countryName'],
      images: json['images'] != null ? List<String>.from(json['images']) : null,
      rating: json['rating']?.toDouble() ?? json['starRating']?.toDouble(),
      phone: json['phone'] ?? json['contactNumber'],
      email: json['email'],
      amenities: json['amenities'] != null
          ? List<String>.from(json['amenities'])
          : null,
      facilities: json['facilities'] != null
          ? List<String>.from(json['facilities'])
          : null,
      website: json['website'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      contactNumber: json['contactNumber'],
      tboCode: json['tboCode'],
      starRating: json['starRating'],
      cityName: json['cityName'],
      countryName: json['countryName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'city': city,
      'country': country,
      'images': images,
      'rating': rating,
      'phone': phone,
      'email': email,
      'amenities': amenities,
      'facilities': facilities,
      'website': website,
      'latitude': latitude,
      'longitude': longitude,
      'contactNumber': contactNumber,
      'tboCode': tboCode,
      'starRating': starRating,
      'cityName': cityName,
      'countryName': countryName,
    };
  }
}

/// Room details for booking
class RoomDetailsPlus {
  String? roomType;
  String? bedType;
  int? adults;
  int? children;
  String? mealPlan;
  double? roomPrice;
  String? currency;

  // Additional fields from API
  String? roomName;
  double? totalFare;
  double? totalTax;
  String? status;
  String? mealType;
  String? inclusion;
  List<String>? amenities;
  List<CancellationPolicyItemPlus>? cancellationPolicies;
  List<List<SupplementPlus>>? supplements;
  List<GuestDetailsPlus>? guests;

  RoomDetailsPlus({
    this.roomType,
    this.bedType,
    this.adults,
    this.children,
    this.mealPlan,
    this.roomPrice,
    this.currency,
    this.roomName,
    this.totalFare,
    this.totalTax,
    this.status,
    this.mealType,
    this.inclusion,
    this.amenities,
    this.cancellationPolicies,
    this.supplements,
    this.guests,
  });

  factory RoomDetailsPlus.fromJson(Map<String, dynamic> json) {
    return RoomDetailsPlus(
      roomType: json['roomType'],
      bedType: json['bedType'],
      adults: json['adults'],
      children: json['children'],
      mealPlan: json['mealPlan'],
      roomPrice: json['roomPrice']?.toDouble(),
      currency: json['currency'],
      roomName: json['roomName'],
      totalFare: json['totalFare']?.toDouble(),
      totalTax: json['totalTax']?.toDouble(),
      status: json['status'],
      mealType: json['mealType'],
      inclusion: json['inclusion'],
      amenities: json['amenities'] != null
          ? List<String>.from(json['amenities'])
          : null,
      cancellationPolicies: json['cancellationPolicies'] != null
          ? (json['cancellationPolicies'] as List)
              .map((policy) => CancellationPolicyItemPlus.fromJson(policy))
              .toList()
          : null,
      supplements: (json['supplements'] as List? ?? [])
          .map((supplementGroup) => (supplementGroup as List? ?? [])
              .map((supplement) => SupplementPlus.fromJson(supplement))
              .toList())
          .toList(),
      guests: json['guests'] != null
          ? (json['guests'] as List)
              .map((guest) => GuestDetailsPlus.fromJson(guest))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'roomType': roomType,
      'bedType': bedType,
      'adults': adults,
      'children': children,
      'mealPlan': mealPlan,
      'roomPrice': roomPrice,
      'currency': currency,
      'roomName': roomName,
      'totalFare': totalFare,
      'totalTax': totalTax,
      'status': status,
      'mealType': mealType,
      'inclusion': inclusion,
      'amenities': amenities,
      'cancellationPolicies':
          cancellationPolicies?.map((policy) => policy.toJson()).toList(),
      'supplements': supplements,
      'guests': guests?.map((guest) => guest.toJson()).toList(),
    };
  }
}

/// Cancellation policy item
class CancellationPolicyItemPlus {
  String? fromDate;
  String? chargeType;
  double? cancellationCharge;

  CancellationPolicyItemPlus({
    this.fromDate,
    this.chargeType,
    this.cancellationCharge,
  });

  factory CancellationPolicyItemPlus.fromJson(Map<String, dynamic> json) {
    return CancellationPolicyItemPlus(
      fromDate: json['fromDate'],
      chargeType: json['chargeType'],
      cancellationCharge: json['cancellationCharge']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fromDate': fromDate,
      'chargeType': chargeType,
      'cancellationCharge': cancellationCharge,
    };
  }
}

/// Guest details for booking
class GuestDetailsPlus {
  String? title;
  String? firstName;
  String? lastName;
  String? type;
  String? nationality;

  GuestDetailsPlus({
    this.title,
    this.firstName,
    this.lastName,
    this.type,
    this.nationality,
  });

  factory GuestDetailsPlus.fromJson(Map<String, dynamic> json) {
    return GuestDetailsPlus(
      title: json['title'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      type: json['type'],
      nationality: json['nationality'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'firstName': firstName,
      'lastName': lastName,
      'type': type,
      'nationality': nationality,
    };
  }
}

/// Payment details for booking
class PaymentDetailsPlus {
  String? method;
  String? status;
  double? amount;
  String? currency;
  String? transactionId;
  String? cardLast4;

  PaymentDetailsPlus({
    this.method,
    this.status,
    this.amount,
    this.currency,
    this.transactionId,
    this.cardLast4,
  });

  factory PaymentDetailsPlus.fromJson(Map<String, dynamic> json) {
    return PaymentDetailsPlus(
      method: json['method'],
      status: json['status'],
      amount: json['amount']?.toDouble(),
      currency: json['currency'],
      transactionId: json['transactionId'],
      cardLast4: json['cardLast4'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'method': method,
      'status': status,
      'amount': amount,
      'currency': currency,
      'transactionId': transactionId,
      'cardLast4': cardLast4,
    };
  }
}

/// Booking dates
class BookingDatesPlus {
  String? checkIn;
  String? checkOut;
  int? numberOfNights;

  BookingDatesPlus({
    this.checkIn,
    this.checkOut,
    this.numberOfNights,
  });

  factory BookingDatesPlus.fromJson(Map<String, dynamic> json) {
    return BookingDatesPlus(
      checkIn: json['checkIn'],
      checkOut: json['checkOut'],
      numberOfNights: json['numberOfNights'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'checkIn': checkIn,
      'checkOut': checkOut,
      'numberOfNights': numberOfNights,
    };
  }
}

