import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../views/my_bookings_screen_plus.dart';
import '../routes/booking_routes.dart';
import '../controllers/my_bookings_controller_plus.dart';
import '../models/booking_models_plus.dart';
import '../services/cancel_booking_service.dart';

/// Integration helper for connecting the new My Bookings system
/// with the existing hotel booking workflow
class BookingIntegration {
  /// Navigate to My Bookings screen from anywhere in the app
  static void showMyBookings() {
    BookingNavigation.toMyBookings();
  }

  /// Show My Bookings as a modal bottom sheet
  static void showMyBookingsModal() {
    Get.bottomSheet(
      Container(
        height: Get.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: const MyBookingsScreenPlus(),
      ),
      isScrollControlled: true,
    );
  }

  /// Create a booking summary from successful booking completion
  static BookingSummaryPlus createBookingSummaryFromCompletion({
    required String bookingReferenceId,
    required String hotelName,
    required String checkIn,
    required String checkOut,
    required double totalAmount,
    required String currency,
    String? confirmationNumber,
    String? hotelImage,
    String? hotelAddress,
    int? numberOfRooms,
    int? numberOfGuests,
  }) {
    return BookingSummaryPlus(
      bookingReferenceId: bookingReferenceId,
      status: 'CONFIRMED',
      hotelName: hotelName,
      hotelImage: hotelImage,
      hotelAddress: hotelAddress,
      checkIn: checkIn,
      checkOut: checkOut,
      createdAt: DateTime.now().toIso8601String(),
      numberOfRooms: numberOfRooms ?? 1,
      numberOfGuests: numberOfGuests ?? 2,
      canCancel: true,
      confirmationNumber: confirmationNumber,
    );
  }

  /// Add a new booking to the My Bookings list after successful completion
  static void addNewBooking(BookingSummaryPlus booking) {
    try {
      // Check if My Bookings controller is registered
      if (Get.isRegistered<MyBookingsControllerPlus>()) {
        final controller = Get.find<MyBookingsControllerPlus>();

        // Add the new booking to the beginning of the list
        controller.bookings.insert(0, booking);

        // Refresh the filtered list
        controller.filterBookings();

        print('✅ [BOOKING_INTEGRATION] New booking added to My Bookings list');
      }
    } catch (e) {
      print('❌ [BOOKING_INTEGRATION] Error adding new booking: $e');
    }
  }

  /// Show booking success dialog with option to view in My Bookings
  static void showBookingSuccessDialog({
    required String bookingReferenceId,
    required String hotelName,
    VoidCallback? onViewBookings,
  }) {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 8),
            Text('تم الحجز بنجاح!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم تأكيد حجزك في $hotelName'),
            const SizedBox(height: 8),
            Text(
              'رقم الحجز: $bookingReferenceId',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'يمكنك عرض تفاصيل الحجز في قائمة حجوزاتي',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('حسناً'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              if (onViewBookings != null) {
                onViewBookings();
              } else {
                showMyBookings();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('عرض الحجوزات'),
          ),
        ],
      ),
    );
  }

  /// Create a floating action button for My Bookings
  static Widget createMyBookingsFloatingButton({
    VoidCallback? onPressed,
    String? tooltip,
  }) {
    return FloatingActionButton(
      onPressed: onPressed ?? showMyBookings,
      tooltip: tooltip ?? 'حجوزاتي',
      backgroundColor: Colors.blue,
      child: const Icon(
        Icons.bookmark,
        color: Colors.white,
      ),
    );
  }

  /// Create a My Bookings menu item for app drawer or menu
  static Widget createMyBookingsMenuItem({
    VoidCallback? onTap,
    bool showBadge = false,
    int badgeCount = 0,
  }) {
    return ListTile(
      leading: Stack(
        children: [
          const Icon(Icons.bookmark_border, size: 24),
          if (showBadge && badgeCount > 0)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                child: Text(
                  badgeCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
      title: const Text('حجوزاتي'),
      subtitle: const Text('عرض وإدارة حجوزاتك'),
      onTap: onTap ?? showMyBookings,
    );
  }

  /// Create a quick access card for My Bookings
  static Widget createMyBookingsQuickCard({
    VoidCallback? onTap,
    int? bookingCount,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap ?? showMyBookings,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.bookmark,
                size: 32,
                color: Colors.blue,
              ),
              const SizedBox(height: 8),
              const Text(
                'حجوزاتي',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (bookingCount != null) ...[
                const SizedBox(height: 4),
                Text(
                  '$bookingCount حجز',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Check if user has any bookings
  static Future<bool> hasBookings() async {
    try {
      // This would typically check with the API or local storage
      // For now, return true as a placeholder
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get booking count for badge display
  static Future<int> getBookingCount() async {
    try {
      // This would typically get the count from API or local storage
      // For now, return 0 as a placeholder
      return 0;
    } catch (e) {
      return 0;
    }
  }
}

/// Extension methods for easier integration
extension BookingIntegrationExtensions on Widget {
  /// Add My Bookings floating button to any screen
  Widget withMyBookingsButton({
    VoidCallback? onPressed,
    String? tooltip,
  }) {
    return Scaffold(
      body: this,
      floatingActionButton: BookingIntegration.createMyBookingsFloatingButton(
        onPressed: onPressed,
        tooltip: tooltip,
      ),
    );
  }
}

/// Booking event listeners for integration
class BookingEventListeners {
  /// Listen for booking completion events
  static void onBookingCompleted({
    required String bookingReferenceId,
    required String hotelName,
    required String checkIn,
    required String checkOut,
    required double totalAmount,
    required String currency,
    String? confirmationNumber,
    String? hotelImage,
    String? hotelAddress,
    int? numberOfRooms,
    int? numberOfGuests,
  }) {
    // Create booking summary
    final booking = BookingIntegration.createBookingSummaryFromCompletion(
      bookingReferenceId: bookingReferenceId,
      hotelName: hotelName,
      checkIn: checkIn,
      checkOut: checkOut,
      totalAmount: totalAmount,
      currency: currency,
      confirmationNumber: confirmationNumber,
      hotelImage: hotelImage,
      hotelAddress: hotelAddress,
      numberOfRooms: numberOfRooms,
      numberOfGuests: numberOfGuests,
    );

    // Add to My Bookings
    BookingIntegration.addNewBooking(booking);

    // Show success dialog
    BookingIntegration.showBookingSuccessDialog(
      bookingReferenceId: bookingReferenceId,
      hotelName: hotelName,
    );
  }

  /// Listen for booking cancellation events
  static void onBookingCancelled(String bookingReferenceId) {
    try {
      if (Get.isRegistered<MyBookingsControllerPlus>()) {
        final controller = Get.find<MyBookingsControllerPlus>();

        // Find and update the booking status
        final bookingIndex = controller.bookings.indexWhere(
          (booking) => booking.bookingReferenceId == bookingReferenceId,
        );

        if (bookingIndex != -1) {
          final updatedBooking = controller.bookings[bookingIndex].copyWith(
            status: 'CANCELLED',
          );
          controller.bookings[bookingIndex] = updatedBooking;
          controller.filterBookings();
        }
      }
    } catch (e) {
      print('❌ [BOOKING_INTEGRATION] Error updating cancelled booking: $e');
    }
  }
}

/// Extension for BookingSummaryPlus
extension BookingSummaryPlusExtensions on BookingSummaryPlus {
  BookingSummaryPlus copyWith({
    String? bookingReferenceId,
    String? status,
    String? hotelName,
    String? hotelImage,
    String? hotelAddress,
    String? checkIn,
    String? checkOut,
    double? totalAmount,
    String? currency,
    String? createdAt,
    int? numberOfRooms,
    int? numberOfGuests,
    String? paymentMethod,
    bool? canCancel,
    String? cancellationDeadline,
    List<String>? amenities,
    double? rating,
    String? confirmationNumber,
  }) {
    return BookingSummaryPlus(
      bookingReferenceId: bookingReferenceId ?? this.bookingReferenceId,
      status: status ?? this.status,
      hotelName: hotelName ?? this.hotelName,
      hotelImage: hotelImage ?? this.hotelImage,
      hotelAddress: hotelAddress ?? this.hotelAddress,
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
      createdAt: createdAt ?? this.createdAt,
      numberOfRooms: numberOfRooms ?? this.numberOfRooms,
      numberOfGuests: numberOfGuests ?? this.numberOfGuests,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      canCancel: canCancel ?? this.canCancel,
      cancellationDeadline: cancellationDeadline ?? this.cancellationDeadline,
      amenities: amenities ?? this.amenities,
      rating: rating ?? this.rating,
      confirmationNumber: confirmationNumber ?? this.confirmationNumber,
    );
  }
}
