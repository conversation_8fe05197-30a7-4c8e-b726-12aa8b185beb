import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/core.dart';
import '../../../repos/new_repos.dart';
import '../../../core/models/booking/booking_models.dart';
import '../../hotels/models/hotel_models.dart';
import '../models/booking_models_plus.dart';
import '../models/cancel_booking_models.dart';
import '../widgets/cancel_booking_bottom_sheet.dart';
import '../services/cancel_booking_service.dart';
import 'my_bookings_controller_plus.dart';

class BookingDetailsControllerPlus extends GetxController {
  // Observable variables
  final _isLoading = false.obs;
  final _bookingDetails = Rxn<BookingSummaryPlus>();
  final _error = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  BookingSummaryPlus? get bookingDetails => _bookingDetails.value;
  String get error => _error.value;
  bool fromDeepLink = false;

  // Load booking details from API
  Future<void> loadBookingDetails() async {
    try {
      _isLoading.value = true;
      _error.value = '';

      final bookingId = _getBookingId();
      if (bookingId == null) {
        _error.value = 'booking.load.error.notFound'.tr;
        return;
      }

      print('🔍 Loading booking details for ID: $bookingId');

      final result = await NewRepos.booking.getBookingDetails(bookingId);

      result.fold(
            (failure) {
          print('❌ Failed to load booking details: ${failure.errorMassage}');
          _error.value = failure.errorMassage.isNotEmpty
              ? failure.errorMassage
              : 'booking.load.error.failed'.tr;
        },
            (response) {
          if (_bookingDetails.value == null) {
            print('📝 No booking data in arguments, using API response');
          }
          _bookingDetails.value = response.data;

          print('✅ Booking details loaded successfully ${_bookingDetails.value?.toJson()}');
        },
      );
    } catch (e) {
      print('💥 Exception loading booking details: $e');
      _error.value = 'booking.load.error.exception'.tr;
    } finally {
      _isLoading.value = false;
    }
  }


  // Helper method to get booking ID from various sources
  String? _getBookingId() {
    // Try to get from current booking data
    if (_bookingDetails.value?.id != null) {
      return _bookingDetails.value!.id!;
    }

    // Try to get from route parameters
    final parameters = Get.parameters;
    if (parameters['bookingId'] != null) {
      return parameters['bookingId'];
    }

    // Try to get from arguments
    final arguments = Get.arguments;
    if (arguments is String) {
      return arguments;
    }
    if (arguments is Map && arguments['bookingId'] != null) {
      return arguments['bookingId'].toString();
    }

    return null;
  }

  @override
  void onInit() {
    _bookingDetails.value = Get.arguments["booking"];
    fromDeepLink = Get.arguments["fromDeepLink"] ?? false;
    loadBookingDetails();
    super.onInit();
  }

  // Share booking details
  void shareBooking() {
    final booking = _bookingDetails.value;
    if (booking == null) return;

    final shareText = '''
${'share.title'.tr}

${'share.bookingNumber'.tr}: ${booking.bookingReferenceId}
${'share.confirmationNumber'.tr}: ${booking.confirmationNumber}
${'share.hotel'.tr}: ${booking.hotel?.name}
${'share.location'.tr}: ${booking.hotel?.city}, ${booking.hotel?.country}

${'share.checkIn'.tr}: ${_formatDate(booking.checkIn)}
${'share.checkOut'.tr}: ${_formatDate(booking.checkOut)}
${'share.nights'.tr}: ${booking.numberOfNights}

${'share.totalPrice'.tr}: ${booking.pricing?.converted.price} ${booking.pricing?.converted.currency}
${'share.status'.tr}: ${booking.status}

${'share.footer'.tr}
''';

    Share.share(shareText, subject: '${'share.subject'.tr} - ${booking.bookingReferenceId}');
  }

  // Download booking as PDF
  void downloadBooking() {
    // TODO: Implement PDF generation and download
  }

  // Print booking
  void printBooking() {
    // TODO: Implement printing functionality
  }

  // Cancel booking using unified method
  Future<void> cancelBooking() async {
    final booking = _bookingDetails.value;
    if (booking == null) return;

    // 🎯 استخدام الدالة الموحدة لإلغاء الحجز
    await MyBookingsControllerPlus.cancelBookingUnified(booking);
  }

  /// Check if a booking can be cancelled
  bool _canCancelBooking(BookingSummaryPlus booking) {
    // Don't allow cancellation if already cancelled or completed
    final status = booking.status?.toUpperCase() ?? '';
    if (status == 'CANCELLED' ||
        status == 'CANCELLATION_IN_PROGRESS' ||
        status == 'COMPLETED' ||
        status == 'CHECKED_OUT' ||
        status == 'FAILED') {
      return false;
    }

    // Check if check-in date has passed
    try {
      final checkInDate =
          booking.checkIn != null ? DateTime.parse(booking.checkIn!) : null;

      if (checkInDate != null) {
        final now = DateTime.now();
        // Allow cancellation until the end of check-in day
        final checkInEndOfDay = DateTime(
            checkInDate.year, checkInDate.month, checkInDate.day, 23, 59, 59);

        if (now.isAfter(checkInEndOfDay)) {
          return false;
        }
      }
    } catch (e) {
      print('❌ [BOOKING_DETAILS] Error parsing check-in date: $e');
      return false;
    }

    return true;
  }

  /// Perform the actual booking cancellation
  Future<void> _performCancellation(
    BookingSummaryPlus booking,
    CancellationReason reason,
    String? customReason,
  ) async {
    try {
      // Close the bottom sheet first
      Get.back();

      // Show loading indicator
      _isLoading.value = true;
      Core.showGlobalSnackBar(
        'cancellation.inProgress'.tr,
        backgroundColor: Colors.blue,
      );

      // Get the cancellation service
      final cancelService = Get.find<CancelBookingService>();

      // Format the cancellation reason
      final formattedReason =
          cancelService.formatCancellationReason(reason, customReason);

      // Call the cancellation API
      final response = await cancelService.cancelBooking(
        bookingReferenceId: booking.bookingReferenceId ?? '',
        cancellationReason: formattedReason,
      );

      final success = response.booking != null;
      if (success) {
        // Update the booking status locally
        final updatedBooking =
            booking.copyWith(status: 'CANCELLATION_IN_PROGRESS');
        _bookingDetails.value = updatedBooking;

        // Show success message
        Core.showGlobalSnackBar(
          'cancellation.success'.tr,
          backgroundColor: Colors.green,
        );

        // Refresh the booking details
        await loadBookingDetails();
      } else {
        throw Exception('cancellation.error.general'.tr);
      }
    } catch (e) {
      print('❌ [BOOKING_DETAILS] Cancellation failed: $e');

      String errorMessage = 'cancellation.error.general'.tr;
      if (e.toString().contains('not found')) {
        errorMessage = 'cancellation.error.notFound'.tr;
      } else if (e.toString().contains('unauthorized')) {
        errorMessage = 'cancellation.error.unauthorized'.tr;
      }
      Core.showGlobalSnackBar(
        errorMessage,
        backgroundColor: Colors.red,
      );

    } finally {
      _isLoading.value = false;
    }
  }

  // Modify booking
  void modifyBooking() {
    // TODO: Implement booking modification
  }

  // Get status color
  Color getStatusColor(String? status) {
    switch (status?.toUpperCase()) {
      case 'CONFIRMED':
        return Colors.green;
      case 'PENDING':
        return Colors.orange;
      case 'CANCELLED':
        return Colors.red;
      case 'FAILED':
        return Colors.red.shade700;
      default:
        return Colors.grey;
    }
  }

  // Get status icon
  IconData getStatusIcon(String? status) {
    switch (status?.toUpperCase()) {
      case 'CONFIRMED':
        return Icons.check_circle;
      case 'PENDING':
        return Icons.schedule;
      case 'CANCELLED':
        return Icons.cancel;
      case 'FAILED':
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  // Get localized status text
  String getStatusText(String? status) {
    switch (status?.toUpperCase()) {
      case 'CONFIRMED':
        return 'bookingStatus.confirmed'.tr;
      case 'PENDING':
        return 'bookingStatus.pending'.tr;
      case 'CANCELLED':
        return 'bookingStatus.cancelled'.tr;
      case 'FAILED':
        return 'bookingStatus.failed'.tr;
      default:
        return status ?? 'bookingStatus.unknown'.tr;
    }
  }

  // Format date
  String _formatDate(String? dateString) {
    if (dateString == null) return '';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
