import 'package:flutter/material.dart';
import '../../../core/core.dart';
import '../../../repos/new_repos.dart';
import '../../../core/models/booking/booking_models.dart';
import '../models/booking_models_plus.dart';
import '../models/cancel_booking_models.dart';
import '../services/cancel_booking_service.dart';
import '../widgets/cancel_booking_bottom_sheet.dart';
import '../routes/booking_routes.dart';

class MyBookingsControllerPlus extends GetxController {
  // Observable variables
  final _isLoading = false.obs;
  final _isRefreshing = false.obs;
  final _bookings = <BookingSummaryPlus>[].obs;
  final _filteredBookings = <BookingSummaryPlus>[].obs;
  final _currentPage = 1.obs;
  final _totalPages = 1.obs;
  final _totalBookings = 0.obs;
  final _selectedStatus = 'ALL'.obs;
  final _searchQuery = ''.obs;
  final _hasMoreData = true.obs;

  // Text controllers
  final searchController = TextEditingController();

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isRefreshing => _isRefreshing.value;
  List<BookingSummaryPlus> get bookings => _bookings;
  List<BookingSummaryPlus> get filteredBookings => _filteredBookings;
  int get currentPage => _currentPage.value;
  int get totalPages => _totalPages.value;
  int get totalBookings => _totalBookings.value;
  String get selectedStatus => _selectedStatus.value;
  String get searchQuery => _searchQuery.value;
  bool get hasMoreData => _hasMoreData.value;

  // Status options
  final List<String> statusOptions = [
    'ALL',
    'PENDING',
    'CONFIRMED',
    'CANCELLED',
    'FAILED'
  ];

  @override
  void onInit() {
    super.onInit();
    _setupSearchListener();
    loadBookings();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  void _setupSearchListener() {
    searchController.addListener(() {
      _searchQuery.value = searchController.text;
      filterBookings();
    });
  }

  /// Load mock bookings when repository is not available
  void _loadMockBookings() {
    try {
      // Create mock bookings for testing
      final mockBookings = [
        BookingSummaryPlus(
          bookingReferenceId: 'BOOKING-REF-mock-001',
          hotelName: 'Mock Hotel 1',
          status: 'Confirmed',
          checkIn:
              DateTime.now().add(const Duration(days: 1)).toIso8601String(),
          checkOut:
              DateTime.now().add(const Duration(days: 3)).toIso8601String(),
          nights: 2,
          numberOfRooms: 1,
          numberOfGuests: 2,
          canCancel: true,
        ),
        BookingSummaryPlus(
          bookingReferenceId: 'BOOKING-REF-mock-002',
          hotelName: 'Mock Hotel 2',
          status: 'Processing',
          checkIn:
              DateTime.now().add(const Duration(days: 5)).toIso8601String(),
          checkOut:
              DateTime.now().add(const Duration(days: 7)).toIso8601String(),
          nights: 2,
          numberOfRooms: 1,
          numberOfGuests: 1,
          canCancel: true,
        ),
      ];

      _bookings.value = mockBookings;
      _filteredBookings.value = mockBookings;
      _isLoading.value = false;
      _isRefreshing.value = false;

      print(
          '✅ [MY_BOOKINGS] Mock bookings loaded: ${mockBookings.length} bookings');
    } catch (e) {
      print('❌ [MY_BOOKINGS] Error loading mock bookings: $e');
      _isLoading.value = false;
      _isRefreshing.value = false;
    }
  }

  // Load bookings from API
  Future<void> loadBookings({bool isRefresh = false}) async {
    if (isRefresh) {
      _isRefreshing.value = true;
      _currentPage.value = 1;
      _hasMoreData.value = true;
    } else {
      _isLoading.value = true;
    }

    print(
        '🔵 [MY_BOOKINGS] Loading bookings - Page: ${_currentPage.value}, Status: ${_selectedStatus.value}');

    if (!Get.isRegistered<BookingRepository>()) {
      print(
          '⚠️ [MY_BOOKINGS] BookingRepository not registered yet, using mock data');
      _loadMockBookings();
      return;
    }

    final request = MyBookingsRequest(
      page: _currentPage.value,
      limit: 10,
      status: _selectedStatus.value,
    );

    final result = await NewRepos.booking.getMyBookings(request);

    result.fold(
      (failure) {
        print(
            '❌ [MY_BOOKINGS] Failed to load bookings: ${failure.errorMassage}');
        Core.showGlobalSnackBar(
          'Failed to load bookings: ${failure.errorMassage}',
          backgroundColor: Colors.red,
        );
      },
      (response) {
        final bookingsList = response.data?.bookings ?? [];

        print('✅ [MY_BOOKINGS] Loaded ${bookingsList.length} bookings');

        if (isRefresh || _currentPage.value == 1) {
          _bookings.value = bookingsList;
        } else {
          _bookings.addAll(bookingsList);
        }

        final pagination = response.data?.pagination;
        if (pagination != null) {
          _totalPages.value = pagination.totalPages ?? 1;
          _totalBookings.value = pagination.total ?? 0;
          _hasMoreData.value = _currentPage.value < _totalPages.value;
        }

        filterBookings();
      },
    );

    try {
      // Optional: Additional logic if needed
    } catch (e) {
      print('❌ [MY_BOOKINGS] Error loading bookings: $e');
      Core.showGlobalSnackBar(
        'Error loading bookings: $e',
        backgroundColor: Colors.red,
      );
    } finally {
      _isLoading.value = false;
      _isRefreshing.value = false;
    }
  }

  // Load more bookings (pagination)
  Future<void> loadMoreBookings() async {
    if (!_hasMoreData.value || _isLoading.value) return;

    _currentPage.value++;
    await loadBookings();
  }

  // Refresh bookings
  Future<void> refreshBookings() async {
    await loadBookings(isRefresh: true);
  }

  // Filter bookings by status
  void filterByStatus(String status) {
    if (_selectedStatus.value != status) {
      _selectedStatus.value = status;
      _currentPage.value = 1;
      loadBookings(isRefresh: true);
    }
  }

  // Filter bookings locally based on search query
  void filterBookings() {
    if (_searchQuery.value.isEmpty) {
      _filteredBookings.value = _bookings;
    } else {
      final query = _searchQuery.value.toLowerCase();
      _filteredBookings.value = _bookings.where((booking) {
        return booking.hotelName?.toLowerCase().contains(query) == true ||
            booking.bookingReferenceId?.toLowerCase().contains(query) == true ||
            booking.status?.toLowerCase().contains(query) == true;
      }).toList();
    }
  }

  // Clear search
  void clearSearch() {
    searchController.clear();
    _searchQuery.value = '';
    filterBookings();
  }

  // Navigate to booking details
  void viewBookingDetails(BookingSummaryPlus booking) {
    if (booking.bookingReferenceId != null) {
      print(
          '🔵 [MY_BOOKINGS] Navigating to booking details: ${booking.bookingReferenceId}');

      // Navigate to booking details screen
      BookingNavigation.toBookingDetails(
          booking.id!,
          booking.bookingReferenceId!
      );
    }
  }

  // Get status color
  Color getStatusColor(String? status) {
    switch (status?.toUpperCase()) {
      case 'CONFIRMED':
        return Colors.green;
      case 'PENDING':
        return Colors.orange;
      case 'CANCELLED':
        return Colors.red;
      case 'FAILED':
        return Colors.red.shade700;
      default:
        return Colors.grey;
    }
  }

  // Get status icon
  IconData getStatusIcon(String? status) {
    switch (status?.toUpperCase()) {
      case 'CONFIRMED':
        return Icons.check_circle;
      case 'PENDING':
        return Icons.schedule;
      case 'CANCELLED':
        return Icons.cancel;
      case 'FAILED':
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  // Get localized status text
  String getStatusText(String? status) {
    switch (status?.toUpperCase()) {
      case 'CONFIRMED':
        return 'bookingStatus.confirmed'.tr;
      case 'PENDING':
        return 'bookingStatus.pending'.tr;
      case 'CANCELLED':
        return 'bookingStatus.cancelled'.tr;
      case 'FAILED':
        return 'bookingStatus.failed'.tr;
      default:
        return status ?? 'bookingStatus.unknown'.tr;
    }
  }

  // Format date
  String formatDate(String? dateString) {
    if (dateString == null) return '';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  // Calculate nights
  int calculateNights(String? checkIn, String? checkOut) {
    if (checkIn == null || checkOut == null) return 0;

    try {
      final checkInDate = DateTime.parse(checkIn);
      final checkOutDate = DateTime.parse(checkOut);
      return checkOutDate.difference(checkInDate).inDays;
    } catch (e) {
      return 0;
    }
  }

  /// 🎯 **دالة موحدة لإلغاء الحجز - مشتركة بين جميع الشاشات**
  ///
  /// يمكن استدعاؤها من:
  /// - BookingCardPlus
  /// - BookingDetailsScreen
  /// - أي مكان آخر في التطبيق
  ///
  /// تضمن تجربة مستخدم موحدة وصيانة سهلة
  static Future<void> cancelBookingUnified(BookingSummaryPlus booking) async {
    try {
      // التحقق من إمكانية الإلغاء
      if (!_canCancelBookingStatic(booking)) {
        Core.showGlobalSnackBar(
          'لا يمكن إلغاء هذا الحجز في الوقت الحالي',
          backgroundColor: Colors.orange,
        );
        return;
      }

      // عرض نافذة اختيار سبب الإلغاء
      await CancelBookingBottomSheet.show(
        bookingReferenceId: booking.bookingReferenceId ?? '',
        hotelName: booking.hotelName ?? booking.hotel?.name ?? 'Unknown Hotel',
        onConfirm: (reason, customReason) async {
          await _performCancellationStatic(booking, reason, customReason);
        },
      );
    } catch (e) {
      print('❌ Error in unified cancel booking: $e');
      Core.showGlobalSnackBar(
        'حدث خطأ أثناء فتح نافذة الإلغاء',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Show cancellation bottom sheet and handle booking cancellation
  /// 🔄 **استخدم cancelBookingUnified بدلاً من هذه الدالة**
  @Deprecated('Use cancelBookingUnified instead for consistent experience')
  Future<void> showCancelBookingDialog(BookingSummaryPlus booking) async {
    await cancelBookingUnified(booking);
  }

  /// 🔧 **دالة static للتحقق من إمكانية الإلغاء**
  static bool _canCancelBookingStatic(BookingSummaryPlus booking) {
    // Don't allow cancellation if already cancelled or completed
    final status = booking.status?.toUpperCase() ?? '';
    if (status == 'CANCELLED' ||
        status == 'CANCELLATION_IN_PROGRESS' ||
        status == 'COMPLETED' ||
        status == 'REFUNDED') {
      return false;
    }

    // For testing purposes, be more lenient with dates
    try {
      if (booking.checkIn != null && booking.checkIn!.isNotEmpty) {
        final checkInDate = DateTime.parse(booking.checkIn!);
        final now = DateTime.now();
        final daysDifference = checkInDate.difference(now).inDays;

        // Allow cancellation if check-in is within 7 days (for testing)
        if (daysDifference < -7) {
          return false;
        }
      }
    } catch (e) {
      // Allow cancellation even if date parsing fails (for testing)
    }

    return true;
  }

  /// 🔧 **دالة static لتنفيذ الإلغاء الفعلي**
  static Future<void> _performCancellationStatic(
    BookingSummaryPlus booking,
    CancellationReason reason,
    String? customReason,
  ) async {
    try {
      // Close the bottom sheet first
      Get.back();

      // Show loading indicator
      Core.showGlobalSnackBar(
        'جاري إلغاء الحجز...',
        backgroundColor: Colors.blue,
      );

      // Get the cancellation service
      final cancelService = Get.find<CancelBookingService>();

      // Format the cancellation reason
      final formattedReason =
          cancelService.formatCancellationReason(reason, customReason);

      // Call the cancellation API
      final response = await cancelService.cancelBooking(
        bookingReferenceId: booking.bookingReferenceId ?? '',
        cancellationReason: formattedReason,
      );

      if (response.booking != null) {
        // Success - update UI and show success message
        Core.showGlobalSnackBar(
          'تم إلغاء الحجز بنجاح',
          backgroundColor: Colors.green,
        );

        // Refresh bookings if controller exists
        if (Get.isRegistered<MyBookingsControllerPlus>()) {
          final controller = Get.find<MyBookingsControllerPlus>();
          controller.loadBookings(isRefresh: true);
        }

        // Navigate back if we're in booking details
        if (Get.currentRoute.contains('booking-details')) {
          Get.back();
        }
      } else {
        throw Exception('فشل في إلغاء الحجز');
      }
    } catch (e) {
      print('❌ Error in cancellation: $e');
      Core.showGlobalSnackBar(
        'فشل في إلغاء الحجز: ${e.toString()}',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Perform the actual booking cancellation
  /// 🔄 **استخدم _performCancellationStatic بدلاً من هذه الدالة**
  @Deprecated('Use _performCancellationStatic instead')
  Future<void> _performCancellation(
    BookingSummaryPlus booking,
    CancellationReason reason,
    String? customReason,
  ) async {
    // Close the bottom sheet first
    Get.back();

    // Show loading indicator
    Core.showGlobalSnackBar(
      'جاري إلغاء الحجز...',
      backgroundColor: Colors.blue,
    );

    // Get the cancellation service
    final cancelService = Get.find<CancelBookingService>();

    // Format the cancellation reason
    final formattedReason =
        cancelService.formatCancellationReason(reason, customReason);

    // Call the cancellation API
    final response = await cancelService.cancelBooking(
      bookingReferenceId: booking.bookingReferenceId ?? '',
      cancellationReason: formattedReason,
    );

    final success = response.booking != null;
    final bookingRes = response.booking;
    if (success) {
      // Update the booking status locally
      _updateBookingStatusLocally(
          bookingRes?.bookingReferenceId ?? '', 'CANCELLATION_IN_PROGRESS');

      // Show success message
      const message = 'تم إلغاء الحجز بنجاح';

      Core.showGlobalSnackBar(
        message,
        backgroundColor: Colors.green,
      );

      // Refresh the bookings list
      await loadBookings();
    } else {
      throw Exception('فشل في إلغاء الحجز');
    }

    try {} catch (e) {
      print('❌ [MY_BOOKINGS] Cancellation failed: $e');

      String errorMessage = 'حدث خطأ أثناء إلغاء الحجز';
      if (e.toString().contains('not found')) {
        errorMessage = 'الحجز غير موجود أو تم إلغاؤه مسبقاً';
      } else if (e.toString().contains('unauthorized')) {
        errorMessage = 'غير مصرح لك بإلغاء هذا الحجز';
      }

      Core.showGlobalSnackBar(
        errorMessage,
        backgroundColor: Colors.red,
      );
    }
  }

  /// Check if a booking can be cancelled
  bool _canCancelBooking(BookingSummaryPlus booking) {
    // Don't allow cancellation if already cancelled or completed
    final status = booking.status?.toUpperCase() ?? '';
    if (status == 'CANCELLED' ||
        status == 'CANCELLATION_IN_PROGRESS' ||
        status == 'COMPLETED' ||
        status == 'CHECKED_OUT') {
      return false;
    }

    // Check if check-in date has passed
    try {
      final checkInDate = DateTime.parse(booking.checkIn ?? '');
      final now = DateTime.now();

      // Don't allow cancellation if check-in date has passed
      if (checkInDate.isBefore(now)) {
        return false;
      }
    } catch (e) {
      print('❌ [MY_BOOKINGS] Error parsing check-in date: $e');
      return false;
    }

    return true;
  }

  /// Update booking status locally without API call
  void _updateBookingStatusLocally(
      String bookingReferenceId, String newStatus) {
    try {
      // Update in main bookings list
      final bookingIndex = _bookings.indexWhere(
        (booking) => booking.bookingReferenceId == bookingReferenceId,
      );

      if (bookingIndex != -1) {
        final updatedBooking =
            _bookings[bookingIndex].copyWith(status: newStatus);
        _bookings[bookingIndex] = updatedBooking;
      }

      // Update in filtered bookings list
      final filteredIndex = _filteredBookings.indexWhere(
        (booking) => booking.bookingReferenceId == bookingReferenceId,
      );

      if (filteredIndex != -1) {
        final updatedBooking =
            _filteredBookings[filteredIndex].copyWith(status: newStatus);
        _filteredBookings[filteredIndex] = updatedBooking;
      }

      // Trigger UI update
      _bookings.refresh();
      _filteredBookings.refresh();
    } catch (e) {
      print('❌ [MY_BOOKINGS] Error updating booking status locally: $e');
    }
  }

  /// Get cancellation status text for display
  String getCancellationStatusText(String status) {
    switch (status.toUpperCase()) {
      case 'CANCELLATION_IN_PROGRESS':
        return 'bookingStatus.cancellationInProgress'.tr;
      case 'CANCELLED':
        return 'bookingStatus.cancelled'.tr;
      case 'REFUNDED':
        return 'bookingStatus.refunded'.tr;
      default:
        return getStatusText(status);
    }
  }

  /// Check if booking status indicates cancellation
  bool isCancellationStatus(String status) {
    final upperStatus = status.toUpperCase();
    return upperStatus == 'CANCELLATION_IN_PROGRESS' ||
        upperStatus == 'CANCELLED' ||
        upperStatus == 'REFUNDED';
  }
}
