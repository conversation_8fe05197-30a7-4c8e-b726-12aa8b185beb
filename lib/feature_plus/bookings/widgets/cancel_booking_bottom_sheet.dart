import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/theme/color_manager.dart';
import '../models/cancel_booking_models.dart';

/// Bottom sheet for selecting cancellation reason
class CancelBookingBottomSheet extends StatefulWidget {
  final String bookingReferenceId;
  final String hotelName;
  final Function(CancellationReason reason, String? customReason) onConfirm;

  const CancelBookingBottomSheet({
    super.key,
    required this.bookingReferenceId,
    required this.hotelName,
    required this.onConfirm,
  });

  /// Show the cancellation bottom sheet
  static Future<void> show({
    required String bookingReferenceId,
    required String hotelName,
    required Function(CancellationReason reason, String? customReason)
        onConfirm,
  }) {
    return Get.bottomSheet(
      CancelBookingBottomSheet(
        bookingReferenceId: bookingReferenceId,
        hotelName: hotelName,
        onConfirm: onConfirm,
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
    );
  }

  @override
  State<CancelBookingBottomSheet> createState() =>
      _CancelBookingBottomSheetState();
}

class _CancelBookingBottomSheetState extends State<CancelBookingBottomSheet> {
  CancellationReason? selectedReason;
  final TextEditingController customReasonController = TextEditingController();
  bool isLoading = false;

  @override
  void dispose() {
    customReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBookingInfo(),
                  const SizedBox(height: 24),
                  _buildWarningMessage(),
                  const SizedBox(height: 24),
                  _buildReasonSelection(),
                  if (selectedReason == CancellationReason.other) ...[
                    const SizedBox(height: 16),
                    _buildCustomReasonInput(),
                  ],
                  const SizedBox(height: 32),
                  _buildCancellationPolicy(),
                ],
              ),
            ),
          ),
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.cancel_outlined,
              color: Colors.red,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'myBookingsScreenPlus.cancelBooking'.tr,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorManager.shadowGray.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ColorManager.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.hotel,
                color: ColorManager.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.hotelName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColorManager.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.confirmation_number_outlined,
                color: ColorManager.grey,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${'myBookingsScreenPlus.bookingReference'.tr}: ${widget.bookingReferenceId}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: ColorManager.grey,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWarningMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'myBookingsScreenPlus.cancelWarning'.tr,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReasonSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'myBookingsScreenPlus.cancelReason'.tr,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: ColorManager.primary,
          ),
        ),
        const SizedBox(height: 12),
        ...CancellationReason.values.map((reason) => _buildReasonTile(reason)),
      ],
    );
  }

  Widget _buildReasonTile(CancellationReason reason) {
    final isSelected = selectedReason == reason;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color:
            isSelected ? ColorManager.primary.withOpacity(0.1) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? ColorManager.primary
              : ColorManager.grey.withOpacity(0.3),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: RadioListTile<CancellationReason>(
        value: reason,
        groupValue: selectedReason,
        onChanged: (value) {
          setState(() {
            selectedReason = value;
          });
        },
        title: Text(
          reason.localized,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? ColorManager.primary : ColorManager.black,
          ),
        ),
        activeColor: ColorManager.primary,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8),
      ),
    );
  }

  Widget _buildCustomReasonInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'myBookingsScreenPlus.pleaseSpecifyReason'.tr,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: ColorManager.primary,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: customReasonController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'myBookingsScreenPlus.writeCancelReason'.tr,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ColorManager.grey.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide:
                  const BorderSide(color: ColorManager.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  Widget _buildCancellationPolicy() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorManager.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ColorManager.blue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: ColorManager.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'myBookingsScreenPlus.cancellationPolicyTitle'.tr,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: ColorManager.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'myBookingsScreenPlus.cancellationPolicyText'.tr,
            style: const TextStyle(
              fontSize: 12,
              color: ColorManager.blue,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: isLoading ? null : () => Get.back(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: ColorManager.grey),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'myBookingsScreenPlus.cancel'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ColorManager.grey,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canConfirm() && !isLoading ? _handleConfirm : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: isLoading
                  ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
                  : Text(
                'myBookingsScreenPlus.confirmCancel'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

        ],
      ),
    );
  }

  bool _canConfirm() {
    if (selectedReason == null) return false;
    if (selectedReason == CancellationReason.other) {
      return customReasonController.text.trim().isNotEmpty;
    }
    return true;
  }

  void _handleConfirm() {
    if (!_canConfirm()) return;

    setState(() {
      isLoading = true;
    });

    final customReason = selectedReason == CancellationReason.other
        ? customReasonController.text.trim()
        : null;

    widget.onConfirm(selectedReason!, customReason);
  }
}
