import 'package:fandooq/core/data_base/features/global_app/lang.dart';
import 'package:fandooq/core/theme/app_themes.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import 'package:fandooq/feature_plus/feature_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:fandooq/feature/main/main.dart';

class SplashScreenPlus extends StatefulWidget {
  const SplashScreenPlus({super.key});
  
  @override
  State<SplashScreenPlus> createState() => _SplashScreenPlusState();
}

class _SplashScreenPlusState extends State<SplashScreenPlus> 
    with TickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _isAnimationComplete = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
    ));
  }

  void _startSplashSequence() async {
    // Set initial system UI
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      systemNavigationBarColor: ColorManager.primary,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));

    // Start animation
    _animationController.forward();

    // Wait for animation to complete
    await Future.delayed(const Duration(seconds: 3));

    // Complete animation
    setState(() {
      _isAnimationComplete = true;
    });

    // Wait for fade out
    await Future.delayed(const Duration(milliseconds: 500));

    // Reset system UI
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      systemNavigationBarColor: ColorManager.scaffold,
      systemNavigationBarIconBrightness: Brightness.dark,
    ));

    print("SPLASH_SCREEN : ");

    // Navigate to main page
    HomeIntegration.offAndHome();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        systemOverlayStyle: ColorManager.statusBarPrimaryFull,
      ),
      body: AnimatedContainer(
        color: _isAnimationComplete 
            ? Colors.transparent 
            : ColorManager.primary,
        duration: const Duration(milliseconds: 500),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: AnimatedOpacity(
            opacity: _isAnimationComplete ? 0 : 1,
            duration: const Duration(milliseconds: 500),
            child: Center(
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Lottie.asset(
                        "assets/lottie/fandooq_animation.json",
                        repeat: false,
                        onLoaded: (composition) {
                          _animationController.duration = composition.duration;
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
} 