import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/components/logger.dart';
import '../../../core/cache/preference_manager.dart';
import '../../../core/core.dart';
import '../../../core/services/confirmation_service.dart';
import '../../../repos/user/user.dart';
import '../../auth/controllers/auth_controller.dart';
import '../models/user_models_plus.dart';
import '../services/user_service_plus.dart';

/// 🎯 **UserControllerPlus - المتحكم المركزي لإدارة حالة المستخدم**
///
/// يدير جميع بيانات المستخدم وتفضيلاته عبر دورة حياة التطبيق
/// - معلومات المستخدم الأساسية
/// - تفضيلات التطبيق (اللغة، العملة، الثيم)
/// - حالة المصادقة والجلسة
/// - المفضلات والحجوزات المحفوظة
/// - الإشعارات والتنبيهات
class UserControllerPlus extends GetxController {
  static UserControllerPlus get instance => Get.find<UserControllerPlus>();

  final UserServicePlus _userService = UserServicePlus();

  // ==================== Observable Variables ====================

  /// 👤 **معلومات المستخدم الأساسية**
  final Rx<UserProfilePlus?> _userProfile = Rx<UserProfilePlus?>(null);
  UserProfilePlus? get userProfile => _userProfile.value;

  /// 🔐 **حالة المصادقة**
  final RxBool _isAuthenticated = false.obs;

  /// 🔐 **التحقق من حالة المصادقة من النظام الحقيقي**
  bool get isAuthenticated {
    // التحقق من النظام الحقيقي أولاً
    final isLoggedInPrefs = PreferenceManager.instance.isLoggedIn();
    final hasToken = PreferenceManager.instance.authToken.isNotEmpty;
    final hasUser = PreferenceManager.instance.userData() != null;

    // التحقق من AuthPlusController إذا كان متاحاً
    bool authPlusAuthenticated = false;
    try {
      if (Get.isRegistered<AuthPlusController>()) {
        final authController = Get.find<AuthPlusController>();
        authPlusAuthenticated = authController.isAuthenticated;
      }
    } catch (e) {
      // AuthPlusController غير متاح
    }

    // الحالة الحقيقية هي دمج كل المصادر
    final realAuthState =
        isLoggedInPrefs && hasToken && hasUser || authPlusAuthenticated;

    // تحديث الحالة الداخلية لتتطابق مع الحالة الحقيقية
    if (_isAuthenticated.value != realAuthState) {
      _isAuthenticated.value = realAuthState;
    }

    return realAuthState;
  }

  /// 🔄 **حالة التحميل**
  final RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  /// ⚙️ **تفضيلات المستخدم**
  final Rx<UserPreferencesPlus> _preferences = UserPreferencesPlus().obs;
  UserPreferencesPlus get preferences => _preferences.value;

  /// ❤️ **المفضلات**
  final RxList<String> _favoriteHotels = <String>[].obs;
  List<String> get favoriteHotels => _favoriteHotels;

  /// 🔔 **الإشعارات**
  final RxList<UserNotificationPlus> _notifications =
      <UserNotificationPlus>[].obs;
  List<UserNotificationPlus> get notifications => _notifications;
  final RxInt _unreadNotificationsCount = 0.obs;
  int get unreadNotificationsCount => _unreadNotificationsCount.value;

  /// 📊 **إحصائيات المستخدم**
  final Rx<UserStatsPlus> _userStats = UserStatsPlus().obs;
  UserStatsPlus get userStats => _userStats.value;

  /// 🎨 **تفضيلات الواجهة**
  final RxString _selectedLanguage = 'ar'.obs;
  String get selectedLanguage => _selectedLanguage.value;

  final RxString _selectedCurrency = 'USD'.obs;
  String get selectedCurrency => _selectedCurrency.value;

  final RxBool _isDarkMode = false.obs;
  bool get isDarkMode => _isDarkMode.value;

  // ==================== Lifecycle Methods ====================

  @override
  void onInit() {
    super.onInit();
    Log.debug('🚀 UserControllerPlus onInit() called');
    _initializeUser();
  }

  @override
  void onReady() {
    super.onReady();
    Log.debug('✅ UserControllerPlus onReady() called');
    // تحديث المفضلات من الخادم في الخلفية (بدون blocking)
    _updateFavoritesFromServer();
  }

  @override
  void onClose() {
    Log.debug('🔄 UserControllerPlus onClose() called');
    super.onClose();
  }

  // ==================== Initialization Methods ====================

  /// 🔄 **تهيئة المستخدم من البيانات المحفوظة**
  Future<void> _initializeUser() async {
    try {
      // تحميل حالة المصادقة (سريع - من التخزين المحلي فقط)
      await _loadAuthenticationState();

      // تحميل التفضيلات المحفوظة (سريع - من التخزين المحلي فقط)
      await _loadSavedPreferences();

      // تحميل المفضلات المحفوظة (سريع - من التخزين المحلي فقط)
      await _loadSavedFavoritesLocal();

      Log.debug('✅ User initialization completed');
    } catch (e) {
      Log.error('❌ Error initializing user: $e');
    }
  }


  /// 🚪 **تسجيل خروج المستخدم مع التأكيد**
  Future<void> logoutWithConfirmation() async {
    final confirmed = await ConfirmationService.showLogoutConfirmation();

    if (confirmed) {
      await logout();
    }
  }

  /// 🧹 **مسح بيانات المستخدم (للاستخدام الداخلي)**
  void clearUserData() {
    _userProfile.value = null;
    _isAuthenticated.value = false;
    _notifications.clear();
    _favoriteHotels.clear();
    print('✅ [USER_CONTROLLER_PLUS] User data cleared');
  }

  /// 🚪 **تسجيل خروج المستخدم**
  Future<void> logout() async {
    try {
      _isLoading.value = true;

      // تسجيل خروج من النظام الحقيقي أولاً
      await PreferenceManager.instance.logout();

      // مسح البيانات المحلية
      await _clearUserData();

      // إعادة تعيين المتغيرات
      _userProfile.value = null;
      _isAuthenticated.value = false;
      _notifications.clear();
      _favoriteHotels.clear();

      // مسح بيانات AuthPlusController إذا كان متاحاً (بدون استدعاء logout لتجنب الحلقة)
      try {
        if (Get.isRegistered<AuthPlusController>()) {
          final authController = Get.find<AuthPlusController>();
          // مسح البيانات فقط بدون استدعاء logout
          authController.reset();
        }
      } catch (e) {
        Log.debug('AuthPlusController not available for logout');
      }

      Core.showGlobalSnackBar('تم تسجيل الخروج بنجاح');

      // الانتقال لصفحة تسجيل الدخول
      Get.offAllNamed('/auth-plus');
    } catch (e) {
      Log.error('❌ Logout error: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  // ==================== Preferences Methods ====================

  /// 🌐 **تغيير اللغة**
  Future<void> changeLanguage(String languageCode) async {
    try {
      _selectedLanguage.value = languageCode;
      _preferences.value = _preferences.value.copyWith(language: languageCode);

      // حفظ التفضيلات
      await _savePreferences();

      // تطبيق اللغة
      Get.updateLocale(Locale(languageCode));

      Core.showGlobalSnackBar('تم تغيير اللغة بنجاح');
    } catch (e) {
      Log.error('❌ Error changing language: $e');
    }
  }

  /// 💱 **تغيير العملة**
  Future<void> changeCurrency(String currencyCode) async {
    try {
      _selectedCurrency.value = currencyCode;
      _preferences.value = _preferences.value.copyWith(currency: currencyCode);

      // حفظ التفضيلات
      await _savePreferences();

      Core.showGlobalSnackBar('تم تغيير العملة بنجاح');
    } catch (e) {
      Log.error('❌ Error changing currency: $e');
    }
  }

  /// 🎨 **تغيير الثيم**
  Future<void> toggleDarkMode() async {
    try {
      _isDarkMode.value = !_isDarkMode.value;
      _preferences.value =
          _preferences.value.copyWith(isDarkMode: _isDarkMode.value);

      // حفظ التفضيلات
      await _savePreferences();

      // تطبيق الثيم
      Get.changeThemeMode(_isDarkMode.value ? ThemeMode.dark : ThemeMode.light);

      Core.showGlobalSnackBar(_isDarkMode.value
          ? 'تم تفعيل الوضع الليلي'
          : 'تم تفعيل الوضع النهاري');
    } catch (e) {
      Log.error('❌ Error toggling dark mode: $e');
    }
  }

  // ==================== Favorites Methods ====================

  /// ❤️ **إضافة فندق للمفضلة**
  Future<void> addToFavorites(String hotelId) async {
    try {
      if (!_favoriteHotels.contains(hotelId)) {
        _favoriteHotels.add(hotelId);
        await _saveFavorites();
        Core.showGlobalSnackBar('تم إضافة الفندق للمفضلة');
      }
    } catch (e) {
      Log.error('❌ Error adding to favorites: $e');
    }
  }

  /// 💔 **إزالة فندق من المفضلة**
  Future<void> removeFromFavorites(String hotelId) async {
    try {
      _favoriteHotels.remove(hotelId);
      await _saveFavorites();
      Core.showGlobalSnackBar('تم إزالة الفندق من المفضلة');
    } catch (e) {
      Log.error('❌ Error removing from favorites: $e');
    }
  }

  /// ❓ **التحقق من وجود فندق في المفضلة**
  bool isFavorite(String hotelId) {
    // التحقق من النظام الحقيقي أولاً
    try {
      final isRealFavorite = UserRepo.to.favExist(hotelId);
      if (isRealFavorite) return true;
    } catch (e) {
      Log.debug('Real favorites check failed, using local: $e');
    }

    // fallback للنظام المحلي
    return _favoriteHotels.contains(hotelId);
  }


  /// ✅ **تحديد إشعار كمقروء**
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        _updateUnreadCount();
        await _userService.markNotificationAsRead(notificationId);
      }
    } catch (e) {
      Log.error('❌ Error marking notification as read: $e');
    }
  }

  /// 📊 **تحديث عدد الإشعارات غير المقروءة**
  void _updateUnreadCount() {
    _unreadNotificationsCount.value =
        _notifications.where((n) => !n.isRead).length;
  }

  // ==================== Stats Methods ====================

  /// 📈 **تحميل إحصائيات المستخدم**
  Future<void> loadUserStats() async {
    try {
      final stats = await _userService.getUserStats();
      if (stats != null) {
        _userStats.value = stats;
      }
    } catch (e) {
      Log.error('❌ Error loading user stats: $e');
    }
  }

  // ==================== Storage Methods ====================


  /// 📖 **تحميل حالة المصادقة**
  Future<void> _loadAuthenticationState() async {
    try {
      // تحميل من النظام الحقيقي أولاً
      final isLoggedIn = PreferenceManager.instance.isLoggedIn();
      final existingUser = PreferenceManager.instance.userData();

      if (isLoggedIn && existingUser != null) {
        // تحويل UserModel إلى UserProfilePlus
        _userProfile.value = UserProfilePlus(
          id: existingUser.id?.toString() ?? '',
          firstName: existingUser.fName ?? '',
          lastName: existingUser.lName ?? '',
          email: existingUser.email ?? '',
          phoneNumber: existingUser.phone ?? '',
          nationality: existingUser.nationality,
          createdAt: DateTime.now(), // UserModel لا يحتوي على createdAt
          isPhoneVerified: existingUser.phoneVerified ?? true,
          isEmailVerified: existingUser.emailVerified ?? false,
        );
        _isAuthenticated.value = true;
        Log.debug('✅ Loaded user from existing system: ${existingUser.fName}');
      } else {
        // تحميل من النظام الجديد كـ fallback
        final authValue =
            await PreferenceManager.instance.getValue('is_authenticated');
        _isAuthenticated.value = authValue == 'true';
        final profileJson =
            await PreferenceManager.instance.getValue('user_profile');
        if (profileJson != null && profileJson.isNotEmpty) {
          try {
            _userProfile.value = UserProfilePlus.fromJsonString(profileJson);
            Log.debug('✅ Loaded user profile from new system');
          } catch (e) {
            Log.error('❌ Error parsing user profile JSON: $e');
            _userProfile.value = null;
            _isAuthenticated.value = false;
          }
        }
      }
    } catch (e) {
      Log.error('❌ Error loading authentication state: $e');
      _isAuthenticated.value = false;
      _userProfile.value = null;
    }
  }

  /// 💾 **حفظ التفضيلات**
  Future<void> _savePreferences() async {
    await PreferenceManager.instance
        .saveValue('user_preferences', _preferences.value.toJsonString());
  }

  /// 📖 **تحميل التفضيلات المحفوظة**
  Future<void> _loadSavedPreferences() async {
    try {
      final preferencesJson =
          await PreferenceManager.instance.getValue('user_preferences');
      if (preferencesJson != null && preferencesJson.isNotEmpty) {
        _preferences.value =
            UserPreferencesPlus.fromJsonString(preferencesJson);
        _selectedLanguage.value = _preferences.value.language;
        _selectedCurrency.value = _preferences.value.currency;
        _isDarkMode.value = _preferences.value.isDarkMode;
        Log.debug('✅ Loaded user preferences from storage');
      } else {
        // استخدام التفضيلات الافتراضية
        _preferences.value = UserPreferencesPlus();
        Log.debug('✅ Using default user preferences');
      }
    } catch (e) {
      Log.error('❌ Error loading saved preferences: $e');
      // في حالة الخطأ، استخدام التفضيلات الافتراضية
      _preferences.value = UserPreferencesPlus();
    }
  }

  /// 💾 **حفظ المفضلات**
  Future<void> _saveFavorites() async {
    await PreferenceManager.instance
        .saveValue('favorite_hotels', _favoriteHotels.join(','));
  }

  /// 📖 **تحميل المفضلات المحفوظة من التخزين المحلي فقط (سريع)**
  Future<void> _loadSavedFavoritesLocal() async {
    try {
      // تحميل من التخزين المحلي فقط (أسرع)
      final favoritesString =
          await PreferenceManager.instance.getValue('favorite_hotels');
      if (favoritesString != null && favoritesString.isNotEmpty) {
        _favoriteHotels.value = favoritesString.split(',');
        Log.debug(
            '✅ Loaded ${_favoriteHotels.length} favorites from local storage');
      }
    } catch (e) {
      Log.error('❌ Error loading saved favorites: $e');
      // في حالة الخطأ، استخدام قائمة فارغة
      _favoriteHotels.value = [];
    }
  }

  /// 📖 **تحميل المفضلات المحفوظة مع التحديث من الخادم**
  Future<void> _loadSavedFavorites() async {
    try {
      // تحميل من التخزين المحلي أولاً (أسرع)
      await _loadSavedFavoritesLocal();

      // تحميل من النظام الحقيقي في الخلفية (بدون انتظار)
      if (_isAuthenticated.value) {
        _userService.getFavoriteHotels().then((realFavorites) {
          if (realFavorites.isNotEmpty) {
            _favoriteHotels.value = realFavorites;
            _saveFavoritesToLocal();
            update(); // تحديث UI
            Log.debug(
                '✅ Updated ${realFavorites.length} favorites from server');
          }
        }).catchError((e) {
          Log.debug('Real favorites loading failed: $e');
        });
      }
    } catch (e) {
      Log.error('❌ Error loading saved favorites: $e');
      // في حالة الخطأ، استخدام قائمة فارغة
      _favoriteHotels.value = [];
    }
  }

  /// 💾 **حفظ المفضلات محلياً**
  Future<void> _saveFavoritesToLocal() async {
    final favoritesString = _favoriteHotels.join(',');
    await PreferenceManager.instance
        .saveValue('favorite_hotels', favoritesString);
  }

  /// 🗑️ **مسح بيانات المستخدم**
  Future<void> _clearUserData() async {
    await PreferenceManager.instance.saveValue('is_authenticated', '');
    await PreferenceManager.instance.saveValue('user_profile', '');
    await PreferenceManager.instance.saveValue('user_preferences', '');
    await PreferenceManager.instance.saveValue('favorite_hotels', '');
  }

  // ==================== Utility Methods ====================

  /// 📱 **الحصول على اسم المستخدم للعرض**
  String get displayName {
    if (_userProfile.value != null) {
      return '${_userProfile.value!.firstName} ${_userProfile.value!.lastName}';
    }
    return 'مستخدم';
  }

  /// 📧 **الحصول على البريد الإلكتروني**
  String get email {
    return _userProfile.value?.email ?? '';
  }

  /// 📞 **الحصول على رقم الهاتف**
  String get phoneNumber {
    return _userProfile.value?.phoneNumber ?? '';
  }

  /// 🖼️ **الحصول على صورة المستخدم**
  String get profileImageUrl {
    return _userProfile.value?.profileImageUrl ?? '';
  }

  /// 🎯 **التحقق من اكتمال الملف الشخصي**
  bool get isProfileComplete {
    if (_userProfile.value == null) return false;
    return _userProfile.value!.firstName.isNotEmpty &&
        _userProfile.value!.lastName.isNotEmpty &&
        _userProfile.value!.email.isNotEmpty;
  }

  /// 🔄 **تحديث المفضلات من الخادم في الخلفية**
  Future<void> _updateFavoritesFromServer() async {
    if (!_isAuthenticated.value) return;

    try {
      final realFavorites = await _userService.getFavoriteHotels();
      if (realFavorites.isNotEmpty) {
        _favoriteHotels.value = realFavorites;
        _saveFavoritesToLocal();
        update(); // تحديث UI
        Log.debug('✅ Updated ${realFavorites.length} favorites from server');
      }
    } catch (e) {
      Log.debug('Background favorites update failed: $e');
      // لا نعرض خطأ للمستخدم لأن هذا تحديث في الخلفية
    }
  }

  /// 🔄 **تحديث المفضلات**
  Future<void> refreshFavorites() async {
    await _loadSavedFavorites();
    update();
  }

  /// 🗑️ **مسح جميع المفضلات**
  Future<void> clearAllFavorites() async {
    try {
      Log.debug('🗑️ Clearing all favorites...');

      // مسح من النظام الحقيقي
      for (final hotelId in List.from(_favoriteHotels)) {
        await removeFromFavorites(hotelId);
      }

      // مسح من التخزين المحلي
      _favoriteHotels.clear();
      await _saveFavoritesToLocal();

      update();
      Log.debug('✅ All favorites cleared successfully');
    } catch (e) {
      Log.error('❌ Error clearing all favorites: $e');
    }
  }


}
