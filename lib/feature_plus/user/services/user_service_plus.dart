import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';

import '../../../core/components/logger.dart';
import '../../../core/network_provider/networking.dart';
import '../../../repos/user/user.dart';
import '../../../core/models/hotels/hotels.dart';
import '../models/user_models_plus.dart';

/// 🌐 **خدمة المستخدم - إدارة طلبات API الخاصة بالمستخدم**
///
/// دمج مع النظام الحقيقي للتطبيق
class UserServicePlus {
  final NetworkHandler _networkHandler = NetworkHandler();
  // ==================== Authentication APIs ====================

  /// 🔐 **تسجيل دخول المستخدم**
  Future<LoginResponsePlus> login(String phone, String otp) async {
    try {
      Log.debug('🔐 Attempting login for phone: $phone');

      // استخدام API الحقيقي للتحقق من OTP
      final response = await _networkHandler.post(
        ObjectResponse(), // نوع الاستجابة
        '/auth/verify-phone',
        body: {
          'phone': phone,
          'otp': otp,
        },
      );

      if (response.isRequestSuccess && response.body != null) {
        final data = response.body!.data;
        Log.debug('✅ Login successful');

        // تحويل البيانات من API إلى نموذج UserProfilePlus
        final user = UserProfilePlus(
          id: data['user']['id'] ?? '',
          firstName: data['user']['firstName'] ?? '',
          lastName: data['user']['lastName'] ?? '',
          email: '', // API لا يرجع email حالياً
          phoneNumber: data['user']['phone'] ?? phone,
          nationality: data['user']['nationality'],
          createdAt: DateTime.parse(
              data['user']['createdAt'] ?? DateTime.now().toIso8601String()),
          lastLoginAt: data['user']['lastLoginAt'] != null
              ? DateTime.parse(data['user']['lastLoginAt'])
              : null,
          isPhoneVerified: data['user']['isPhoneVerified'] ?? false,
          isEmailVerified: false,
        );

        return LoginResponsePlus(
          success: true,
          message: data['message'] ?? 'تم تسجيل الدخول بنجاح',
          user: user,
          token: data['token'],
        );
      } else {
        Log.error('❌ Login failed with status: ${response.statusCode}');
        return LoginResponsePlus(
          success: false,
          message: response.failure?.message ?? 'فشل في تسجيل الدخول',
        );
      }
    } catch (e) {
      Log.error('❌ Login error: $e');
      return LoginResponsePlus(
        success: false,
        message: 'حدث خطأ أثناء تسجيل الدخول',
      );
    }
  }

  /// 📱 **إرسال رمز التحقق**
  Future<bool> sendOTP(String phone) async {
    try {
      Log.debug('📱 Sending OTP to phone: $phone');

      // محاكاة إرسال OTP ناجح للاختبار
      await Future.delayed(const Duration(seconds: 1));

      Log.debug('✅ OTP sent successfully (dummy: 1234)');
      return true;
    } catch (e) {
      Log.error('❌ Send OTP error: $e');
      return false;
    }
  }

  // ==================== Profile APIs ====================


  /// ✏️ **تحديث ملف المستخدم**
  Future<bool> updateUserProfile(UserProfilePlus profile) async {
    try {
      Log.debug('✏️ Updating user profile');

      // محاكاة تحديث ملف المستخدم للاختبار
      await Future.delayed(const Duration(milliseconds: 500));

      Log.debug('✅ User profile updated successfully');
      return true;
    } catch (e) {
      Log.error('❌ Update user profile error: $e');
      return false;
    }
  }

  // ==================== Notifications APIs ====================

  /// 🔔 **الحصول على الإشعارات**
  Future<List<UserNotificationPlus>> getNotifications({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      Log.debug('🔔 Fetching notifications - Page: $page, Limit: $limit');

      // محاكاة تحميل الإشعارات للاختبار
      await Future.delayed(const Duration(milliseconds: 500));

      Log.debug('✅ Notifications fetched successfully');
      return [
        UserNotificationPlus(
          id: '1',
          title: 'مرحباً بك في فندق',
          message: 'تم تأكيد حجزك بنجاح في فندق الريتز كارلتون',
          type: 'booking',
          isRead: false,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        UserNotificationPlus(
          id: '2',
          title: 'عرض خاص',
          message: 'احصل على خصم 20% على حجزك القادم',
          type: 'promotion',
          isRead: true,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ];
    } catch (e) {
      Log.error('❌ Get notifications error: $e');
      return [];
    }
  }

  /// ✅ **تحديد إشعار كمقروء**
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      Log.debug('✅ Marking notification as read: $notificationId');

      // محاكاة تحديد الإشعار كمقروء للاختبار
      await Future.delayed(const Duration(milliseconds: 200));

      Log.debug('✅ Notification marked as read successfully');
      return true;
    } catch (e) {
      Log.error('❌ Mark notification as read error: $e');
      return false;
    }
  }

  // ==================== Preferences APIs ====================

  /// ⚙️ **الحصول على تفضيلات المستخدم**
  Future<UserPreferencesPlus?> getUserPreferences() async {
    try {
      Log.debug('⚙️ Fetching user preferences');

      // محاكاة تحميل التفضيلات للاختبار
      await Future.delayed(const Duration(milliseconds: 300));

      Log.debug('✅ User preferences fetched successfully');
      return UserPreferencesPlus(
        language: 'ar',
        currency: 'USD',
        isDarkMode: false,
        notificationsEnabled: true,
        emailNotifications: true,
        pushNotifications: true,
      );
    } catch (e) {
      Log.error('❌ Get user preferences error: $e');
      return null;
    }
  }

  /// 💾 **حفظ تفضيلات المستخدم**
  Future<bool> saveUserPreferences(UserPreferencesPlus preferences) async {
    try {
      Log.debug('💾 Saving user preferences');

      // محاكاة حفظ التفضيلات للاختبار
      await Future.delayed(const Duration(milliseconds: 300));

      Log.debug('✅ User preferences saved successfully');
      return true;
    } catch (e) {
      Log.error('❌ Save user preferences error: $e');
      return false;
    }
  }

  // ==================== Favorites APIs ====================

  /// ❤️ **الحصول على المفضلات**
  Future<List<String>> getFavoriteHotels() async {
    try {
      Log.debug('❤️ Fetching favorite hotels');

      // استخدام النظام الحقيقي للمفضلات
      final userRepo = UserRepo.to;
      final favoritesResponse = await userRepo.getFavorite();

      // التحقق من وجود الاستجابة والفنادق
      if (favoritesResponse.hotels == null) {
        Log.debug('⚠️ Favorites response is null, using local storage');
        return _getLocalFavorites();
      }

      // تحويل قائمة الفنادق إلى قائمة معرفات
      final favoriteIds = favoritesResponse.hotels!
          .map((hotel) => hotel.code ?? '')
          .where((code) => code.isNotEmpty)
          .toList();

      Log.debug(
          '✅ Favorite hotels fetched successfully: ${favoriteIds.length} hotels');
      return favoriteIds;
    } catch (e) {
      Log.error('❌ Get favorite hotels error: $e');
      // fallback للبيانات المحلية
      return _getLocalFavorites();
    }
  }

  /// 📱 **الحصول على المفضلات من التخزين المحلي**
  List<String> _getLocalFavorites() {
    try {
      final localFavorites = UserRepo.local.getFavorites();
      final localIds = localFavorites
          .map((hotel) => hotel.code ?? '')
          .where((code) => code.isNotEmpty)
          .toList();
      Log.debug('✅ Loaded ${localIds.length} favorites from local storage');
      return localIds;
    } catch (localError) {
      Log.error('❌ Local favorites error: $localError');
      return [];
    }
  }

  /// ➕ **إضافة فندق للمفضلة**
  Future<bool> addToFavorites(String hotelId) async {
    try {
      Log.debug('➕ Adding hotel to favorites: $hotelId');

      // استخدام النظام الحقيقي لإضافة المفضلة
      final userRepo = UserRepo.to;

      // إنشاء HotelData مؤقت للإضافة
      final hotelData = HotelData(hotelCode: hotelId);
      await userRepo.addFavorite(hotelData);

      Log.debug('✅ Hotel added to favorites successfully');
      return true;
    } catch (e) {
      Log.error('❌ Add to favorites error: $e');
      return false;
    }
  }

  /// ➖ **إزالة فندق من المفضلة**
  Future<bool> removeFromFavorites(String hotelId) async {
    try {
      Log.debug('➖ Removing hotel from favorites: $hotelId');

      // استخدام النظام الحقيقي لإزالة المفضلة
      final userRepo = UserRepo.to;
      await userRepo.removeFavorite(hotelId);

      Log.debug('✅ Hotel removed from favorites successfully');
      return true;
    } catch (e) {
      Log.error('❌ Remove from favorites error: $e');
      return false;
    }
  }

  // ==================== Stats APIs ====================

  /// 📊 **الحصول على إحصائيات المستخدم**
  Future<UserStatsPlus?> getUserStats() async {
    try {
      Log.debug('📊 Fetching user stats');

      // محاكاة تحميل الإحصائيات للاختبار
      await Future.delayed(const Duration(milliseconds: 500));

      Log.debug('✅ User stats fetched successfully');
      return UserStatsPlus(
        totalBookings: 15,
        completedBookings: 12,
        cancelledBookings: 3,
        totalSpent: 2500.0,
        favoriteHotelsCount: 8,
        reviewsCount: 10,
        averageRating: 4.5,
        membershipLevel: 'Gold',
        loyaltyPoints: 1250,
      );
    } catch (e) {
      Log.error('❌ Get user stats error: $e');
      return null;
    }
  }
}
