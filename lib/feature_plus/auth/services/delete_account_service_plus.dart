import 'package:get/get.dart';
import '../../../core/network_provider/networking.dart';
import '../models/delete_account_models_plus.dart';

/// خدمة حذف الحساب
class DeleteAccountServicePlus extends GetxService {
  static DeleteAccountServicePlus get instance => Get.find<DeleteAccountServicePlus>();
  
  final NetworkHandler _networkHandler = NetworkHandler();

  /// إرسال OTP لحذف الحساب
  Future<SendOtpForDeletionResponse> sendOtpForAccountDeletion(String phone) async {
    try {
      print('📱 [DELETE_ACCOUNT_SERVICE] Sending OTP for account deletion: $phone');

      final response = await _networkHandler.post(
        ObjectResponse(),
        'auth/send-otp-for-account-deletion',
        body: {
          'phone': phone,
        },
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;
        
        if (responseData['success'] == true) {
          print('✅ [DELETE_ACCOUNT_SERVICE] OTP sent successfully');
          return SendOtpForDeletionResponse.fromJson(responseData);
        } else {
          print('❌ [DELETE_ACCOUNT_SERVICE] Failed to send OTP: ${responseData['message']}');
          return SendOtpForDeletionResponse(
            success: false,
            message: responseData['message'] ?? 'Failed to send OTP',
            error: responseData['error'],
          );
        }
      }
      
      print('❌ [DELETE_ACCOUNT_SERVICE] Request failed');
      return const SendOtpForDeletionResponse(
        success: false,
        message: 'Request failed',
      );
    } catch (e) {
      print('❌ [DELETE_ACCOUNT_SERVICE] Error sending OTP: $e');
      return SendOtpForDeletionResponse(
        success: false,
        message: 'An error occurred: $e',
      );
    }
  }

  /// تأكيد حذف الحساب بـ OTP
  Future<DeleteAccountResponse> deleteAccount({
    required String phone,
    required String otp,
    String? reason,
  }) async {
    try {
      print('🗑️ [DELETE_ACCOUNT_SERVICE] Deleting account for: $phone');

      final response = await _networkHandler.delete(
        ObjectResponse(),
        'auth/account',
        body: {
          'phone': phone,
          'otp': otp,
          'reason': reason ?? '',
        },
        dioType: DioType.api,
      );

      if (response.isRequestSuccess && response.body != null) {
        final responseData = response.body!.toMap;
        
        if (responseData['success'] == true) {
          print('✅ [DELETE_ACCOUNT_SERVICE] Account deleted successfully');
          return DeleteAccountResponse.fromJson(responseData);
        } else {
          print('❌ [DELETE_ACCOUNT_SERVICE] Failed to delete account: ${responseData['message']}');
          return DeleteAccountResponse(
            success: false,
            message: responseData['message'] ?? 'Failed to delete account',
            error: responseData['error'],
          );
        }
      }
      
      print('❌ [DELETE_ACCOUNT_SERVICE] Request failed');
      return const DeleteAccountResponse(
        success: false,
        message: 'Request failed',
      );
    } catch (e) {
      print('❌ [DELETE_ACCOUNT_SERVICE] Error deleting account: $e');
      return DeleteAccountResponse(
        success: false,
        message: 'An error occurred: $e',
      );
    }
  }
}
