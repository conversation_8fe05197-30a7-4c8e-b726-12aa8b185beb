import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/delete_account_controller_plus.dart';
import '../models/delete_account_models_plus.dart';
import '../../../core/theme/color_manager.dart';

/// صفحة حذف الحساب
class DeleteAccountScreenPlus extends StatelessWidget {
  const DeleteAccountScreenPlus({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DeleteAccountControllerPlus>(
      init: DeleteAccountControllerPlus(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.grey[50],
          appBar: _buildAppBar(),
          body: _buildBody(controller),
        );
      },
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0,
      title: Text(
        'deleteAccount.title'.tr,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      centerTitle: true,
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody(DeleteAccountControllerPlus controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تحذير حذف الحساب
          _buildWarningCard(),
          const SizedBox(height: 24),
          
          // نموذج حذف الحساب
          _buildDeleteForm(controller),
        ],
      ),
    );
  }

  /// بطاقة التحذير
  Widget _buildWarningCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.red[600],
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'deleteAccount.warningTitle'.tr,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'deleteAccount.warningMessage'.tr,
            style: TextStyle(
              fontSize: 14,
              color: Colors.red[600],
              height: 1.5,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'deleteAccount.warningConsequences'.tr,
            style: TextStyle(
              fontSize: 14,
              color: Colors.red[600],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// نموذج حذف الحساب
  Widget _buildDeleteForm(DeleteAccountControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'deleteAccount.formTitle'.tr,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 24),
          
          // حقل رقم الهاتف
          _buildPhoneField(controller),
          const SizedBox(height: 20),
          
          // حقل OTP (يظهر بعد إرسال OTP)
          Obx(() {
            if (controller.otpSent) {
              return Column(
                children: [
                  _buildOtpField(controller),
                  const SizedBox(height: 16),
                  _buildOtpTimer(controller),
                  const SizedBox(height: 20),
                ],
              );
            }
            return const SizedBox.shrink();
          }),
          
          // اختيار سبب الحذف
          Obx(() {
            if (controller.otpSent) {
              return Column(
                children: [
                  _buildReasonSelection(controller),
                  const SizedBox(height: 24),
                ],
              );
            }
            return const SizedBox.shrink();
          }),
          
          // أزرار الإجراءات
          _buildActionButtons(controller),
        ],
      ),
    );
  }

  /// حقل رقم الهاتف
  Widget _buildPhoneField(DeleteAccountControllerPlus controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'deleteAccount.phoneNumber'.tr,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller.phoneController,
          keyboardType: TextInputType.phone,
          enabled: !controller.otpSent,
          decoration: InputDecoration(
            hintText: 'deleteAccount.phoneHint'.tr,
            prefixIcon: const Icon(Icons.phone),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ColorManager.primary),
            ),
            filled: true,
            fillColor: controller.otpSent ? Colors.grey[100] : Colors.white,
          ),
        ),
      ],
    );
  }

  /// حقل OTP
  Widget _buildOtpField(DeleteAccountControllerPlus controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'deleteAccount.otpCode'.tr,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller.otpController,
          keyboardType: TextInputType.number,
          maxLength: 6,
          decoration: InputDecoration(
            hintText: 'deleteAccount.otpHint'.tr,
            prefixIcon: const Icon(Icons.security),
            counterText: '',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ColorManager.primary),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ],
    );
  }

  /// مؤقت OTP
  Widget _buildOtpTimer(DeleteAccountControllerPlus controller) {
    return Obx(() {
      final remainingSeconds = controller.remainingSeconds;
      final canResend = controller.canResendOtp;
      
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            canResend 
                ? 'deleteAccount.otpExpired'.tr
                : 'deleteAccount.otpExpiresIn'.tr.replaceAll(
                    '{seconds}', 
                    '${remainingSeconds ~/ 60}:${(remainingSeconds % 60).toString().padLeft(2, '0')}'
                  ),
            style: TextStyle(
              fontSize: 14,
              color: canResend ? Colors.red : Colors.grey[600],
            ),
          ),
          if (canResend)
            TextButton(
              onPressed: controller.resendOtp,
              child: Text(
                'deleteAccount.resendOtp'.tr,
                style: TextStyle(
                  color: ColorManager.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      );
    });
  }

  /// اختيار سبب الحذف
  Widget _buildReasonSelection(DeleteAccountControllerPlus controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'deleteAccount.reasonTitle'.tr,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        
        // قائمة الأسباب
        ...DeleteAccountReason.values.map((reason) {
          return Obx(() => RadioListTile<DeleteAccountReason>(
            title: Text(reason.displayName),
            value: reason,
            groupValue: controller.selectedReason,
            onChanged: (value) {
              if (value != null) {
                controller.setSelectedReason(value);
              }
            },
            activeColor: ColorManager.primary,
            contentPadding: EdgeInsets.zero,
          ));
        }),
        
        // حقل السبب المخصص
        Obx(() {
          if (controller.selectedReason == DeleteAccountReason.other) {
            return Padding(
              padding: const EdgeInsets.only(top: 12),
              child: TextFormField(
                controller: controller.reasonController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'deleteAccount.customReasonHint'.tr,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: ColorManager.primary),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  /// أزرار الإجراءات
  Widget _buildActionButtons(DeleteAccountControllerPlus controller) {
    return Obx(() {
      if (!controller.otpSent) {
        // زر إرسال OTP
        return SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: controller.isLoadingSendOtp 
                ? null 
                : controller.sendOtpForDeletion,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: controller.isLoadingSendOtp
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'deleteAccount.sendOtp'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
          ),
        );
      } else {
        // أزرار تأكيد الحذف والإلغاء
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: controller.isLoadingDeleteAccount 
                    ? null 
                    : controller.confirmDeleteAccount,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: controller.isLoadingDeleteAccount
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'deleteAccount.confirmDelete'.tr,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: OutlinedButton(
                onPressed: () => Get.back(),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey[400]!),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'common.cancel'.tr,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ],
        );
      }
    });
  }
}
