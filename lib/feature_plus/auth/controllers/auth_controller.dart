import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/cache/preference_manager.dart';
import '../../../core/core.dart';
import '../../../core/controllers/user-app/user_controller.dart';
import '../../user/controllers/user_controller_plus.dart';
import '../models/auth_models.dart';
import '../models/user_model.dart';
import '../routes/auth_routes.dart';
import '../services/auth_service.dart';

/// Authentication controller for phone-based login with OTP
class AuthPlusController extends GetxController {

  static AuthPlusController get to => Get.find<AuthPlusController>();

  final AuthService _authService = AuthService();

  // Reactive variables
  final Rx<AuthState> _authState = AuthState.initial.obs;
  final RxString _phoneNumber = ''.obs;
  final RxString _otp = ''.obs;
  final RxString _maskedPhone = ''.obs;
  final RxInt _otpExpiresIn = 0.obs;
  final RxInt _remainingTime = 0.obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingVerifyOtp = false.obs;
  final RxString _errorMessage = ''.obs;
  final Rx<AuthUser?> _currentUser = Rx<AuthUser?>(null);
  final RxString _authToken = ''.obs;
  final Rx<CountryCode?> _selectedCountryCode = Rx<CountryCode?>(null);

  // Timer for OTP countdown
  Timer? _otpTimer;

  // Text controllers
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController otpController = TextEditingController();

  // Getters
  AuthState get authState => _authState.value;
  String get phoneNumber => _phoneNumber.value;
  String get otp => _otp.value;
  String get maskedPhone => _maskedPhone.value;
  int get otpExpiresIn => _otpExpiresIn.value;
  int get remainingTime => _remainingTime.value;
  bool get isLoading => _isLoading.value;
  bool get isLoadingVerifyOtp => _isLoadingVerifyOtp.value;
  String get errorMessage => _errorMessage.value;
  AuthUser? get currentUser => _currentUser.value;
  String get authToken => _authToken.value;
  bool get isAuthenticated =>
      _currentUser.value != null && _authToken.value.isNotEmpty;
  Rx<CountryCode?> get selectedCountryCode => _selectedCountryCode;

  @override
  void onInit() {
    super.onInit();
    _initializeCountryCode();
    _loadSavedAuth();
    print('🔐 AuthPlusController initialized');
  }

  /// Initialize default country code
  void _initializeCountryCode() {
    // Set default country code to Saudi Arabia
    _selectedCountryCode.value = CountryCode.fromCountryCode("SA");
  }

  @override
  void onClose() {
    _otpTimer?.cancel();
    phoneController.dispose();
    otpController.dispose();
    super.onClose();
  }

  /// Load saved authentication data
  Future<void> _loadSavedAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedToken = prefs.getString('auth_plus_token');
      final savedUserJson = prefs.getString('auth_plus_user');

      if (savedToken != null && savedUserJson != null) {
        try {
          // Parse the saved user data
          final userMap = jsonDecode(savedUserJson);
          final savedUser = AuthUser.fromJson(userMap);

          // Validate token with server
          final isValidToken = await _validateTokenWithServer(savedToken);

          if (isValidToken) {
            // Set the authentication state
            _authToken.value = savedToken;
            _currentUser.value = savedUser;
            _authState.value = AuthState.authenticated;

            // Integrate with existing user system
            await _integrateWithExistingUserSystem(savedUser, savedToken);

            print('✅ Loaded saved authentication - User: ${savedUser.phone}');
          } else {
            print('❌ Saved token is invalid, clearing auth data');
            await _clearAuth();
          }
        } catch (parseError) {
          print('❌ Error parsing saved user data: $parseError');
          // Clear invalid data
          await _clearAuth();
        }
      } else {
        // Check if there's authentication in PreferenceManager (existing system)
        final existingToken = PreferenceManager.instance.authToken;
        final existingUser = PreferenceManager.instance.userData();

        if (existingToken.isNotEmpty && existingUser != null) {
          // Convert existing user to AuthUser and save to new system
          final authUser = AuthUser(
            id: existingUser.id.toString(),
            phone: existingUser.phone ?? '',
            firstName: existingUser.fName ?? '',
            lastName: existingUser.lName ?? '',
            isPhoneVerified: existingUser.phoneVerified ?? false,
            preferredLanguage: existingUser.preferredLanguage ?? 'en',
            preferredCurrency: existingUser.preferredCurrency ?? 'USD',
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
          );

          _authToken.value = existingToken;
          _currentUser.value = authUser;
          _authState.value = AuthState.authenticated;

          // Save to new system for consistency
          await _saveAuth(existingToken, authUser);

          print(
              '✅ Migrated existing authentication to new system - User: ${authUser.phone}');
        } else {
          print('📱 No saved authentication found in either system');
        }
      }
    } catch (e) {
      print('❌ Error loading saved auth: $e');
    }
  }

  /// Save authentication data
  Future<void> _saveAuth(String token, AuthUser user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_plus_token', token);
      await prefs.setString('auth_plus_user', jsonEncode(user.toJson()));
      print('✅ Authentication data saved');
    } catch (e) {
      print('❌ Error saving auth: $e');
    }
  }

  /// Validate token with server
  Future<bool> _validateTokenWithServer(String token) async {
    try {
      // For now, just check if token is not empty and has reasonable length
      // In production, you would make an API call to validate the token
      if (token.isEmpty || token.length < 10) {
        return false;
      }

      // TODO: Add actual server validation
      // final response = await _authService.validateToken(token);
      // return response.success;

      return true; // Assume valid for now
    } catch (e) {
      print('❌ Error validating token: $e');
      return false;
    }
  }

  /// Clear authentication data
  Future<void> _clearAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_plus_token');
      await prefs.remove('auth_plus_user');
      print('✅ Authentication data cleared');
    } catch (e) {
      print('❌ Error clearing auth: $e');
    }
  }

  /// Update country code
  void updateCountryCode(CountryCode countryCode) {
    _selectedCountryCode.value = countryCode;
    // Clear phone number when country changes
    _phoneNumber.value = '';
    phoneController.clear();
    _clearError();
  }

  /// Update phone number
  void updatePhoneNumber(String phone) {
    _phoneNumber.value = phone;
    phoneController.text = phone;
    _clearError();
  }

  /// Update OTP
  void updateOtp(String otp) {
    _otp.value = otp;
    otpController.text = otp;
    _clearError();
  }

  /// Clear error message
  void _clearError() {
    _errorMessage.value = '';
  }

  /// Set error message
  void _setError(String message) {
    _errorMessage.value = message;
    _authState.value = AuthState.error;
  }

  /// Send OTP to phone number
  Future<void> sendOtp() async {
    try {
      _clearError();

      // Validate phone number
      if (_phoneNumber.value.isEmpty) {
        _setError('يرجى إدخال رقم الهاتف');
        return;
      }

      if (!_authService.isValidPhoneNumber(_phoneNumber.value)) {
        _setError('رقم الهاتف غير صحيح');
        return;
      }

      _isLoading.value = true;
      _authState.value = AuthState.sendingOtp;

      // Format phone number with selected country code
      final dialCode = _selectedCountryCode.value?.dialCode ?? '+966';
      String originalPhone = _phoneNumber.value;

      print('🔧 DEBUG: Starting phone formatting...');
      print('🔧 DEBUG: originalPhone="$originalPhone"');
      print('🔧 DEBUG: dialCode="$dialCode"');

      // Clean the phone number (remove any existing country codes, spaces, etc.)
      String cleanPhone = originalPhone.replaceAll(RegExp(r'[^\d]'), '');
      print('🔧 DEBUG: cleanPhone after removing non-digits="$cleanPhone"');

      // Remove leading zeros and invalid prefixes
      if (cleanPhone.startsWith('0')) {
        cleanPhone = cleanPhone.substring(1);
        print('🔧 DEBUG: cleanPhone after removing leading zero="$cleanPhone"');
      }

      // For Saudi numbers, remove leading '1' if present (common mistake)
      if (dialCode == '+966' &&
          cleanPhone.startsWith('1') &&
          cleanPhone.length == 10) {
        cleanPhone = cleanPhone.substring(1);
        print(
            '🔧 DEBUG: cleanPhone after removing leading 1 for Saudi="$cleanPhone"');
      }

      // Check if the original phone already contains the country code
      String formattedPhone;
      if (originalPhone.startsWith(dialCode)) {
        // Phone already has the correct country code
        formattedPhone = originalPhone;
        print('🔧 DEBUG: Phone already has correct country code, using as-is');
      } else if (originalPhone.startsWith('+')) {
        // Phone has a different country code, use as-is
        formattedPhone = originalPhone;
        print('🔧 DEBUG: Phone has different country code, using as-is');
      } else {
        // Add the country code
        formattedPhone = '$dialCode$cleanPhone';
        print('🔧 DEBUG: Adding country code to clean phone');
      }

      print('🔧 DEBUG: Final formattedPhone="$formattedPhone"');

      // Send OTP
      final response = await _authService.sendOtp(phone: formattedPhone);

      if (response.success) {
        _maskedPhone.value = response.maskedPhone;
        _otpExpiresIn.value = response.expiresInMinutes;
        _authState.value = AuthState.otpSent;

        // Start countdown timer
        _startOtpTimer();

        Core.showGlobalSnackBar(
          response.message,
          backgroundColor: Colors.green,
        );

        var result = await AuthNavigator.navigateToVerifyOtpLogin();

        if(result != null){
          Get.back(result: result);
        }

      } else {
        _setError(response.message);
        Core.showGlobalSnackBar(
          response.message,
          backgroundColor: Colors.red,
        );
      }
    } catch (e) {
      _setError('خطأ في إرسال رمز التحقق: $e');
      Core.showGlobalSnackBar(
        'خطأ في إرسال رمز التحقق',
        backgroundColor: Colors.red,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Verify OTP and complete authentication
  Future<void> verifyOtp() async {
    try {
      _clearError();

      // Validate OTP
      if (_otp.value.isEmpty) {
        _setError('يرجى إدخال رمز التحقق');
        return;
      }

      if (_otp.value.length < 4) {
        _setError('رمز التحقق غير صحيح');
        return;
      }

      _isLoadingVerifyOtp.value = true;
      _authState.value = AuthState.verifyingOtp;

      // Format phone number same way as in sendOtp
      String originalPhone = _phoneNumber.value;
      String dialCode = _selectedCountryCode.value?.dialCode ?? '+966';

      print('🔧 DEBUG: Verifying OTP...');
      print('🔧 DEBUG: originalPhone="$originalPhone"');
      print('🔧 DEBUG: dialCode="$dialCode"');
      print('🔧 DEBUG: otp="$_otp.value"');

      // Clean the phone number (same logic as sendOtp)
      String cleanPhone = originalPhone.replaceAll(RegExp(r'[^\d]'), '');
      print('🔧 DEBUG: cleanPhone after removing non-digits="$cleanPhone"');

      // Remove leading zeros and invalid prefixes
      if (cleanPhone.startsWith('0')) {
        cleanPhone = cleanPhone.substring(1);
        print('🔧 DEBUG: cleanPhone after removing leading zero="$cleanPhone"');
      }

      // For Saudi numbers, remove leading '1' if present (common mistake)
      if (dialCode == '+966' &&
          cleanPhone.startsWith('1') &&
          cleanPhone.length == 10) {
        cleanPhone = cleanPhone.substring(1);
        print(
            '🔧 DEBUG: cleanPhone after removing leading 1 for Saudi="$cleanPhone"');
      }

      // Check if the original phone already contains the country code
      String formattedPhone;
      if (originalPhone.startsWith(dialCode)) {
        // Phone already has the correct country code
        formattedPhone = originalPhone;
        print('🔧 DEBUG: Phone already has correct country code, using as-is');
      } else if (originalPhone.startsWith('+')) {
        // Phone has a different country code, use as-is
        formattedPhone = originalPhone;
        print('🔧 DEBUG: Phone has different country code, using as-is');
      } else {
        // Add the country code
        formattedPhone = '$dialCode$cleanPhone';
        print('🔧 DEBUG: Adding country code to clean phone');
      }

      print(
          '🔧 DEBUG: Final formattedPhone for verification="$formattedPhone"');

      // Verify OTP
      final response = await _authService.verifyOtp(
        phone: formattedPhone,
        otp: _otp.value,
      );

      if (response.success && response.user != null && response.token != null) {
        _currentUser.value = response.user;
        _authToken.value = response.token!;
        _authState.value = AuthState.authenticated;

        // Save authentication data
        await _saveAuth(response.token!, response.user!);

        // Integrate with existing user system
        await _integrateWithExistingUserSystem(response.user!, response.token!);

        // Stop timer
        _otpTimer?.cancel();

        Core.showGlobalSnackBar(
          response.message,
          backgroundColor: Colors.green,
        );
        // Navigate back or to home
        Get.back(result: true);
      } else {
        _setError(response.message);
        Core.showGlobalSnackBar(
          response.message,
          backgroundColor: Colors.red,
        );
      }
    } catch (e) {
      _setError('خطأ في التحقق من الرمز: $e');
      Core.showGlobalSnackBar(
        'خطأ في التحقق من الرمز',
        backgroundColor: Colors.red,
      );
    } finally {
      _isLoadingVerifyOtp.value = false;
    }
  }

  /// Integrate with existing user system
  Future<void> _integrateWithExistingUserSystem(
      AuthUser authUser, String token) async {
    try {
      // Convert AuthUser to existing UserModel
      final userModel = UserModel(
        id: int.tryParse(authUser.id),
        phone: authUser.phone.replaceAll('+', ''),
        dialCode: _authService.getCountryCode(authUser.phone),
        fName: authUser.firstName,
        lName: authUser.lastName,
        phoneVerified: authUser.isPhoneVerified,
        preferredLanguage: authUser.preferredLanguage,
        preferredCurrency: authUser.preferredCurrency,
        token: token,
      );

      // Save to PreferenceManager directly (this is what the existing system uses)
      await PreferenceManager.instance.saveAuthToken(token);
      await PreferenceManager.instance.saveUserModel(userModel);
      await PreferenceManager.instance.saveIsLoggedIn(true);

      // Use existing UserController to login
      final userController = UserController.to;
      await userController.login(userModel);

      // Also notify UserControllerPlus if available
      try {
        if (Get.isRegistered<UserControllerPlus>()) {
          final userControllerPlus = Get.find<UserControllerPlus>();
          // Trigger update to refresh authentication state
          userControllerPlus.update();
        }
      } catch (e) {
        print('⚠️ UserControllerPlus not available for update: $e');
      }

      print(
          '✅ Integrated with existing user system - Token saved to PreferenceManager');
    } catch (e) {
      print('❌ Error integrating with existing user system: $e');
    }
  }

  /// Start OTP countdown timer
  void _startOtpTimer() {
    _remainingTime.value =
        _otpExpiresIn.value * 60; // Convert minutes to seconds

    _otpTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime.value > 0) {
        _remainingTime.value--;
      } else {
        timer.cancel();
      }
    });
  }

  /// Resend OTP
  Future<void> resendOtp() async {
    _otpTimer?.cancel();
    await sendOtp();
  }

  /// Logout
  Future<void> logout() async {
    try {
      _currentUser.value = null;
      _authToken.value = '';
      _authState.value = AuthState.initial;
      _phoneNumber.value = '';
      _otp.value = '';
      _maskedPhone.value = '';
      _otpExpiresIn.value = 0;
      _remainingTime.value = 0;
      _errorMessage.value = '';

      phoneController.clear();
      otpController.clear();

      _otpTimer?.cancel();

      await _clearAuth();

      // Clear PreferenceManager data
      await PreferenceManager.instance.logout();

      // Logout from existing user system
      final userController = UserController.to;
      await userController.logOutWithoutMessage();

      // مسح بيانات UserControllerPlus إذا كان متاحاً (بدون استدعاء logout لتجنب الحلقة)
      try {
        if (Get.isRegistered<UserControllerPlus>()) {
          final userControllerPlus = Get.find<UserControllerPlus>();
          // مسح البيانات فقط بدون استدعاء logout
          userControllerPlus.clearUserData();
        }
      } catch (e) {
        print('⚠️ UserControllerPlus not available for logout: $e');
      }

      print('✅ Logged out successfully');
    } catch (e) {
      print('❌ Error during logout: $e');
    }
  }

  /// Reset authentication state
  void reset() {
    _authState.value = AuthState.initial;
    _phoneNumber.value = '';
    _otp.value = '';
    _maskedPhone.value = '';
    _otpExpiresIn.value = 0;
    _remainingTime.value = 0;
    _errorMessage.value = '';
    _isLoading.value = false;

    phoneController.clear();
    otpController.clear();

    _otpTimer?.cancel();
  }

  /// Format remaining time as MM:SS
  String get formattedRemainingTime {
    final minutes = _remainingTime.value ~/ 60;
    final seconds = _remainingTime.value % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Check if OTP can be resent
  bool get canResendOtp =>
      _remainingTime.value == 0 && _authState.value == AuthState.otpSent;
}
