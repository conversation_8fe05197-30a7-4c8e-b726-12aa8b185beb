import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/delete_account_service_plus.dart';
import '../models/delete_account_models_plus.dart';
import '../../user/controllers/user_controller_plus.dart';
import 'dart:async';

/// كونترولر حذف الحساب
class DeleteAccountControllerPlus extends GetxController {
  static DeleteAccountControllerPlus get instance =>
      Get.find<DeleteAccountControllerPlus>();

  late final DeleteAccountServicePlus _deleteAccountService;
  late final UserControllerPlus _userController;

  // Form controllers
  final phoneController = TextEditingController();
  final otpController = TextEditingController();
  final reasonController = TextEditingController();

  // حالات التحميل
  final RxBool _isLoadingSendOtp = false.obs;
  final RxBool _isLoadingDeleteAccount = false.obs;
  final RxBool _otpSent = false.obs;

  // بيانات OTP
  final RxString _maskedPhone = ''.obs;
  final RxInt _expiresInMinutes = 5.obs;
  final RxInt _remainingSeconds = 0.obs;

  // السبب المختار
  final Rx<DeleteAccountReason> _selectedReason = DeleteAccountReason.other.obs;

  // Getters
  bool get isLoadingSendOtp => _isLoadingSendOtp.value;
  bool get isLoadingDeleteAccount => _isLoadingDeleteAccount.value;
  bool get otpSent => _otpSent.value;
  String get maskedPhone => _maskedPhone.value;
  int get expiresInMinutes => _expiresInMinutes.value;
  int get remainingSeconds => _remainingSeconds.value;
  DeleteAccountReason get selectedReason => _selectedReason.value;

  bool get canResendOtp => _remainingSeconds.value == 0;
  bool get isOtpExpired => _remainingSeconds.value == 0 && _otpSent.value;

  @override
  void onInit() {
    super.onInit();
    print(
        '🚀 [DELETE_ACCOUNT_CONTROLLER] DeleteAccountControllerPlus initialized');
    _initializeDependencies();
    _initializePhoneNumber();
  }

  @override
  void onClose() {
    phoneController.dispose();
    otpController.dispose();
    reasonController.dispose();
    super.onClose();
  }

  /// تهيئة التبعيات
  void _initializeDependencies() {
    try {
      _deleteAccountService = Get.find<DeleteAccountServicePlus>();
      _userController = Get.find<UserControllerPlus>();
      print('✅ [DELETE_ACCOUNT_CONTROLLER] Dependencies found');
    } catch (e) {
      print('❌ [DELETE_ACCOUNT_CONTROLLER] Error finding dependencies: $e');
      // تسجيل الخدمات إذا لم تكن موجودة
      if (!Get.isRegistered<DeleteAccountServicePlus>()) {
        Get.put(DeleteAccountServicePlus());
      }
      _deleteAccountService = Get.find<DeleteAccountServicePlus>();
      _userController = Get.find<UserControllerPlus>();
    }
  }

  /// تهيئة رقم الهاتف من المستخدم الحالي
  void _initializePhoneNumber() {
    final currentUser = _userController.userProfile;
    if (currentUser?.phoneNumber != null) {
      phoneController.text = currentUser!.phoneNumber;
    }
  }

  /// إرسال OTP لحذف الحساب
  Future<void> sendOtpForDeletion() async {
    if (_isLoadingSendOtp.value) return;

    final phone = phoneController.text.trim();
    if (phone.isEmpty) {
      Get.snackbar(
        'deleteAccount.error'.tr,
        'deleteAccount.phoneRequired'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      _isLoadingSendOtp.value = true;
      print('📱 [DELETE_ACCOUNT_CONTROLLER] Sending OTP for deletion...');

      final response =
          await _deleteAccountService.sendOtpForAccountDeletion(phone);

      if (response.success) {
        _otpSent.value = true;
        _maskedPhone.value = response.maskedPhone ?? phone;
        _expiresInMinutes.value = response.expiresInMinutes ?? 5;
        _startCountdown();

        Get.snackbar(
          'deleteAccount.success'.tr,
          response.message,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        print('✅ [DELETE_ACCOUNT_CONTROLLER] OTP sent successfully');
      } else {
        Get.snackbar(
          'deleteAccount.error'.tr,
          response.message,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        print(
            '❌ [DELETE_ACCOUNT_CONTROLLER] Failed to send OTP: ${response.message}');
      }
    } catch (e) {
      Get.snackbar(
        'deleteAccount.error'.tr,
        'deleteAccount.unexpectedError'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      print('❌ [DELETE_ACCOUNT_CONTROLLER] Error sending OTP: $e');
    } finally {
      _isLoadingSendOtp.value = false;
    }
  }

  /// تأكيد حذف الحساب
  Future<void> confirmDeleteAccount() async {
    if (_isLoadingDeleteAccount.value) return;

    final phone = phoneController.text.trim();
    final otp = otpController.text.trim();

    if (phone.isEmpty || otp.isEmpty) {
      Get.snackbar(
        'deleteAccount.error'.tr,
        'deleteAccount.fillAllFields'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // تأكيد الحذف
    final confirmed = await _showDeleteConfirmationDialog();
    if (!confirmed) return;

    try {
      _isLoadingDeleteAccount.value = true;
      print('🗑️ [DELETE_ACCOUNT_CONTROLLER] Confirming account deletion...');

      final reason = _selectedReason.value != DeleteAccountReason.other
          ? _selectedReason.value.value
          : reasonController.text.trim();

      final response = await _deleteAccountService.deleteAccount(
        phone: phone,
        otp: otp,
        reason: reason,
      );

      if (response.success) {
        // عرض رسالة النجاح
        Get.snackbar(
          'deleteAccount.success'.tr,
          response.message,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 5),
        );

        print('✅ [DELETE_ACCOUNT_CONTROLLER] Account deleted successfully');

        // تسجيل خروج المستخدم وإعادة توجيهه
        await _handleAccountDeleted();
      } else {
        Get.snackbar(
          'deleteAccount.error'.tr,
          response.message,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        print(
            '❌ [DELETE_ACCOUNT_CONTROLLER] Failed to delete account: ${response.message}');
      }
    } catch (e) {
      Get.snackbar(
        'deleteAccount.error'.tr,
        'deleteAccount.unexpectedError'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      print('❌ [DELETE_ACCOUNT_CONTROLLER] Error deleting account: $e');
    } finally {
      _isLoadingDeleteAccount.value = false;
    }
  }

  /// تغيير السبب المختار
  void setSelectedReason(DeleteAccountReason reason) {
    _selectedReason.value = reason;
    print('📝 [DELETE_ACCOUNT_CONTROLLER] Selected reason: ${reason.value}');
  }

  /// بدء العد التنازلي لـ OTP
  void _startCountdown() {
    _remainingSeconds.value = _expiresInMinutes.value * 60;

    // العد التنازلي كل ثانية
    Stream.periodic(const Duration(seconds: 1), (i) => i)
        .take(_remainingSeconds.value)
        .listen((i) {
      _remainingSeconds.value = (_expiresInMinutes.value * 60) - i - 1;
      if (_remainingSeconds.value <= 0) {
        _remainingSeconds.value = 0;
      }
    });
  }

  /// إعادة إرسال OTP
  Future<void> resendOtp() async {
    if (!canResendOtp) return;

    otpController.clear();
    _otpSent.value = false;
    await sendOtpForDeletion();
  }

  /// عرض حوار تأكيد الحذف
  Future<bool> _showDeleteConfirmationDialog() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: Text('deleteAccount.confirmTitle'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('deleteAccount.confirmMessage'.tr),
            const SizedBox(height: 16),
            Text(
              'deleteAccount.confirmWarning'.tr,
              style: const TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text('common.cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(
              'deleteAccount.confirmDelete'.tr,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// معالجة حذف الحساب بنجاح
  Future<void> _handleAccountDeleted() async {
    try {
      // تسجيل خروج المستخدم
      await _userController.logout();

      // الانتقال لصفحة البداية
      Get.offAllNamed('/splash');

      print('✅ [DELETE_ACCOUNT_CONTROLLER] User logged out and redirected');
    } catch (e) {
      print(
          '❌ [DELETE_ACCOUNT_CONTROLLER] Error handling account deletion: $e');
      // في حالة الخطأ، انتقل للصفحة الرئيسية
      Get.offAllNamed('/');
    }
  }

  /// مسح البيانات
  void clearData() {
    phoneController.clear();
    otpController.clear();
    reasonController.clear();
    _otpSent.value = false;
    _maskedPhone.value = '';
    _remainingSeconds.value = 0;
    _selectedReason.value = DeleteAccountReason.other;
  }
}
