
import '../../../core/core.dart';
import '../../../core/router/app_pages.dart';
import '../views/auth_screen.dart';
import '../views/delete_account_screen_plus.dart';
import '../views/otp_verification_screen.dart';
import '../views/phone_input_screen.dart';

class AuthNavigator {

  static String get delete => Routes.DELETE_ACCOUNT;
  static String get verifyLoginOtp => Routes.VERIFY_OTP;
  static String get login => Routes.LOGIN;

  static List<GetPage> routes = [
    GetPage(
      name: delete,
      page: () => const DeleteAccountScreenPlus(),
    ),
    GetPage(
      name: login,
      page: () => const PhoneInputScreen(),
      binding: AuthPlusBinding(),
    ),
    GetPage(
      name: verifyLoginOtp,
      page: () => const OtpVerificationScreen(),
      binding: AuthPlusBinding(),
    ),
  ];

  static void navigateToDeleteAccount() {
    Get.toNamed(delete);
  }

  static navigateToLogin() async {
    return Get.toNamed(login);
  }

  static navigateToVerifyOtpLogin() async {
    return Get.toNamed(verifyLoginOtp);
  }

}