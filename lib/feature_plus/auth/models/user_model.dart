import 'package:fandooq/core/extensions/date.dart';

class UserModel {
  int? id;
  String? fName;
  String? lName;
  String? phone;
  String? dialCode;
  String? gender;
  String? birthDate;
  String? nationality;
  String? residenceCountry;
  String? maritalStatus;
  String? email;
  bool? emailVerified;
  bool? phoneVerified;
  String? photo;
  String? preferredLanguage;
  String? preferredCurrency;
  String? loyaltyNumber;
  String? token;
  List travelers = [];

  UserModel({
    this.id,
    this.fName,
    this.lName,
    this.phone,
    this.dialCode,
    this.gender,
    this.birthDate,
    this.nationality,
    this.residenceCountry,
    this.maritalStatus,
    this.email,
    this.emailVerified,
    this.phoneVerified,
    this.photo,
    this.preferredLanguage,
    this.preferredCurrency,
    this.loyaltyNumber,
    this.token,
    this.travelers = const [],
  });

  String get fullName => '$fName $lName';

  bool get isNameExist => fullName.trim().isNotEmpty;

  bool get isPhotoExist => photo != null && photo!.isNotEmpty;

  bool get isEmailExist => email != null && email!.isNotEmpty;

  bool get isPhoneExist => phone != null && phone!.isNotEmpty;

  String? get birthDateFormatted {
    String? formattedDate;
    if (birthDate != null && birthDate!.isNotEmpty) {
      formattedDate = birthDate!.formatDate;
    }
    return formattedDate;
  }

  factory UserModel.fromJson(dynamic json) {
    // استخراج country code من رقم الهاتف إذا كان متوفراً
    String? extractedDialCode;
    String? cleanPhone;

    if (json['phone'] != null) {
      String fullPhone = json['phone'].toString();
      if (fullPhone.startsWith('+')) {
        // استخراج country code من رقم الهاتف الكامل
        try {
          // البحث عن أول رقم غير متعلق بـ country code
          RegExp regExp = RegExp(r'^\+(\d{1,4})(.+)$');
          Match? match = regExp.firstMatch(fullPhone);
          if (match != null) {
            extractedDialCode = match.group(1);
            cleanPhone = match.group(2);
          } else {
            cleanPhone = fullPhone;
          }
        } catch (e) {
          cleanPhone = fullPhone;
        }
      } else {
        cleanPhone = fullPhone;
      }
    }

    return UserModel(
      id: json['id'],
      fName: json['firstName'],
      lName: json['lastName'],
      phone: cleanPhone ?? json['phone'],
      dialCode: json['countryCode'] ?? extractedDialCode,
      gender: json['gender'],
      birthDate: json['birthDate'],
      nationality: json['nationality'],
      residenceCountry: json['residenceCountry'],
      maritalStatus: json['maritalStatus'],
      email: json['email'],
      emailVerified: json['emailVerified'],
      phoneVerified: json['isPhoneVerified'] ??
          json['phoneVerified'], // الباك اند يستخدم isPhoneVerified
      photo: json['profilePictureUrl'],
      preferredLanguage: json['preferredLanguage'],
      preferredCurrency: json['preferredCurrency'],
      loyaltyNumber: json['loyaltyNumber'],
      travelers: json['travelers'] ?? [],
      token: json['token'],
    );
  }

  String get fullPhone {
    return "${dialCode?.trim()}${phone?.trim()}";
  }

  bool isNotNullOrEmpty(String? value) {
    return value != null && value.isNotEmpty;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firstName': fName,
      'lastName': lName,
      'phone': phone,
      'countryCode': dialCode,
      'gender': gender,
      'birthDate': birthDate,
      'nationality': nationality,
      'residenceCountry': residenceCountry,
      'maritalStatus': maritalStatus,
      'email': email,
      'emailVerified': emailVerified,
      'phoneVerified': phoneVerified,
      'photo': photo,
      'preferredLanguage': preferredLanguage,
      'loyaltyNumber': loyaltyNumber,
      'travelers': travelers,
      'token': token,
    };
  }

  Map<String, dynamic> toJson() {
    return {
      if (isNotNullOrEmpty(fName)) 'firstName': fName,
      if (isNotNullOrEmpty(lName)) 'lastName': lName,
      if (isNotNullOrEmpty(phone)) 'phone': phone,
      if (isNotNullOrEmpty(dialCode)) 'countryCode': dialCode,
      if (isNotNullOrEmpty(gender)) 'gender': gender,
      if (isNotNullOrEmpty(birthDate)) 'birthDate': birthDate,
      if (isNotNullOrEmpty(nationality)) 'nationality': nationality,
      if (isNotNullOrEmpty(residenceCountry))
        'residenceCountry': residenceCountry,
      if (isNotNullOrEmpty(maritalStatus)) 'maritalStatus': maritalStatus,
      if (isNotNullOrEmpty(email)) 'email': email,
      if (emailVerified != null) 'emailVerified': emailVerified,
      if (phoneVerified != null) 'phoneVerified': phoneVerified,
      if (isNotNullOrEmpty(photo)) 'profilePictureUrl': photo,
      if (isNotNullOrEmpty(preferredLanguage))
        'preferredLanguage': preferredLanguage,
      if (isNotNullOrEmpty(preferredCurrency))
        'preferredCurrency': preferredCurrency,
      if (isNotNullOrEmpty(loyaltyNumber)) 'loyaltyNumber': loyaltyNumber,
      if (travelers.isNotEmpty) 'travelers': travelers,
      if (token != null) 'token': token,
    };
  }

  UserModel copyWith({
    int? id,
    String? fName,
    String? lName,
    String? phone,
    String? code,
    String? gender,
    String? birthDate,
    String? nationality,
    String? residenceCountry,
    String? maritalStatus,
    String? email,
    bool? emailVerified,
    bool? phoneVerified,
    String? photo,
    String? preferredLanguage,
    String? preferredCurrency,
    String? loyaltyNumber,
    List? travelers,
    String? token,
  }) {
    return UserModel(
      id: id ?? this.id,
      fName: fName ?? this.fName,
      lName: lName ?? this.lName,
      phone: phone ?? this.phone,
      dialCode: code ?? dialCode,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      nationality: nationality ?? this.nationality,
      residenceCountry: residenceCountry ?? this.residenceCountry,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      email: email ?? this.email,
      emailVerified: emailVerified ?? this.emailVerified,
      phoneVerified: phoneVerified ?? this.phoneVerified,
      photo: photo ?? this.photo,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      preferredCurrency: preferredCurrency ?? this.preferredCurrency,
      loyaltyNumber: loyaltyNumber ?? this.loyaltyNumber,
      travelers: travelers ?? this.travelers,
      token: token ?? this.token,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
