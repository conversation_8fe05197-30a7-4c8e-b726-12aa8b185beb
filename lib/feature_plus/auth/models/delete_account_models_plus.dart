/// نموذج استجابة إرسال OTP لحذف الحساب
class SendOtpForDeletionResponse {
  final bool success;
  final String message;
  final String? maskedPhone;
  final int? expiresInMinutes;
  final String? error;

  const SendOtpForDeletionResponse({
    required this.success,
    required this.message,
    this.maskedPhone,
    this.expiresInMinutes,
    this.error,
  });

  factory SendOtpForDeletionResponse.fromJson(Map<String, dynamic> json) {
    return SendOtpForDeletionResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      maskedPhone: json['maskedPhone'],
      expiresInMinutes: json['expiresInMinutes'],
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'maskedPhone': maskedPhone,
      'expiresInMinutes': expiresInMinutes,
      'error': error,
    };
  }
}

/// نموذج استجابة حذف الحساب
class DeleteAccountResponse {
  final bool success;
  final String message;
  final DeleteAccountDetails? details;
  final String? error;

  const DeleteAccountResponse({
    required this.success,
    required this.message,
    this.details,
    this.error,
  });

  factory DeleteAccountResponse.fromJson(Map<String, dynamic> json) {
    return DeleteAccountResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      details: json['details'] != null 
          ? DeleteAccountDetails.fromJson(json['details'])
          : null,
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'details': details?.toJson(),
      'error': error,
    };
  }
}

/// تفاصيل حذف الحساب
class DeleteAccountDetails {
  final String userId;
  final String phone;
  final int bookingsCount;
  final bool bookingsAnonymized;
  final DateTime deletedAt;

  const DeleteAccountDetails({
    required this.userId,
    required this.phone,
    required this.bookingsCount,
    required this.bookingsAnonymized,
    required this.deletedAt,
  });

  factory DeleteAccountDetails.fromJson(Map<String, dynamic> json) {
    return DeleteAccountDetails(
      userId: json['userId'] ?? '',
      phone: json['phone'] ?? '',
      bookingsCount: json['bookingsCount'] ?? 0,
      bookingsAnonymized: json['bookingsAnonymized'] ?? false,
      deletedAt: DateTime.parse(json['deletedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'phone': phone,
      'bookingsCount': bookingsCount,
      'bookingsAnonymized': bookingsAnonymized,
      'deletedAt': deletedAt.toIso8601String(),
    };
  }
}

/// أسباب حذف الحساب
enum DeleteAccountReason {
  privacyConcerns('privacy_concerns', 'مخاوف الخصوصية'),
  notUseful('not_useful', 'التطبيق غير مفيد'),
  foundAlternative('found_alternative', 'وجدت بديل أفضل'),
  tooManyEmails('too_many_emails', 'كثرة الإشعارات'),
  technicalIssues('technical_issues', 'مشاكل تقنية'),
  other('other', 'أخرى');

  const DeleteAccountReason(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static DeleteAccountReason fromValue(String value) {
    return DeleteAccountReason.values.firstWhere(
      (reason) => reason.value == value,
      orElse: () => DeleteAccountReason.other,
    );
  }
}
