import 'package:get/get.dart';


class HotelsDataPlus {
  static final HotelsDataPlus _instance = HotelsDataPlus._internal();
  factory HotelsDataPlus() => _instance;
  HotelsDataPlus._internal();

  // Controllers
  late final HotelsControllerPlus hotelsController;
  late final HotelDetailsControllerPlus hotelDetailsController;
  late final GeolocationHotelSearchControllerPlus geolocationController;

  void initialize() {
    // Initialize controllers
    hotelsController = Get.put(HotelsControllerPlus());
    hotelDetailsController = Get.put(HotelDetailsControllerPlus());
    geolocationController = Get.put(GeolocationHotelSearchControllerPlus());
  }

  void dispose() {
    hotelsController.dispose();
    hotelDetailsController.dispose();
    geolocationController.dispose();
  }
}

// Controller classes
class HotelsControllerPlus extends GetxController {
  HotelsControllerPlus();
}

class HotelDetailsControllerPlus extends GetxController {
  HotelDetailsControllerPlus();
}

class GeolocationHotelSearchControllerPlus extends GetxController {
  GeolocationHotelSearchControllerPlus();
} 