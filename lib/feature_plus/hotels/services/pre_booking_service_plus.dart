import 'package:dio/dio.dart';
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:get/get.dart';
import '../models/pre_booking_models.dart';
import '../models/hotel_models.dart';

// Custom exception for session expired
class SessionExpiredException implements Exception {
  final String message;
  SessionExpiredException(this.message);

  @override
  String toString() => message;
}

class PreBookingServicePlus {
  final DioClient _dioClient = DioClient();

  // Pre-booking API call
  Future<PreBookingResponsePlus> performPreBooking({
    required String bookingCode,
    required String paymentMode,
    required String currency,
  }) async {
    print('🔵 [PRE_BOOKING_SERVICE] performPreBooking started');
    print('🔵 [PRE_BOOKING_SERVICE] Booking Code: $bookingCode');
    print('🔵 [PRE_BOOKING_SERVICE] Payment Mode: $paymentMode');
    print('🔵 [PRE_BOOKING_SERVICE] Currency: $currency');

    // Prepare request body
    final requestBody = {
      "bookingCode": bookingCode,
      "paymentMode": paymentMode,
      "currency": currency,
    };

    print('🔵 [PRE_BOOKING_SERVICE] Request body prepared: $requestBody');
    print('🔵 [PRE_BOOKING_SERVICE] Getting Dio instance...');

    // Get Dio instance and make API call
    final dio = _dioClient.getDio(DioType.api);
    print('🔵 [PRE_BOOKING_SERVICE] Dio instance obtained');
    print(
        '🔵 [PRE_BOOKING_SERVICE] Making POST request to /hotels/booking/pre-book');

    final response = await dio.post(
      '/hotels/booking/pre-book',
      data: requestBody,
    );

    print('🔵 [PRE_BOOKING_SERVICE] API response received');
    print('🔵 [PRE_BOOKING_SERVICE] Status code: ${response.statusCode}');
    print(
        '🔵 [PRE_BOOKING_SERVICE] Response data type: ${response.data.runtimeType}');

    // Check if response is successful (200 OK or 201 Created)
    if ((response.statusCode == 200 || response.statusCode == 201) &&
        response.data != null) {
      print('🔵 [PRE_BOOKING_SERVICE] Response status is valid');
      final responseData = response.data;
      print(
          '🔵 [PRE_BOOKING_SERVICE] Response data keys: ${responseData.keys?.toList()}');

      // Check if the API response indicates success
      if (responseData['success'] == true && responseData['data'] != null) {
        print('🔵 [PRE_BOOKING_SERVICE] API response indicates success');
        final preBookingData = responseData['data'];
        print(
            '🔵 [PRE_BOOKING_SERVICE] Pre-booking data keys: ${preBookingData.keys?.toList()}');

        print('🔵 [PRE_BOOKING_SERVICE] Mapping API response to model...');
        // Create PreBookingResponsePlus from API response
        final preBookingResponse = _mapApiResponseToModel(
          preBookingData,
          bookingCode,
          currency,
        );

        print('🔵 [PRE_BOOKING_SERVICE] Model mapping completed successfully');
        print(
            '🔵 [PRE_BOOKING_SERVICE] Booking ID: ${preBookingResponse.bookingId}');
        print(
            '🔵 [PRE_BOOKING_SERVICE] Total Price: ${preBookingResponse.totalPrice}');

        return preBookingResponse;
      } else {
        // API returned success: false
        final errorMessage = responseData['message'] ?? 'Pre-booking failed';
        print(
            '🔴 [PRE_BOOKING_SERVICE] API returned success: false - $errorMessage');

        // Handle specific TBO API errors
        if (errorMessage.contains('502') ||
            errorMessage.contains('Bad Gateway') ||
            errorMessage.contains('Failed to call TBO API')) {
          throw Exception(
              'خدمة الحجز غير متاحة حالياً. يرجى المحاولة مرة أخرى لاحقاً.');
        } else if (errorMessage.contains('Session Expired')) {
          throw SessionExpiredException(
              'انتهت صلاحية الجلسة. يرجى إعادة تسجيل الدخول.');
        } else {
          throw Exception('فشل في الحجز المسبق: $errorMessage');
        }
      }
    } else {
      print(
          '🔴 [PRE_BOOKING_SERVICE] Invalid response status: ${response.statusCode}');
      throw Exception('Invalid response from pre-booking API');
    }

    try {} on DioException catch (e) {
      print('🔴 [PRE_BOOKING_SERVICE] DioException caught');
      print('🔴 [PRE_BOOKING_SERVICE] Error type: ${e.type}');
      print('🔴 [PRE_BOOKING_SERVICE] Error message: ${e.message}');
      print(
          '🔴 [PRE_BOOKING_SERVICE] Response status: ${e.response?.statusCode}');
      print('🔴 [PRE_BOOKING_SERVICE] Response data: ${e.response?.data}');

      // Handle specific error cases
      if (e.response?.statusCode == 400) {
        print('🔴 [PRE_BOOKING_SERVICE] Bad request error');
        throw Exception(
            'Invalid booking request. Please check your selection.');
      } else if (e.response?.statusCode == 404) {
        print('🔴 [PRE_BOOKING_SERVICE] Not found error');
        throw Exception('Booking not found. Please try again.');
      } else if (e.response?.statusCode == 409) {
        print('🔴 [PRE_BOOKING_SERVICE] Conflict error');
        throw Exception(
            'Room no longer available. Please select another room.');
      } else {
        print('🔴 [PRE_BOOKING_SERVICE] Network error');
        throw Exception(
            'Network error. Please check your connection and try again.');
      }
    } on SessionExpiredException {
      // Re-throw SessionExpiredException without wrapping it
      rethrow;
    } catch (e) {
      print('🔴 [PRE_BOOKING_SERVICE] Unexpected error: $e');
      print('🔴 [PRE_BOOKING_SERVICE] Stack trace: ${StackTrace.current}');
      throw Exception('An unexpected error occurred. Please try again.');
    }
  }

  // Map API response to PreBookingResponsePlus model
  PreBookingResponsePlus _mapApiResponseToModel(
    Map<String, dynamic> apiData,
    String bookingCode,
    String currency,
  ) {
    print('🟡 [PRE_BOOKING_SERVICE] _mapApiResponseToModel started');
    print('🟡 [PRE_BOOKING_SERVICE] API data keys: ${apiData.keys.toList()}');
    print('🟡 [PRE_BOOKING_SERVICE] Booking code: $bookingCode');
    print('🟡 [PRE_BOOKING_SERVICE] Currency: $currency');

    // Extract data from API response
    final bookingId = apiData['bookingId']?.toString() ??
        apiData['preBookingId']?.toString() ??
        'PB${DateTime.now().millisecondsSinceEpoch}';

    return PreBookingResponsePlus.fromJson(apiData)
        .copyWith(bookingId: bookingId, adults: 0, children: 0);
  }

  // Helper methods
  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  String _generateCancellationPolicy(
      List<dynamic> policies, bool isRefundable) {
    if (policies.isNotEmpty) {
      final policy = policies.first;
      if (policy is Map<String, dynamic>) {
        final chargeType = policy['ChargeType']?.toString() ?? '';
        final charge = policy['CancellationCharge']?.toString() ?? '';

        if (chargeType == 'Percentage' && charge == '100') {
          return 'cancellationPolicy.nonRefundablePolicy'.tr;
        } else if (chargeType == 'Percentage') {
          return 'cancellationPolicy.percentageCharge'
              .tr
              .replaceAll('{charge}', charge);
        } else {
          return 'cancellationPolicy.reviewWithHotel'.tr;
        }
      }
    }

    if (isRefundable) {
      return 'cancellationPolicy.freeUntil24Hours'.tr;
    } else {
      return 'cancellationPolicy.nonRefundableAfterConfirmation'.tr;
    }
  }

  List<String> _extractImportantNotes(
    Map<String, dynamic> apiData, {
    String roomType = '',
    String inclusion = '',
    String mealType = '',
    bool withTransfers = false,
    List<dynamic> roomPromotions = const [],
  }) {
    final notes = <String>[];

    // Add default notes
    notes.add('يجب إبراز بطاقة هوية صالحة عند تسجيل الوصول');
    notes.add('تسجيل الوصول من الساعة 3:00 مساءً');
    notes.add('تسجيل المغادرة حتى الساعة 12:00 ظهراً');

    // Add room type info
    if (roomType.isNotEmpty) {
      if (roomType.toLowerCase().contains('nonsmoking') ||
          roomType.toLowerCase().contains('non smoking')) {
        notes.add('غرفة غير مدخنين');
      }
      if (roomType.toLowerCase().contains('city view')) {
        notes.add('إطلالة على المدينة');
      }
      if (roomType.toLowerCase().contains('king bed')) {
        notes.add('سرير كينغ ساز');
      }
    }

    // Add meal type info
    if (mealType.isNotEmpty) {
      if (mealType.toLowerCase().contains('breakfast')) {
        notes.add('الإفطار مشمول في السعر');
      } else if (mealType.toLowerCase() == 'room_only') {
        notes.add('الغرفة فقط - بدون وجبات');
      }
    }

    // Add inclusion info
    if (inclusion.isNotEmpty) {
      final inclusions = inclusion.split(',');
      for (final inc in inclusions) {
        final cleanInc = inc.trim();
        if (cleanInc.toLowerCase().contains('parking')) {
          notes.add('موقف سيارات مجاني');
        }
        if (cleanInc.toLowerCase().contains('wifi')) {
          notes.add('واي فاي مجاني');
        }
        if (cleanInc.toLowerCase().contains('breakfast')) {
          notes.add('الإفطار مشمول');
        }
      }
    }

    // Add transfer info
    if (withTransfers) {
      notes.add('خدمة النقل من وإلى المطار متوفرة');
    }

    // Add promotions as notes
    if (roomPromotions.isNotEmpty) {
      for (final promotion in roomPromotions) {
        if (promotion is String && promotion.isNotEmpty) {
          notes.add('عرض خاص: $promotion');
        }
      }
    }

    // Add rate conditions from API
    if (apiData['rateConditions'] is List) {
      final conditions = apiData['rateConditions'] as List;
      for (final condition in conditions.take(3)) {
        // Take first 3 conditions
        if (condition is String && condition.isNotEmpty) {
          // Clean HTML tags and extract useful info
          String cleanCondition = condition
              .replaceAll(RegExp(r'<[^>]*>'), '')
              .replaceAll('&lt;', '<')
              .replaceAll('&gt;', '>')
              .trim();

          if (cleanCondition.length > 100) {
            cleanCondition = '${cleanCondition.substring(0, 100)}...';
          }

          if (cleanCondition.isNotEmpty &&
              !cleanCondition.toLowerCase().contains('http') &&
              !cleanCondition.toLowerCase().contains('terms of use')) {
            notes.add(cleanCondition);
          }
        }
      }
    }

    notes.add('قد تطبق رسوم إضافية للخدمات الاختيارية');
    notes.add('الأسعار شاملة للضرائب والرسوم');

    return notes;
  }
}
