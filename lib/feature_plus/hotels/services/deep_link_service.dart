import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/components/logger.dart';
import '../../../core/core.dart';
import '../../../core/models/hotels/hotels.dart';
import '../../home/<USER>/guests_search.dart';
import '../models/hotel_models.dart';
import '../routes/hotels_routes_plus.dart';
import '../../bookings/models/booking_models_plus.dart';
import '../../bookings/routes/booking_routes.dart';

class DeepLinkService extends GetxService {
  static DeepLinkService get to => Get.find<DeepLinkService>();

  @override
  void onInit() {
    super.onInit();
    _initializeDeepLinkHandling();
  }

  void _initializeDeepLinkHandling() {
    // Listen for incoming links when app is already running
    // This would typically use a package like app_links or similar
    Log.debug('🔗 Deep link service initialized');
  }

  /// Handle incoming deep link URL
  Future<void> handleDeepLink(String url) async {
    try {
      Log.debug('🔗 Processing deep link: $url');

      final uri = Uri.parse(url);

      // Check the type of deep link
      if (uri.host == 'hotel' || uri.path.startsWith('/hotel')) {
        await _handleHotelDeepLink(uri);
      } else if (uri.host == 'booking' || uri.path.startsWith('/booking')) {
        await _handleBookingDeepLink(uri);
      } else if (uri.host == 'search' || uri.path.startsWith('/search')) {
        await _handleSearchDeepLink(uri);
      } else {
        Log.warning('⚠️ Unknown deep link format: $url');
        Core.showGlobalSnackBar(
          'رابط غير مدعوم',
          backgroundColor: Colors.orange,
        );
      }
    } catch (e) {
      Log.error('❌ Error processing deep link: $e');
      Core.showGlobalSnackBar(
        'خطأ في فتح الرابط',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Handle hotel-specific deep links
  Future<void> _handleHotelDeepLink(Uri uri) async {
    try {
      // Extract hotel information from query parameters
      final queryParams = uri.queryParameters;

      final hotelCode = queryParams['code'];
      final hotelName = queryParams['name'];
      final city = queryParams['city'];
      final country = queryParams['country'];
      final starsStr = queryParams['stars'];
      final priceStr = queryParams['price'];
      final currency = queryParams['currency'];
      final checkInStr = queryParams['checkin'];
      final checkOutStr = queryParams['checkout'];
      final adultsStr = queryParams['adults'];
      final childrenStr = queryParams['children'];
      final roomsStr = queryParams['rooms'];
      final paxRoomsStr = queryParams['paxRooms'];

      if (hotelCode == null) {
        Log.error('❌ Missing required hotel code in deep link');
        Core.showGlobalSnackBar(
          'رابط الفندق غير صحيح - كود الفندق مفقود',
          backgroundColor: Colors.red,
        );
        return;
      }

      // Parse dates and guest information
      DateTime? checkInDate;
      DateTime? checkOutDate;
      int adults = 2; // Default
      int children = 0; // Default
      int rooms = 1; // Default
      List<PaxRoom> paxRooms = []; // Default empty

      try {
        if (checkInStr != null) {
          checkInDate = DateTime.parse(checkInStr);
        }
        if (checkOutStr != null) {
          checkOutDate = DateTime.parse(checkOutStr);
        }
        adults = int.tryParse(adultsStr ?? '2') ?? 2;
        children = int.tryParse(childrenStr ?? '0') ?? 0;
        rooms = int.tryParse(roomsStr ?? '1') ?? 1;

        // Parse paxRooms JSON if available
        if (paxRoomsStr != null && paxRoomsStr.isNotEmpty) {
          try {
            final decodedPaxRooms = Uri.decodeComponent(paxRoomsStr);
            final paxRoomsJson = jsonDecode(decodedPaxRooms) as List;
            paxRooms =
                paxRoomsJson.map((json) => PaxRoom.fromJson(json)).toList();
            Log.debug(
                '🔍 [DEEP_LINK] Parsed paxRooms: ${paxRooms.length} rooms');

            // Update totals from paxRooms if available
            if (paxRooms.isNotEmpty) {
              adults =
                  paxRooms.fold(0, (sum, room) => sum + (room.adults ?? 0));
              children =
                  paxRooms.fold(0, (sum, room) => sum + (room.children ?? 0));
              rooms = paxRooms.length;
            }
          } catch (e) {
            Log.warning('⚠️ Error parsing paxRooms JSON: $e');
            // Create default paxRooms based on adults/children/rooms
            paxRooms = _createDefaultPaxRooms(adults, children, rooms);
          }
        } else {
          // Create default paxRooms based on adults/children/rooms
          paxRooms = _createDefaultPaxRooms(adults, children, rooms);
        }
      } catch (e) {
        Log.warning('⚠️ Error parsing date/guest parameters: $e');
        // Create default paxRooms
        paxRooms = _createDefaultPaxRooms(adults, children, rooms);
      }

      // Create a basic hotel object from the deep link parameters
      final hotel = HotelPlus(
        code: hotelCode,
        name: hotelName != null ? Uri.decodeComponent(hotelName) : 'فندق',
        description: 'تفاصيل الفندق من الرابط المشترك',
        starRating: int.tryParse(starsStr ?? '0') ?? 0,
        address: '',
        city: city != null ? Uri.decodeComponent(city) : '',
        country: country != null ? Uri.decodeComponent(country) : '',
        currency: currency ?? 'USD',
        images: [],
        amenities: [],
        facilities: [],
      );

      // Navigate to hotel details with search parameters
      await HotelsNavigation.toHotelDetails(
        hotel: hotel,
        scrollToRooms: false,
        additionalArguments: {
          "fromDeepLink": true,
          "checkInDate": checkInDate,
          "checkOutDate": checkOutDate,
          "adults": adults,
          "children": children,
          "rooms": rooms,
          "paxRooms": paxRooms, // Add paxRooms to arguments
          "searchWithAvailability": true, // Flag to trigger availability search
        },
      );

      Log.debug('✅ Successfully navigated to hotel: ${hotel.name}');

      Core.showGlobalSnackBar(
        'تم فتح تفاصيل الفندق بنجاح',
        backgroundColor: Colors.green,
      );
    } catch (e) {
      Log.error('❌ Error handling hotel deep link: $e');
      Core.showGlobalSnackBar(
        'خطأ في فتح تفاصيل الفندق',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Handle booking-specific deep links
  ///
  /// Supported formats:
  /// - fandooq://booking?ref=BOOKING-REF-123&hotel=Hotel%20Name
  /// - https://fandooq/booking?ref=BOOKING-REF-123&hotel=Hotel%20Name
  Future<void> _handleBookingDeepLink(Uri uri) async {
    try {
      Log.debug('📋 Processing booking deep link: ${uri.toString()}');

      final queryParams = uri.queryParameters;
      final bookingRef = queryParams['ref'];
      final id = queryParams['id'];
      final hotelName = queryParams['hotel'];

      if (bookingRef == null) {
        Log.error('❌ Missing booking reference in deep link');
        Core.showGlobalSnackBar(
          'رابط الحجز غير صحيح - رقم الحجز مفقود',
          backgroundColor: Colors.red,
        );
        return;
      }

      // Navigate to booking details with the reference
      BookingNavigation.toBookingDetailsWithParams(
        bookingReferenceId: bookingRef,
        arguments: {
          "booking": BookingSummaryPlus(id: id, bookingReferenceId: bookingRef),
          "fromDeepLink": true
        },
      );

      Log.debug('✅ Successfully navigated to booking: $bookingRef');

      Core.showGlobalSnackBar(
        'تم فتح تفاصيل الحجز بنجاح',
        backgroundColor: Colors.green,
      );
    } catch (e) {
      Log.error('❌ Error handling booking deep link: $e');
      Core.showGlobalSnackBar(
        'خطأ في فتح تفاصيل الحجز',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Handle search-specific deep links
  ///
  /// Supported formats:
  /// - fandooq://search?city=Dubai&checkin=2025-01-01&checkout=2025-01-03&adults=2
  /// - https://fandooq/search?city=Dubai&checkin=2025-01-01&checkout=2025-01-03&adults=2
  Future<void> _handleSearchDeepLink(Uri uri) async {
    try {
      Log.debug('🔍 Processing search deep link: ${uri.toString()}');

      final queryParams = uri.queryParameters;
      final city = queryParams['city'];
      final checkinStr = queryParams['checkin'];
      final checkoutStr = queryParams['checkout'];
      final adultsStr = queryParams['adults'];
      final childrenStr = queryParams['children'];
      final roomsStr = queryParams['rooms'];

      // Parse dates
      DateTime? checkin;
      DateTime? checkout;

      if (checkinStr != null) {
        checkin = DateTime.tryParse(checkinStr);
      }
      if (checkoutStr != null) {
        checkout = DateTime.tryParse(checkoutStr);
      }

      // Navigate to search/hotels screen with parameters
      // This would typically navigate to your hotels search screen
      // and pass the search parameters

      Log.debug('✅ Successfully processed search deep link for city: $city');

      Core.showGlobalSnackBar(
        'تم فتح البحث بنجاح',
        backgroundColor: Colors.green,
      );
    } catch (e) {
      Log.error('❌ Error handling search deep link: $e');
      Core.showGlobalSnackBar(
        'خطأ في فتح البحث',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Create a deep link URL for sharing
  String createHotelShareLink(HotelPlus hotel) {
    // Use custom scheme for app-to-app sharing
    const baseUrl = 'fandooq://hotel';

    final queryParams = {
      'code': hotel.code,
      'name': Uri.encodeComponent(hotel.name),
      'city': Uri.encodeComponent(hotel.city),
      'country': Uri.encodeComponent(hotel.country),
      'stars': hotel.starRating.toString(),
      'currency': hotel.currency,
    };

    final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
    return uri.toString();
  }

  /// Create a web-compatible deep link URL for sharing
  String createHotelWebLink(
    HotelPlus hotel, {
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? adults,
    int? children,
    int? rooms,
    List<PaxRoom>? paxRooms,
  }) {
    // Use HTTPS URL for web sharing
    const baseUrl = 'https://fandooq/hotel';

    final queryParams = <String, String>{
      'code': hotel.code,
      'name': Uri.encodeComponent(hotel.name),
    };

    // Add optional parameters if available
    if (hotel.city.isNotEmpty) {
      queryParams['city'] = Uri.encodeComponent(hotel.city);
    }
    if (hotel.country.isNotEmpty) {
      queryParams['country'] = Uri.encodeComponent(hotel.country);
    }
    if (hotel.starRating > 0) {
      queryParams['stars'] = hotel.starRating.toString();
    }
    if (hotel.currency.isNotEmpty) {
      queryParams['currency'] = hotel.currency;
    }
    if (hotel.latitude != null) {
      queryParams['lat'] = hotel.latitude.toString();
    }
    if (hotel.longitude != null) {
      queryParams['lng'] = hotel.longitude.toString();
    }
    if (hotel.images.isNotEmpty) {
      queryParams['image'] = Uri.encodeComponent(hotel.images.first);
    }

    // Add price if available
    if (hotel.pricing?.converted.price != null) {
      queryParams['price'] = hotel.pricing!.converted.price.toString();
    }

    // Add search parameters if provided
    if (checkInDate != null) {
      queryParams['checkin'] = checkInDate.toIso8601String().split('T')[0];
    }
    if (checkOutDate != null) {
      queryParams['checkout'] = checkOutDate.toIso8601String().split('T')[0];
    }
    if (adults != null && adults > 0) {
      queryParams['adults'] = adults.toString();
    }
    if (children != null && children > 0) {
      queryParams['children'] = children.toString();
    }
    if (rooms != null && rooms > 0) {
      queryParams['rooms'] = rooms.toString();
    }

    // Add paxRooms JSON if provided
    if (paxRooms != null && paxRooms.isNotEmpty) {
      try {
        final paxRoomsJson = paxRooms.map((room) => room.toJson()).toList();
        final encodedPaxRooms = Uri.encodeComponent(jsonEncode(paxRoomsJson));
        queryParams['paxRooms'] = encodedPaxRooms;
        Log.debug(
            '🔍 [DEEP_LINK] Added paxRooms to URL: ${paxRooms.length} rooms');
      } catch (e) {
        Log.warning('⚠️ Error encoding paxRooms: $e');
      }
    }

    final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
    return uri.toString();
  }

  /// Create a deep link URL for sharing a booking
  String createBookingShareLink(BookingSummaryPlus booking) {
    final queryParams = <String, String>{
      'ref': booking.bookingReferenceId ?? '',
    };

    // Add hotel name if available
    if (booking.hotelName != null && booking.hotelName!.isNotEmpty) {
      queryParams['hotel'] = Uri.encodeComponent(booking.hotelName!);
    }

    // Add dates if available
    if (booking.checkIn != null) {
      queryParams['checkin'] = booking.checkIn!;
    }
    if (booking.checkOut != null) {
      queryParams['checkout'] = booking.checkOut!;
    }

    const baseUrl = 'https://fandooq/booking';
    final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
    return uri.toString();
  }

  /// Share hotel via platform's native sharing
  Future<void> shareHotel(
    HotelPlus hotel, {
    DateTime? checkInDate,
    DateTime? checkOutDate,
    int? adults,
    int? children,
    int? rooms,
    List<PaxRoom>? paxRooms,
  }) async {
    try {
      final link = createHotelWebLink(
        hotel,
        checkInDate: checkInDate,
        checkOutDate: checkOutDate,
        adults: adults,
        children: children,
        rooms: rooms,
        paxRooms: paxRooms,
      );
      final text = 'شاهد هذا الفندق الرائع: ${hotel.name}\n$link';

      await Share.share(
        text,
        subject: 'فندق ${hotel.name}',
      );

      Log.debug('✅ Hotel shared successfully: ${hotel.name}');
    } catch (e) {
      Log.error('❌ Error sharing hotel: $e');
      Core.showGlobalSnackBar(
        'خطأ في مشاركة الفندق',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Share booking via platform's native sharing
  Future<void> shareBooking(BookingSummaryPlus booking) async {
    try {
      final link = createBookingShareLink(booking);
      final hotelName = booking.hotelName ?? 'الفندق';
      final text =
          'شاهد تفاصيل حجزي في $hotelName\nرقم الحجز: ${booking.bookingReferenceId}\n$link';

      await Share.share(
        text,
        subject: 'حجز فندق - ${booking.bookingReferenceId}',
      );

      Log.debug('✅ Booking shared successfully: ${booking.bookingReferenceId}');
    } catch (e) {
      Log.error('❌ Error sharing booking: $e');
      Core.showGlobalSnackBar(
        'خطأ في مشاركة الحجز',
        backgroundColor: Colors.red,
      );
    }
  }

  /// Create default paxRooms based on adults, children, and rooms count
  List<PaxRoom> _createDefaultPaxRooms(int adults, int children, int rooms) {
    final List<PaxRoom> paxRooms = [];

    // Distribute adults and children across rooms
    final adultsPerRoom = (adults / rooms).floor();
    final childrenPerRoom = (children / rooms).floor();
    final extraAdults = adults % rooms;
    final extraChildren = children % rooms;

    for (int i = 0; i < rooms; i++) {
      final roomAdults = adultsPerRoom + (i < extraAdults ? 1 : 0);
      final roomChildren = childrenPerRoom + (i < extraChildren ? 1 : 0);

      // Create default children ages (assuming 8 years old)
      final childrenAges = List.generate(roomChildren, (index) => 8);

      paxRooms.add(PaxRoom(
        adults: roomAdults,
        children: roomChildren,
        childrenAges: childrenAges,
      ));
    }

    Log.debug(
        '🔍 [DEEP_LINK] Created default paxRooms: ${paxRooms.length} rooms, $adults adults, $children children');
    return paxRooms;
  }
}
