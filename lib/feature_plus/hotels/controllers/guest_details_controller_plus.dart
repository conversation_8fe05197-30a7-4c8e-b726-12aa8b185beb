import 'package:fandooq/feature_plus/feature_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/core.dart';
import '../models/hotel_models.dart';
import '../models/hotels_data_search.dart';
import '../models/pre_booking_models.dart';
import '../models/hotel_search_request.dart';
import '../routes/hotels_routes_plus.dart';
import '../controllers/payment_controller_plus.dart';

class GuestDetailsControllerPlus extends GetxController {
  // Observable variables
  final _isLoading = false.obs;
  final _guests = <GuestDetailsPlus>[].obs;
  final _preBookingData = Rxn<PreBookingResponsePlus>();
  final _hotel = Rxn<HotelPlus>();
  final _room = Rxn<RoomPlus>();
  final _bookingRequest = Rxn<PreBookingRequestPlus>();
  final _specialRequests = ''.obs;

  // Form controllers
  final List<Map<String, TextEditingController>> guestControllers = [];

  // Getters
  bool get isLoading => _isLoading.value;
  List<GuestDetailsPlus> get guests => _guests;
  PreBookingResponsePlus? get preBookingData => _preBookingData.value;
  HotelPlus? get hotel => _hotel.value;
  RoomPlus? get room => _room.value;
  PreBookingRequestPlus? get bookingRequest => _bookingRequest.value;
  String get specialRequests => _specialRequests.value;

  // Booking details
  int get requiredGuests =>
      (bookingRequest?.adults ?? 0) + (bookingRequest?.children ?? 0);
  int get adults => bookingRequest?.adults ?? 0;
  int get children => bookingRequest?.children ?? 0;
  String get hotelName => hotel?.name ?? '';
  String get roomType => room?.roomType ?? '';
  num get totalPrice => preBookingData?.totalPrice ?? 0.0;
  String get currency => preBookingData?.currency ?? 'USD';

  // Validation
  bool get isFormValid {
    if (guests.length != requiredGuests) return false;

    for (final guest in guests) {
      if (guest.firstName.isEmpty ||
          guest.lastName.isEmpty ||
          guest.nationality.isEmpty ||
          guest.email.isEmpty ||
          guest.phone.isEmpty) {
        return false;
      }
    }
    return true;
  }

  // Initialize with booking data
  void initializeGuestDetails() {
    print('👥 Initializing guest details...');

    _preBookingData.value = Get.arguments["preBookingData"];
    _hotel.value = Get.arguments["hotel"];
    _room.value = Get.arguments["room"];
    _bookingRequest.value = Get.arguments["bookingRequest"];

    // Initialize guest forms
    _initializeGuestForms();

    print('✅ Guest details initialized successfully');
  }

  // Initialize guest forms
  void _initializeGuestForms() {
    _guests.clear();
    guestControllers.clear();

    // Add adult guests
    for (int i = 0; i < adults; i++) {
      _addGuestForm('adult', i == 0); // First adult is main guest
    }

    // Add child guests
    for (int i = 0; i < children; i++) {
      _addGuestForm('child', false);
    }

    print('📝 Created ${_guests.length} guest forms');
  }

  // Add guest form
  void _addGuestForm(String guestType, bool isMainGuest) {
    // Create text controllers
    final controllers = {
      'title': TextEditingController(text: 'Mr'), // Default title
      'firstName': TextEditingController(),
      'lastName': TextEditingController(),
      'nationality': TextEditingController(),
      'email': TextEditingController(),
      'phone': TextEditingController(),
    };

    guestControllers.add(controllers);

    // Create guest model
    final guest = GuestDetailsPlus(
      firstName: '',
      lastName: '',
      nationality: '',
      email: '',
      phone: '',
      isMainGuest: isMainGuest,
      guestType: guestType,
    );

    _guests.add(guest);
  }

  // Update guest details
  void updateGuest(
    int index, {
    String? title,
    String? firstName,
    String? lastName,
    String? nationality,
    String? email,
    String? phone,
  }) {
    if (index >= 0 && index < _guests.length) {
      final currentGuest = _guests[index];

      final updatedGuest = GuestDetailsPlus(
        title: title ?? currentGuest.title,
        firstName: firstName ?? currentGuest.firstName,
        lastName: lastName ?? currentGuest.lastName,
        nationality: nationality ?? currentGuest.nationality,
        email: email ?? currentGuest.email,
        phone: phone ?? currentGuest.phone,
        isMainGuest: currentGuest.isMainGuest,
        guestType: currentGuest.guestType,
      );

      _guests[index] = updatedGuest;
    }
  }

  // Update special requests
  void updateSpecialRequests(String requests) {
    _specialRequests.value = requests;
  }

  // Validate guest form
  String? validateField(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال $fieldName';
    }

    switch (fieldName) {
      case 'البريد الإلكتروني':
        if (!GetUtils.isEmail(value)) {
          return 'يرجى إدخال بريد إلكتروني صحيح';
        }
        break;
      case 'رقم الهاتف':
        if (value.length < 8) {
          return 'يرجى إدخال رقم هاتف صحيح';
        }
        break;
    }

    return null;
  }

  // Get title list
  List<String> get titleList => [
        'Mr',
        'Ms',
        'Mrs',
        'Dr',
        'Prof',
      ];

  // Get nationality list
  List<String> get nationalityList => [
        'مصري',
        'سعودي',
        'إماراتي',
        'كويتي',
        'قطري',
        'بحريني',
        'عماني',
        'أردني',
        'لبناني',
        'سوري',
        'عراقي',
        'فلسطيني',
        'ليبي',
        'تونسي',
        'جزائري',
        'مغربي',
        'سوداني',
        'يمني',
        'أمريكي',
        'بريطاني',
        'فرنسي',
        'ألماني',
        'إيطالي',
        'إسباني',
        'روسي',
        'صيني',
        'ياباني',
        'هندي',
        'باكستاني',
        'بنغلاديشي',
        'أخرى',
      ];

  // Proceed to payment
  Future<void> proceedToPayment() async {
    if (!isFormValid) {
      Core.showGlobalSnackBar(
        'بيانات ناقصة: يرجى إكمال جميع البيانات المطلوبة',
        backgroundColor: Colors.orange,
      );
      return;
    }

    try {
      print('💳 Proceeding to payment...');
      print('Guests: ${guests.length}');
      print('Special requests: $specialRequests');

      _isLoading.value = true;

      // Simulate processing delay
      await Future.delayed(const Duration(seconds: 1));

      // Navigate to payment screen with initialization
      final paymentController = Get.put(PaymentControllerPlus());

      // Create HotelSearchRequest from PreBookingRequestPlus
      final searchRequest = HotelSearchRequest(
          latitude: 0.0, // Default values - these should come from actual search
          longitude: 0.0,
          checkIn: HotelDetailsControllerPlus.to.searchCheckIn!,
          checkOut: HotelDetailsControllerPlus.to.searchCheckOut!,
          adults: HotelDetailsControllerPlus.to.searchAdults,
          children: HotelDetailsControllerPlus.to.searchRooms,
          rooms: HotelDetailsControllerPlus.to.searchRooms,
          currency: bookingRequest!.currency,
          paxRooms: HotelDetailsControllerPlus.to.searchPaxRooms
      );

      final guestPlusList = guests
          .map((guest) => GuestPlus(
                title: guest.title,
                firstName: guest.firstName,
                lastName: guest.lastName,
                nationality: guest.nationality,
                type: guest.guestType,
                passportNumber: guest.passportNumber ?? '',
                email: guest.email,
                phone: guest.phone,
              ))
          .toList();

      print('🔍 [GUEST_DETAILS_CONTROLLER] Preparing guest list for payment:');
      print(
          '🔍 [GUEST_DETAILS_CONTROLLER] Number of guests: ${guestPlusList.length}');
      for (int i = 0; i < guestPlusList.length; i++) {
        final guest = guestPlusList[i];
        print(
            '🔍 [GUEST_DETAILS_CONTROLLER] Guest $i: ${guest.firstName} ${guest.lastName}');
        print('🔍 [GUEST_DETAILS_CONTROLLER] Guest $i title: ${guest.title}');
        print('🔍 [GUEST_DETAILS_CONTROLLER] Guest $i type: ${guest.type}');
      }

      // Group guests by rooms - for now, distribute evenly across rooms
      final numberOfRooms = bookingRequest!.rooms;
      final roomsList = <RoomWithGuestsPlus>[];

      if (numberOfRooms == 1) {
        // All guests in one room
        roomsList.add(RoomWithGuestsPlus(guests: guestPlusList));
      } else {
        // Distribute guests across rooms based on your requirements:
        // Room 1: 2 guests, Room 2: 3 guests (2 adults + 1 child)
        if (numberOfRooms == 2 && guestPlusList.length == 5) {
          // Room 1: First 2 guests (adults)
          final room1Guests = guestPlusList.sublist(0, 2);
          roomsList.add(RoomWithGuestsPlus(guests: room1Guests));

          // Room 2: Remaining 3 guests (2 adults + 1 child)
          final room2Guests = guestPlusList.sublist(2);
          roomsList.add(RoomWithGuestsPlus(guests: room2Guests));
        } else {
          // Fallback: Distribute guests evenly across rooms
          final guestsPerRoom = (guestPlusList.length / numberOfRooms).floor();
          final extraGuests = guestPlusList.length % numberOfRooms;

          int currentIndex = 0;
          for (int roomIndex = 0; roomIndex < numberOfRooms; roomIndex++) {
            final roomGuestCount =
                guestsPerRoom + (roomIndex < extraGuests ? 1 : 0);
            final endIndex = currentIndex + roomGuestCount;

            if (currentIndex < guestPlusList.length) {
              final roomGuests = guestPlusList.sublist(
                  currentIndex, endIndex.clamp(0, guestPlusList.length));
              roomsList.add(RoomWithGuestsPlus(guests: roomGuests));
              currentIndex = endIndex;
            }
          }
        }
      }

      print('🔍 [GUEST_DETAILS_CONTROLLER] Created ${roomsList.length} rooms');
      for (int i = 0; i < roomsList.length; i++) {
        print(
            '🔍 [GUEST_DETAILS_CONTROLLER] Room ${i + 1} has ${roomsList[i].guests.length} guests');
      }

      paymentController.initialize(
        preBooking: preBookingData!,
        hotelData: hotel!,
        roomData: room!,
        request: searchRequest,
        roomsList: roomsList,
        requests: specialRequests,
      );

      // Navigate to payment screen using named routes
      HotelsNavigation.toPayment();

    } catch (e) {
      print('❌ Error proceeding to payment: $e');
      Core.showGlobalSnackBar(
        'خطأ: حدث خطأ أثناء المعالجة، يرجى المحاولة مرة أخرى',
        backgroundColor: Colors.red,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  // Go back to pre-booking
  void goBackToPreBooking() {
    print('⬅️ Going back to pre-booking...');
    Get.back();
  }

  // Auto-fill guest data (for testing)
  void autoFillGuestData() {
    if (guests.isNotEmpty) {
      // Fill main guest
      guestControllers[0]['title']?.text = 'Mr';
      guestControllers[0]['firstName']?.text = 'Abdulrahman';
      guestControllers[0]['lastName']?.text = 'Tarek';
      guestControllers[0]['nationality']?.text = 'مصري';
      guestControllers[0]['email']?.text = '<EMAIL>';
      guestControllers[0]['phone']?.text = '+201553158154';

      updateGuest(
        0,
        title: 'Mr',
        firstName: 'Abdulrahman',
        lastName: 'Tarek',
        nationality: 'مصري',
        email: '<EMAIL>',
        phone: '+201553158154',
      );

      // Fill other guests if any
      for (int i = 1; i < guests.length; i++) {
        guestControllers[i]['title']?.text = 'Mr';
        guestControllers[i]['firstName']?.text = 'نزيل ${i + 1}';
        guestControllers[i]['lastName']?.text = 'اختبار';
        guestControllers[i]['nationality']?.text = 'مصري';
        guestControllers[i]['email']?.text = 'guest${i + 1}@example.com';
        guestControllers[i]['phone']?.text = '+20123456789$i';

        updateGuest(
          i,
          title: 'Mr',
          firstName: 'نزيل ${i + 1}',
          lastName: 'اختبار',
          nationality: 'مصري',
          email: 'guest${i + 1}@example.com',
          phone: '+20123456789$i',
        );
      }
    }
  }

  @override
  void onClose() {
    // Dispose text controllers
    for (final controllerMap in guestControllers) {
      for (final controller in controllerMap.values) {
        controller.dispose();
      }
    }
    print('🔄 Guest details controller disposed');
    super.onClose();
  }

  @override
  void onInit() {
    // Initialize controller with booking data - use permanent: false for proper cleanup
    initializeGuestDetails();
    super.onInit();
  }
}
