import 'package:fandooq/core/models/hotels/geolocation_hotel_search_models.dart';
import 'package:fandooq/core/utils/snak_bar/snak_bar_helper.dart';
import 'package:fandooq/feature_plus/settings/controllers/settings_controller.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

/// Controller for geolocation-based hotel search
// class GeoLocationHotelSearchController extends GetxController {
//   final HotelsRepo _hotelsRepo = Get.find<HotelsRepo>();
//
//   // Observable variables
//   RxBool isLoading = false.obs;
//   RxBool isLocationLoading = false.obs;
//   RxString error = ''.obs;
//
//   // Search parameters
//   Rx<GeoLocationHotelSearchRequest?> currentSearchRequest =
//       Rx<GeoLocationHotelSearchRequest?>(null);
//
//   // Search results
//   Rx<GeoLocationHotelSearchResponse?> searchResponse =
//       Rx<GeoLocationHotelSearchResponse?>(null);
//   RxList<GeoLocationHotel> hotels = <GeoLocationHotel>[].obs;
//   Rx<GeoLocationHotel?> nearestHotel = Rx<GeoLocationHotel?>(null);
//   Rx<SearchAreaInfo?> searchArea = Rx<SearchAreaInfo?>(null);
//   RxInt totalResults = 0.obs;
//
//   // Location data
//   Rx<Position?> currentPosition = Rx<Position?>(null);
//   RxDouble currentLatitude = 0.0.obs;
//   RxDouble currentLongitude = 0.0.obs;
//
//   // Search filters
//   RxString checkInDate = ''.obs;
//   RxString checkOutDate = ''.obs;
//   RxInt adults = 2.obs;
//   RxInt children = 0.obs;
//   RxInt rooms = 1.obs;
//   RxString currency = 'USD'.obs;
//   RxDouble radiusKm = 10.0.obs;
//   RxInt maxResults = 50.obs;
//
//   @override
//   void onInit() {
//     super.onInit();
//     if (kDebugMode) {
//       print('🚀 GeoLocationHotelSearchController onInit() called');
//     }
//     _initializeLocation();
//     _initializeCurrency();
//   }
//
//   /// Initialize currency from settings
//   void _initializeCurrency() {
//     if (kDebugMode) {
//       print('💱 🔄 _initializeCurrency() called');
//     }
//
//     // Always try to get currency from settings controller first
//     try {
//       if (kDebugMode) {
//         print('💱 🔍 Checking if SettingsController is registered...');
//       }
//
//       if (Get.isRegistered<SettingsController>()) {
//         if (kDebugMode) {
//           print('💱 ✅ SettingsController is registered, getting currency...');
//         }
//         final settingsController = Get.find<SettingsController>();
//         currency.value = settingsController.appSettings.currency;
//         if (kDebugMode) {
//           print('💱 ✅ Got currency from SettingsController: ${currency.value}');
//         }
//       } else {
//         if (kDebugMode) {
//           print('⚠️ SettingsController not registered, using USD default');
//         }
//         currency.value = 'USD';
//       }
//     } catch (e) {
//       if (kDebugMode) {
//         print('⚠️ Error getting currency from settings: $e');
//       }
//       currency.value = 'USD';
//     }
//
//     // Always print current currency for debugging
//     if (kDebugMode) {
//       print('💱 🔍 Final currency value: ${currency.value}');
//     }
//   }
//
//   /// Update currency when settings change
//   void updateCurrency(String newCurrency) {
//     if (currency.value != newCurrency) {
//       currency.value = newCurrency;
//       if (kDebugMode) {
//         print('💱 Currency updated to: ${currency.value}');
//       }
//
//       // Refresh search with new currency if we have an active search
//       if (currentSearchRequest.value != null) {
//         if (kDebugMode) {
//           print('🔄 Refreshing search with new currency...');
//         }
//         searchHotelsByLocation(
//           latitude: currentSearchRequest.value!.latitude,
//           longitude: currentSearchRequest.value!.longitude,
//           currencyCode: currency.value,
//         );
//       }
//     }
//   }
//
//   /// Initialize location services and get current position
//   Future<void> _initializeLocation() async {
//     try {
//       isLocationLoading.value = true;
//
//       // Check if location services are enabled
//       bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
//       if (!serviceEnabled) {
//         error.value =
//             'Location services are disabled. Please enable location services.';
//         ToastManager.show(
//           title: 'Location Required',
//           message:
//               'Please enable location services to search for nearby hotels.',
//         );
//         return;
//       }
//
//       // Check location permission
//       LocationPermission permission = await Geolocator.checkPermission();
//       if (permission == LocationPermission.denied) {
//         permission = await Geolocator.requestPermission();
//         if (permission == LocationPermission.denied) {
//           error.value = 'Location permission denied';
//           ToastManager.show(
//             title: 'Permission Required',
//             message: 'Location permission is required to find nearby hotels.',
//           );
//           return;
//         }
//       }
//
//       if (permission == LocationPermission.deniedForever) {
//         error.value = 'Location permissions are permanently denied';
//         if (kDebugMode) {
//           print('Location permissions are permanently denied');
//         }
//         return;
//       }
//
//       // Get current position
//       await getCurrentLocation();
//     } catch (e) {
//       error.value = 'Failed to initialize location: $e';
//       if (kDebugMode) {
//         print('Location initialization error: $e');
//       }
//     } finally {
//       isLocationLoading.value = false;
//     }
//   }
//
//   /// Get current GPS location
//   Future<void> getCurrentLocation() async {
//     try {
//       isLocationLoading.value = true;
//       error.value = '';
//
//       Position position = await Geolocator.getCurrentPosition(
//         desiredAccuracy: LocationAccuracy.high,
//         timeLimit: const Duration(seconds: 10),
//       );
//
//       currentPosition.value = position;
//       currentLatitude.value = position.latitude;
//       currentLongitude.value = position.longitude;
//
//       if (kDebugMode) {
//         print('Current location: ${position.latitude}, ${position.longitude}');
//       }
//
//       ToastManager.show(
//         title: 'Location Found',
//         message: 'Current location detected successfully.',
//       );
//     } catch (e) {
//       error.value = 'Failed to get current location: $e';
//       if (kDebugMode) {
//         print('Get location error: $e');
//       }
//       ToastManager.show(
//         title: 'Location Error',
//         message: 'Failed to get current location. Please try again.',
//       );
//     } finally {
//       isLocationLoading.value = false;
//     }
//   }
//
//   /// Search for hotels using current location
//   Future<void> searchNearbyHotels() async {
//     if (currentLatitude.value == 0.0 || currentLongitude.value == 0.0) {
//       ToastManager.show(
//         title: 'Location Required',
//         message: 'Please enable location services first.',
//       );
//       await getCurrentLocation();
//       return;
//     }
//
//     await searchHotelsByLocation(
//       latitude: currentLatitude.value,
//       longitude: currentLongitude.value,
//     );
//   }
//
//   /// Search for hotels by specific coordinates
//   Future<void> searchHotelsByLocation({
//     required double latitude,
//     required double longitude,
//     String? checkIn,
//     String? checkOut,
//     int? adultsCount,
//     int? childrenCount,
//     int? roomsCount,
//     String? currencyCode,
//     double? searchRadiusKm,
//     int? maxResultsCount,
//   }) async {
//     try {
//       isLoading.value = true;
//       error.value = '';
//
//       final request = GeoLocationHotelSearchRequest(
//         latitude: latitude,
//         longitude: longitude,
//         checkIn: checkIn ??
//             (checkInDate.value.isNotEmpty ? checkInDate.value : null),
//         checkOut: checkOut ??
//             (checkOutDate.value.isNotEmpty ? checkOutDate.value : null),
//         adults: adultsCount ?? adults.value,
//         children: childrenCount ?? children.value,
//         rooms: roomsCount ?? rooms.value,
//         currency: currencyCode ?? currency.value,
//         radiusKm: searchRadiusKm ?? radiusKm.value,
//         maxResults: maxResultsCount ?? maxResults.value,
//       );
//
//       currentSearchRequest.value = request;
//
//       if (kDebugMode) {
//         print('Searching hotels at: $latitude, $longitude');
//         print('Search radius: ${request.radiusKm}km');
//         print('Max results: ${request.maxResults}');
//       }
//
//       final result = await _hotelsRepo.searchHotelsByLocation(request);
//
//       result.fold(
//         (failure) {
//           error.value = failure.errorMassage;
//           ToastManager.show(
//             title: 'Search Failed',
//             message: error.value,
//           );
//           if (kDebugMode) {
//             print('Hotel search failed: ${failure.errorMassage}');
//           }
//         },
//         (response) {
//           searchResponse.value = response;
//
//           if (response.success == true && response.data != null) {
//             hotels.value = response.data!.hotels ?? [];
//             nearestHotel.value = response.data!.nearestHotel;
//             searchArea.value = response.data!.searchArea;
//             totalResults.value = response.data!.totalResults ?? 0;
//
//             if (kDebugMode) {
//               print('Hotel search success: Found ${hotels.length} hotels');
//               print('Nearest hotel: ${nearestHotel.value?.hotelName}');
//               print('Search area: ${searchArea.value?.areaName}');
//             }
//
//             ToastManager.show(
//               title: 'Search Complete',
//               message: 'Found ${hotels.length} hotels nearby.',
//             );
//           } else {
//             error.value = 'No hotels found in this area';
//             hotels.clear();
//             nearestHotel.value = null;
//             searchArea.value = null;
//             totalResults.value = 0;
//           }
//         },
//       );
//     } catch (e) {
//       error.value = 'Search error: $e';
//       if (kDebugMode) {
//         print('Hotel search error: $e');
//       }
//       ToastManager.show(
//         title: 'Search Error',
//         message: 'An error occurred while searching for hotels.',
//       );
//     } finally {
//       isLoading.value = false;
//     }
//   }
//
//   /// Update search filters
//   void updateSearchFilters({
//     String? checkIn,
//     String? checkOut,
//     int? adultsCount,
//     int? childrenCount,
//     int? roomsCount,
//     String? currencyCode,
//     double? searchRadiusKm,
//     int? maxResultsCount,
//   }) {
//     if (checkIn != null) checkInDate.value = checkIn;
//     if (checkOut != null) checkOutDate.value = checkOut;
//     if (adultsCount != null) adults.value = adultsCount;
//     if (childrenCount != null) children.value = childrenCount;
//     if (roomsCount != null) rooms.value = roomsCount;
//     if (currencyCode != null) currency.value = currencyCode;
//     if (searchRadiusKm != null) radiusKm.value = searchRadiusKm;
//     if (maxResultsCount != null) maxResults.value = maxResultsCount;
//   }
//
//   /// Clear search results
//   void clearResults() {
//     hotels.clear();
//     nearestHotel.value = null;
//     searchArea.value = null;
//     totalResults.value = 0;
//     searchResponse.value = null;
//     error.value = '';
//   }
//
//   /// Get hotel by code
//   GeoLocationHotel? getHotelByCode(String hotelCode) {
//     return hotels.firstWhereOrNull((hotel) => hotel.hotelCode == hotelCode);
//   }
//
//   /// Sort hotels by distance
//   void sortHotelsByDistance() {
//     hotels.sort((a, b) {
//       final distanceA = a.distanceKm ?? double.infinity;
//       final distanceB = b.distanceKm ?? double.infinity;
//       return distanceA.compareTo(distanceB);
//     });
//   }
//
//   /// Sort hotels by price (lowest first)
//   void sortHotelsByPrice() {
//     hotels.sort((a, b) {
//       final priceA = a.rooms?.isNotEmpty == true
//           ? a.rooms!.first.totalFare ?? double.infinity
//           : double.infinity;
//       final priceB = b.rooms?.isNotEmpty == true
//           ? b.rooms!.first.totalFare ?? double.infinity
//           : double.infinity;
//       return priceA.compareTo(priceB);
//     });
//   }
//
//   /// Filter hotels by star rating
//   void filterHotelsByStarRating(int minStars) {
//     final filteredHotels = searchResponse.value?.data?.hotels?.where((hotel) {
//           return (hotel.starRating ?? 0) >= minStars;
//         }).toList() ??
//         [];
//
//     hotels.value = filteredHotels;
//   }
//
//   /// Check if location permission is granted
//   Future<bool> isLocationPermissionGranted() async {
//     LocationPermission permission = await Geolocator.checkPermission();
//     return permission == LocationPermission.always ||
//         permission == LocationPermission.whileInUse;
//   }
//
//   /// Request location permission
//   Future<bool> requestLocationPermission() async {
//     LocationPermission permission = await Geolocator.requestPermission();
//     return permission == LocationPermission.always ||
//         permission == LocationPermission.whileInUse;
//   }
// }
