import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../../core/core.dart';
import '../../../core/controllers/settings/settings_app_controller.dart';
import '../models/hotel_models.dart';
import '../models/hotel_search_request.dart';
import '../models/hotels_data_search.dart';
import '../services/hotels_service.dart';
import '../../settings/controllers/settings_controller.dart';

// خيارات الترتيب
enum SortOption {
  priceAsc,
  priceDesc,
  rating,
  distance,
  popularity,
  availability
}

// حالات التطبيق
abstract class HotelsStatePlus {}

class HotelsInitialStatePlus extends HotelsStatePlus {}

class HotelsLoadingStatePlus extends HotelsStatePlus {}

class HotelsLoadedStatePlus extends HotelsStatePlus {
  final List<HotelPlus> hotels;
  final List<HotelPlus> filteredHotels;
  final int totalAvailable;
  final int totalRequested;

  HotelsLoadedStatePlus({
    required this.hotels,
    required this.filteredHotels,
    required this.totalAvailable,
    required this.totalRequested,
  });

  HotelsLoadedStatePlus copyWith({
    List<HotelPlus>? hotels,
    List<HotelPlus>? filteredHotels,
    int? totalAvailable,
    int? totalRequested,
  }) {
    return HotelsLoadedStatePlus(
      hotels: hotels ?? this.hotels,
      filteredHotels: filteredHotels ?? this.filteredHotels,
      totalAvailable: totalAvailable ?? this.totalAvailable,
      totalRequested: totalRequested ?? this.totalRequested,
    );
  }
}

class HotelsErrorStatePlus extends HotelsStatePlus {
  final String message;
  HotelsErrorStatePlus(this.message);
}

class HotelsControllerPlus extends GetxController {
  static HotelsControllerPlus get to => Get.find<HotelsControllerPlus>();

  // الخدمات
  final HotelsServicePlus _hotelsService = HotelsServicePlus.instance;

  // الحالة الحالية
  final Rx<HotelsStatePlus> _state =
      Rx<HotelsStatePlus>(HotelsInitialStatePlus());
  HotelsStatePlus get state => _state.value;

  // حفظ آخر موقع بحث لتجنب إعادة تعيين الموقع عند تغيير العملة
  HotelSearchRequest? _lastSearchRequest;

  // الفلاتر
  final RxBool _showFreeCancellationOnly = false.obs;
  final RxBool _showBreakfastIncludedOnly = false.obs;
  final RxBool _showRoomOnlyFilter = false.obs;
  final RxBool _showHalfBoardFilter = false.obs;
  final RxBool _showWiFiFilter = false.obs;
  final RxBool _showPoolFilter = false.obs;
  final RxInt _selectedStarRating = 0.obs;
  final RxDouble _minPriceFilter = 0.0.obs;
  final RxDouble _maxPriceFilter = 1000.0.obs;
  final RxDouble _dynamicMinPrice = 0.0.obs; // الحد الأدنى الديناميكي
  final RxDouble _dynamicMaxPrice = 1000.0.obs; // الحد الأقصى الديناميكي
  final RxList<String> _selectedFacilities = <String>[].obs;
  // Removed _includeTaxesAndFees - always use totalFare
  final RxString _sortBy = 'price_low_to_high'
      .obs; // price_low_to_high, price_high_to_low, star_rating, name

  // متغيرات الترتيب المتقدم
  final Rx<SortOption> _currentSort = SortOption.priceAsc.obs;

  // متغيرات حفظ الفلاتر
  final RxMap<String, dynamic> _savedFilters = <String, dynamic>{}.obs;

  // متغيرات Pull-to-Refresh
  final RxBool _isRefreshing = false.obs;

  // Getters للحالة
  bool get isLoading => state is HotelsLoadingStatePlus;
  bool get hasError => state is HotelsErrorStatePlus;
  bool get hasData => state is HotelsLoadedStatePlus;

  List<HotelPlus> get hotels {
    if (state is HotelsLoadedStatePlus) {
      return (state as HotelsLoadedStatePlus).filteredHotels;
    }
    return [];
  }

  int get totalHotels {
    if (state is HotelsLoadedStatePlus) {
      return (state as HotelsLoadedStatePlus).totalAvailable;
    }
    return 0;
  }

  String? get errorMessage {
    if (state is HotelsErrorStatePlus) {
      return (state as HotelsErrorStatePlus).message;
    }
    return null;
  }

  // البيانات الحقيقية من HotelsData
  HotelsDataSearch? get _hotelsData {
    try {
      return Get.find<HotelsDataSearch>();
    } catch (e) {
      return null;
    }
  }

  // عدد البالغين الحقيقي
  int get adults {
    final hotelsData = _hotelsData;
    if (hotelsData?.request?.rooms != null) {
      int totalAdults = 0;
      for (var room in hotelsData!.request!.rooms) {
        totalAdults += room.adults.length;
      }
      return totalAdults > 0 ? totalAdults : 2;
    }
    return 2;
  }

  // عدد الأطفال الحقيقي
  int get children {
    final hotelsData = _hotelsData;
    if (hotelsData?.request?.rooms != null) {
      int totalChildren = 0;
      for (var room in hotelsData!.request!.rooms) {
        totalChildren += room.children.length;
      }
      return totalChildren;
    }
    return 0;
  }

  // عدد الغرف الحقيقي
  int get rooms {
    final hotelsData = _hotelsData;
    return hotelsData?.request?.rooms.length ?? 1;
  }

  // اسم الموقع الحقيقي
  final RxString _locationName = 'Cairo, Egypt'.obs;
  String get locationName => _locationName.value;

  void _updateLocationName() {
    try {
      String? locationName;

      // 1. محاولة الحصول على الموقع من arguments (الشاشة الرئيسية)
      final arguments = Get.arguments;
      if (arguments != null && arguments is Map<String, dynamic>) {
        final searchRequest = arguments['searchRequest'];
        if (searchRequest?.locationName != null) {
          locationName = searchRequest.locationName;
          print('📍 Location name from arguments: $locationName');
        }
      }

      // 2. محاولة الحصول على الموقع من HotelsData
      if (locationName == null) {
        final hotelsData = _hotelsData;
        if (hotelsData?.request?.location?.name != null) {
          locationName = hotelsData?.request?.location?.name;
          print('📍 Location name from HotelsData: $locationName');
        }
      }

      // 3. استخدام الموقع الافتراضي إذا لم نجد شيء
      _locationName.value = locationName ?? 'Cairo, Egypt';
      print('📍 Final location name: ${_locationName.value}');
    } catch (e) {
      print('❌ Error updating location name: $e');
      _locationName.value = 'Cairo, Egypt';
    }
  }

  // Getters للفلاتر
  bool get showFreeCancellationOnly => _showFreeCancellationOnly.value;
  bool get showBreakfastIncludedOnly => _showBreakfastIncludedOnly.value;
  bool get showRoomOnlyFilter => _showRoomOnlyFilter.value;
  bool get showHalfBoardFilter => _showHalfBoardFilter.value;
  bool get showWiFiFilter => _showWiFiFilter.value;
  bool get showPoolFilter => _showPoolFilter.value;
  int get selectedStarRating => _selectedStarRating.value;
  double get minPriceFilter => _minPriceFilter.value;
  double get maxPriceFilter => _maxPriceFilter.value;
  double get dynamicMinPrice => _dynamicMinPrice.value;
  double get dynamicMaxPrice => _dynamicMaxPrice.value;
  List<String> get selectedFacilities => _selectedFacilities.toList();
  // Removed includeTaxesAndFees getter - always use totalFare
  String get sortBy => _sortBy.value;

  // Getters للميزات الجديدة
  SortOption get currentSort => _currentSort.value;
  bool get isRefreshing => _isRefreshing.value;
  Map<String, dynamic> get savedFilters => _savedFilters;

  @override
  void onInit() {
    super.onInit();
    _handleArgumentsFromHomeScreen(); // 🆕 معالجة البيانات من الشاشة الرئيسية
    _updateLocationName();
    loadSavedFilters();
  }

  /// معالجة البيانات المرسلة من الشاشة الرئيسية
  void _handleArgumentsFromHomeScreen() {
    try {
      final arguments = Get.arguments;
      if (arguments != null && arguments is Map<String, dynamic>) {
        final searchRequest = arguments['searchRequest'];
        if (searchRequest != null) {
          print(
              '🏠 Received search request from home screen: ${searchRequest.location}');
          // تحديث اسم الموقع من البيانات المرسلة
          if (searchRequest.location != null) {
            _locationName.value = searchRequest.location!;
            print('📍 Updated location name to: ${_locationName.value}');
          }
        }
      }
    } catch (e) {
      print('⚠️ Error handling arguments from home screen: $e');
    }
  }

  /// البحث باستخدام البيانات من الشاشة الرئيسية
  Future<void> _searchWithHomeScreenData(dynamic searchRequest) async {
    try {
      print('🏠 Searching with home screen data... $searchRequest');

      // تحويل searchRequest إلى HotelSearchRequest
      final request = HotelSearchRequest(
          latitude: searchRequest.latitude ??
              24.7136, // استخدام الموقع الفعلي أو الرياض كافتراضي
          longitude: searchRequest.longitude ?? 46.6753,
          checkIn: searchRequest.checkIn ??
              DateTime.now().add(const Duration(days: 1)),
          checkOut: searchRequest.checkOut ??
              DateTime.now().add(const Duration(days: 2)),
          adults: searchRequest.adults ?? 2,
          children: searchRequest.children ?? 0,
          rooms: searchRequest.rooms ?? 1,
          currency: _getCurrentCurrency(),
          paxRooms: HotelsDataSearch.to.request!.paxRooms);

      print(
          '🏠 Using coordinates from home screen: ${request.latitude}, ${request.longitude}');

      // ✅ حفظ آخر طلب بحث قبل التنفيذ
      _lastSearchRequest = request;
      print(
          '💾 Saved search request from home screen: ${request.latitude}, ${request.longitude}');

      // تنفيذ البحث
      await _performSearch(request);
    } catch (e) {
      print('❌ Error searching with home screen data: $e');
      _state.value = HotelsErrorStatePlus('Failed to search hotels: $e');
    }
  }

  /// البحث عن الفنادق باستخدام البيانات من HotelsData أو arguments
  Future<void> searchHotelsWithData() async {
    try {
      // ثانياً: محاولة الحصول على البيانات من HotelsData
      HotelsDataSearch? hotelsData;
      try {
        hotelsData = Get.find<HotelsDataSearch>();
      } catch (e) {
        print('⚠️ HotelsData not found, using default search');
        return;
      }

      print('🔍 Starting hotel search with existing data...');
      print('💱 🔍 Checking currency in searchHotelsWithData...');

      // تحديث الحالة إلى التحميل
      _state.value = HotelsLoadingStatePlus();

      // // أولاً: محاولة استخدام البيانات من arguments (من الشاشة الرئيسية)
      // final arguments = Get.arguments;
      // if (arguments != null && arguments is Map<String, dynamic>) {
      //   final searchRequest = arguments['searchRequest'];
      //   if (searchRequest != null) {
      //     print('🏠 Using search request from home screen ${searchRequest}');
      //     await _searchWithHomeScreenData(searchRequest);
      //     return;
      //   }
      // }

      final hotelRequest = hotelsData.request;

      if (hotelRequest == null) {
        print('⚠️ No hotel request found, using default search');
        return;
      }

      // التحقق من وجود الموقع
      if (hotelRequest.location?.latitude == null ||
          hotelRequest.location?.longitude == null) {
        print('⚠️ No location data found, using default search');
        return;
      }

      print(
          '📍 Using location: ${hotelRequest.location!.latitude}, ${hotelRequest.location!.longitude}');

      // حساب عدد البالغين والأطفال من الغرف
      int totalAdults = 0;
      int totalChildren = 0;

      for (var room in hotelRequest.rooms) {
        totalAdults += room.adults.length;
        totalChildren += room.children.length;
      }

      print('👥 Guests: $totalAdults adults, $totalChildren children');
      print(
          '📅 Dates: ${hotelRequest.stay?.checkIn} to ${hotelRequest.stay?.checkOut}');

      // إنشاء طلب البحث
      print('💱 🔍 About to call _getCurrentCurrency()...');
      final currency = _getCurrentCurrency();
      print('💱 🔍 Got currency: $currency');

      final request = HotelSearchRequest(
          latitude: hotelRequest.location!.latitude,
          longitude: hotelRequest.location!.longitude,
          checkIn: hotelRequest.stay!.checkIn!,
          checkOut: hotelRequest.stay!.checkOut!,
          adults: totalAdults,
          children: totalChildren,
          rooms: hotelRequest.rooms.length,
          currency: currency,
          limit: 100,
          paxRooms: HotelsDataSearch.to.request!.paxRooms);

      // ✅ حفظ آخر طلب بحث قبل التنفيذ
      _lastSearchRequest = request;
      print(
          '💾 Saved search request: ${request.latitude}, ${request.longitude}');

      await _performSearch(request);
    } catch (e) {
      print('❌ Search error: $e');
      _state.value = HotelsErrorStatePlus('An unexpected error occurred');
    }
  }

  /// تنفيذ البحث الفعلي
  Future<void> _performSearch(HotelSearchRequest request) async {
    try {
      print('📍 Search location: ${request.latitude}, ${request.longitude}');

      // ✅ تحديث اسم الموقع بعد البحث الناجح
      _updateLocationName();

      // ملاحظة: _lastSearchRequest يتم حفظه الآن في نقاط الدخول قبل استدعاء هذه الدالة
      _state.value = HotelsLoadingStatePlus();
      // البحث عن الفنادق
      final response = await _hotelsService.searchHotelsByLocation(request);

      if (response.success) {
        print('✅ Search successful: ${response.hotels.length} hotels found');

        // تحديث الحالة مع النتائج
        final loadedState = HotelsLoadedStatePlus(
          hotels: response.hotels,
          filteredHotels: response.hotels,
          totalAvailable: response.totalAvailable,
          totalRequested: response.totalRequested,
        );

        _state.value = loadedState;

        _calculateDynamicPriceRange(); // حساب نطاق السعر الديناميكي
        _applyFilters();
      } else {
        print('❌ Search failed: ${response.message}');
        _state.value =
            HotelsErrorStatePlus(response.message ?? 'Search failed');
      }
    } catch (e) {
      print('❌ Search error: $e');
      _state.value = HotelsErrorStatePlus('An unexpected error occurred');
    }
  }

  /// حساب الحد الأدنى والأقصى الديناميكي للسعر من الفنادق المحملة
  void _calculateDynamicPriceRange() {
    if (_state.value is HotelsLoadedStatePlus) {
      final loadedState = _state.value as HotelsLoadedStatePlus;
      final hotels = loadedState.hotels;

      if (hotels.isNotEmpty) {
        try {
          // حساب جميع الأسعار (استخدام minPrice دائماً - السعر النهائي)
          final prices = hotels.map((hotel) {
            return hotel.minPrice; // أقل سعر نهائي للفندق
          }).toList();

          // حساب الحد الأدنى والأقصى
          final minPrice = prices.reduce((a, b) => a < b ? a : b);
          final maxPrice = prices.reduce((a, b) => a > b ? a : b);

          // حساب الحد الأدنى الديناميكي (تقريب إلى أسفل لأقرب 10)
          final dynamicMin = (minPrice / 10).floor() * 10.0;
          _dynamicMinPrice.value = dynamicMin < 0 ? 0.0 : dynamicMin;

          // حساب الحد الأقصى الديناميكي (إضافة هامش 10% وتقريب إلى أقرب 50)
          final dynamicMax = ((maxPrice * 1.1) / 50).ceil() * 50.0;
          _dynamicMaxPrice.value = dynamicMax < 100 ? 100 : dynamicMax;

          print(
              '💰 Dynamic price range calculated: ${_dynamicMinPrice.value} - ${_dynamicMaxPrice.value} $currency');
          print('💰 Based on hotel prices: $minPrice - $maxPrice $currency');

          // تحديث فلتر السعر بالكامل عند تغيير العملة أو التحميل الأول
          final shouldUpdateFilter = _maxPriceFilter.value == 1000.0 ||
              _maxPriceFilter.value > _dynamicMaxPrice.value * 1.5 ||
              _minPriceFilter.value != _dynamicMinPrice.value;

          if (shouldUpdateFilter) {
            _minPriceFilter.value = _dynamicMinPrice.value;
            _maxPriceFilter.value = _dynamicMaxPrice.value;
            print(
                '💰 Updated price filter range to: ${_minPriceFilter.value} - ${_maxPriceFilter.value} $currency');
          }
        } catch (e) {
          print('⚠️ Error calculating dynamic price range: $e');
          _dynamicMinPrice.value = 0.0;
          _dynamicMaxPrice.value = 1000.0;
        }
      } else {
        _dynamicMinPrice.value = 0.0;
        _dynamicMaxPrice.value = 1000.0; // القيمة الافتراضية عند عدم وجود فنادق
      }
    } else {
      _dynamicMinPrice.value = 0.0;
      _dynamicMaxPrice.value = 1000.0; // القيمة الافتراضية
    }
  }

  /// تطبيق الفلاتر
  void _applyFilters() {
    if (state is! HotelsLoadedStatePlus) return;

    final currentState = state as HotelsLoadedStatePlus;
    var filtered = List<HotelPlus>.from(currentState.hotels);

    // فلتر الإلغاء المجاني
    if (_showFreeCancellationOnly.value) {
      filtered = filtered.where((hotel) => hotel.hasFreeCancellation).toList();
    }

    // فلتر الإفطار
    if (_showBreakfastIncludedOnly.value) {
      filtered = filtered.where((hotel) => hotel.hasBreakfast).toList();
    }

    // فلتر الغرفة فقط
    if (_showRoomOnlyFilter.value) {
      filtered = filtered
          .where((hotel) =>
              hotel.availability?.rooms.any((room) => room.hasRoomOnly) ??
              false)
          .toList();
    }

    // فلتر نصف الإقامة
    if (_showHalfBoardFilter.value) {
      filtered = filtered
          .where((hotel) =>
              hotel.availability?.rooms.any((room) => room.hasHalfBoard) ??
              false)
          .toList();
    }

    // فلتر WiFi
    if (_showWiFiFilter.value) {
      filtered = filtered
          .where((hotel) => hotel.facilities.any((facility) =>
              facility.toLowerCase().contains('wifi') ||
              facility.toLowerCase().contains('internet')))
          .toList();
    }

    // فلتر المسبح
    if (_showPoolFilter.value) {
      filtered = filtered
          .where((hotel) => hotel.facilities.any((facility) =>
              facility.toLowerCase().contains('pool') ||
              facility.toLowerCase().contains('swimming')))
          .toList();
    }

    // فلتر النجوم
    if (_selectedStarRating.value > 0) {
      filtered = filtered
          .where((hotel) => hotel.starRating >= _selectedStarRating.value)
          .toList();
    }

    // فلتر السعر
    filtered = filtered.where((hotel) {
      final price = hotel.minPrice; // استخدام السعر النهائي دائماً
      return price >= _minPriceFilter.value && price <= _maxPriceFilter.value;
    }).toList();

    // فلتر المرافق المحددة
    if (_selectedFacilities.isNotEmpty) {
      filtered = filtered
          .where((hotel) => _selectedFacilities.every((selectedFacility) =>
              hotel.facilities.any((facility) => facility
                  .toLowerCase()
                  .contains(selectedFacility.toLowerCase()))))
          .toList();
    }

    // ترتيب النتائج
    _sortHotels(filtered);

    // تحديث الحالة مع النتائج المفلترة
    _state.value = currentState.copyWith(filteredHotels: filtered);
  }

  /// ترتيب الفنادق
  void _sortHotels(List<HotelPlus> hotels) {
    switch (_sortBy.value) {
      case 'price_low_to_high':
        hotels.sort((a, b) => a.minPrice.compareTo(b.minPrice));
        break;
      case 'price_high_to_low':
        hotels.sort((a, b) => b.minPrice.compareTo(a.minPrice));
        break;
      case 'star_rating':
        hotels.sort((a, b) => b.starRating.compareTo(a.starRating));
        break;
      case 'name':
        hotels.sort((a, b) => a.name.compareTo(b.name));
        break;
      default:
        // ترتيب افتراضي: أقل سعر أولاً
        hotels.sort((a, b) => a.minPrice.compareTo(b.minPrice));
    }
  }

  /// تبديل فلتر الإلغاء المجاني
  void toggleFreeCancellationFilter() {
    _showFreeCancellationOnly.value = !_showFreeCancellationOnly.value;
    _applyFilters();
  }

  /// تبديل فلتر الإفطار
  void toggleBreakfastFilter() {
    _showBreakfastIncludedOnly.value = !_showBreakfastIncludedOnly.value;
    _applyFilters();
  }

  /// تبديل فلتر الغرفة فقط
  void toggleRoomOnlyFilter() {
    _showRoomOnlyFilter.value = !_showRoomOnlyFilter.value;
    _applyFilters();
  }

  /// تبديل فلتر نصف الإقامة
  void toggleHalfBoardFilter() {
    _showHalfBoardFilter.value = !_showHalfBoardFilter.value;
    _applyFilters();
  }

  /// تبديل فلتر WiFi
  void toggleWiFiFilter() {
    _showWiFiFilter.value = !_showWiFiFilter.value;
    _applyFilters();
  }

  /// تبديل فلتر المسبح
  void togglePoolFilter() {
    _showPoolFilter.value = !_showPoolFilter.value;
    _applyFilters();
  }

  /// تحديد تقييم النجوم
  void setStarRating(int rating) {
    _selectedStarRating.value = rating;
    _applyFilters();
  }

  /// تحديد نطاق السعر
  void setPriceRange(double min, double max) {
    _minPriceFilter.value = min;
    _maxPriceFilter.value = max;
    _applyFilters();
  }

  // Removed toggleIncludeTaxesAndFees - always use final prices

  /// تحديد طريقة الترتيب
  void setSortBy(String sortType) {
    _sortBy.value = sortType;
    _applyFilters();
  }

  /// إضافة/إزالة مرفق من الفلاتر
  void toggleFacility(String facility) {
    if (_selectedFacilities.contains(facility)) {
      _selectedFacilities.remove(facility);
    } else {
      _selectedFacilities.add(facility);
    }
    _applyFilters();
  }

  /// إعادة تعيين جميع الفلاتر
  void resetFilters() {
    _showFreeCancellationOnly.value = false;
    _showBreakfastIncludedOnly.value = false;
    _showRoomOnlyFilter.value = false;
    _showHalfBoardFilter.value = false;
    _showWiFiFilter.value = false;
    _showPoolFilter.value = false;
    _selectedStarRating.value = 0;
    _minPriceFilter.value = _dynamicMinPrice.value;
    _maxPriceFilter.value = _dynamicMaxPrice.value;
    _selectedFacilities.clear();
    // Removed includeTaxesAndFees reset - always use final prices
    _sortBy.value = 'price_low_to_high';
    _applyFilters();
  }

  /// الحصول على العملة الحالية من الإعدادات (public getter)
  String get currency => _getCurrentCurrency();

  /// الحصول على العملة من فندق محدد (من PricingPlus إذا متوفر)
  String getCurrencyForHotel(HotelPlus hotel) {
    final firstRoom = hotel.availability!.rooms.first;
    var currency = firstRoom.pricing?.converted.currency;
    // Fallback to hotel currency or default
    return currency ?? _getCurrentCurrency();
  }

  /// الحصول على العملة الحالية من الإعدادات
  String _getCurrentCurrency() {
    try {
      // Method 1: Try SettingsController from feature_plus first (PRIORITY)
      if (Get.isRegistered<SettingsController>()) {
        final settingsController = Get.find<SettingsController>();
        final currency = settingsController.appSettings.currency;
        if (currency.isNotEmpty) {
          print(
              '💱 ✅ Got currency from SettingsController (feature_plus): $currency');
          return currency;
        }
      }

      // Method 2: Try SettingsAppController as fallback
      if (Get.isRegistered<SettingsAppController>()) {
        final settingsAppController = Get.find<SettingsAppController>();
        final currency = settingsAppController.currency?.currency;
        if (currency != null && currency.isNotEmpty) {
          print(
              '💱 ✅ Got currency from SettingsAppController (fallback): $currency');
          return currency;
        }
      }

      print('💱 ⚠️ No settings controller found, using USD default');
      return 'USD';
    } catch (e) {
      print('💱 ⚠️ Error getting currency: $e, using USD default');
      return 'USD';
    }
  }

  /// إعادة تعيين فلتر السعر عند تغيير العملة
  void _resetPriceFilterForCurrencyChange() {
    print('💱 Resetting price filter for currency change...');

    // إعادة تعيين الحد الأدنى والأقصى
    _minPriceFilter.value = 0.0;
    _maxPriceFilter.value = 1000.0; // قيمة مؤقتة
    _dynamicMinPrice.value = 0.0; // قيمة مؤقتة
    _dynamicMaxPrice.value = 1000.0; // قيمة مؤقتة

    print('💱 Price filter reset: min=0, max=1000 (temporary)');
  }

  /// إعادة البحث عند تغيير العملة
  Future<void> refreshSearchWithNewCurrency() async {
    print('💱 Currency changed, refreshing hotel search...');
    print(
        '💱 Current _lastSearchRequest: ${_lastSearchRequest?.latitude}, ${_lastSearchRequest?.longitude}');

    // إعادة تعيين فلتر السعر للعملة الجديدة
    _resetPriceFilterForCurrencyChange();

    // إعادة البحث باستخدام العملة الجديدة مع الحفاظ على الموقع
    if (_lastSearchRequest != null) {
      // استخدام آخر طلب بحث مع تحديث العملة فقط
      final updatedRequest = HotelSearchRequest(
          latitude: _lastSearchRequest!.latitude,
          longitude: _lastSearchRequest!.longitude,
          checkIn: _lastSearchRequest!.checkIn,
          checkOut: _lastSearchRequest!.checkOut,
          adults: _lastSearchRequest!.adults,
          children: _lastSearchRequest!.children,
          rooms: _lastSearchRequest!.rooms,
          currency: _getCurrentCurrency(), // العملة الجديدة فقط
          limit: _lastSearchRequest!.limit,
          paxRooms: HotelsDataSearch.to.request!.paxRooms);

      print(
          '💱 Using last search location: ${updatedRequest.latitude}, ${updatedRequest.longitude}');
      await _performSearch(updatedRequest);
    } else {
      // محاولة الحصول على البيانات من arguments أو HotelsData
      final arguments = Get.arguments;
      if (arguments != null && arguments is Map<String, dynamic>) {
        final searchRequest = arguments['searchRequest'];
        if (searchRequest != null &&
            searchRequest.latitude != null &&
            searchRequest.longitude != null) {
          print(
              '💱 Using coordinates from arguments: ${searchRequest.latitude}, ${searchRequest.longitude}');

          final request = HotelSearchRequest(
              latitude: searchRequest.latitude,
              longitude: searchRequest.longitude,
              checkIn: searchRequest.checkIn,
              checkOut: searchRequest.checkOut,
              adults: searchRequest.adults ?? 2,
              children: searchRequest.children ?? 0,
              rooms: searchRequest.rooms ?? 1,
              currency: _getCurrentCurrency(),
              paxRooms: HotelsDataSearch.to.request!.paxRooms);

          await _performSearch(request);
          return;
        }
      }

      // محاولة الحصول على البيانات من HotelsData
      try {
        final hotelsData = Get.find<HotelsDataSearch>();
        final hotelRequest = hotelsData.request;

        if (hotelRequest?.location?.latitude != null &&
            hotelRequest?.location?.longitude != null) {
          print(
              '💱 Using coordinates from HotelsData: ${hotelRequest!.location!.latitude}, ${hotelRequest.location!.longitude}');

          // حساب عدد البالغين والأطفال من الغرف
          int totalAdults = 0;
          int totalChildren = 0;

          for (var room in hotelRequest.rooms) {
            totalAdults += (room.adults as num).round();
            totalChildren += room.children.length;
          }

          final request = HotelSearchRequest(
              latitude: hotelRequest.location!.latitude,
              longitude: hotelRequest.location!.longitude,
              checkIn: hotelRequest.stay?.checkIn ??
                  DateTime.now().add(const Duration(days: 1)),
              checkOut: hotelRequest.stay?.checkOut ??
                  DateTime.now().add(const Duration(days: 2)),
              adults: totalAdults > 0 ? totalAdults : 2,
              children: totalChildren,
              rooms: hotelRequest.rooms.length,
              currency: _getCurrentCurrency(),
              limit: 100,
              paxRooms: HotelsDataSearch.to.request!.paxRooms);

          await _performSearch(request);
          return;
        }
      } catch (e) {
        print('⚠️ Could not get data from HotelsData: $e');
      }

      // آخر حل: استخدام البحث الافتراضي (جدة)
      print('⚠️ No location data available, using default search');
    }

    // إعادة حساب الحد الأقصى الديناميكي بعد تغيير العملة
    // هذا سيحدث تلقائياً في _performSearch عبر _calculateDynamicMaxPrice
    print(
        '💱 Currency change completed, new max price: ${_dynamicMaxPrice.value} $currency');

    // تحديث واجهة المستخدم لضمان عرض القيم الجديدة
    update();
  }

  /// إعادة تحميل البيانات
  // @override
  // Future<void> refresh() async {
  //   // return refreshHotels();
  // }

  // ===== الميزات الجديدة =====

  /// Pull-to-Refresh المحسن
  Future<void> refreshHotels() async {
    if (_isRefreshing.value) return;

    _isRefreshing.value = true;
    try {
      await searchHotelsWithData();
    } finally {
      _isRefreshing.value = false;
    }
  }

  /// ترتيب الفنادق
  void sortHotels(SortOption option) {
    _currentSort.value = option;
    _sortFilteredHotels();
  }

  void _sortFilteredHotels() {
    if (state is! HotelsLoadedStatePlus) return;

    final currentState = state as HotelsLoadedStatePlus;
    final hotels = List<HotelPlus>.from(currentState.filteredHotels);

    switch (_currentSort.value) {
      case SortOption.priceAsc:
        hotels.sort((a, b) => a.minPrice.compareTo(b.minPrice));
        break;
      case SortOption.priceDesc:
        hotels.sort((a, b) => b.minPrice.compareTo(a.minPrice));
        break;
      case SortOption.rating:
        hotels.sort((a, b) => b.starRating.compareTo(a.starRating));
        break;
      case SortOption.distance:
        // يمكن إضافة منطق المسافة لاحقاً
        break;
      case SortOption.popularity:
        // يمكن إضافة منطق الشعبية لاحقاً
        break;
      case SortOption.availability:
        hotels.sort((a, b) => (b.availability?.totalRooms ?? 0)
            .compareTo(a.availability?.totalRooms ?? 0));
        break;
    }

    _state.value = currentState.copyWith(filteredHotels: hotels);
  }

  /// حفظ الفلاتر المفضلة
  Future<void> saveCurrentFilters() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final filtersMap = {
        'freeCancellation': _showFreeCancellationOnly.value,
        'breakfast': _showBreakfastIncludedOnly.value,
        'roomOnly': _showRoomOnlyFilter.value,
        'halfBoard': _showHalfBoardFilter.value,
        'wifi': _showWiFiFilter.value,
        'pool': _showPoolFilter.value,
        'starRating': _selectedStarRating.value,
        'minPrice': _minPriceFilter.value,
        'maxPrice': _maxPriceFilter.value,
        'facilities': _selectedFacilities.toList(),
        // Removed includeTaxes from saved filters
        'sortBy': _sortBy.value,
        'currentSort': _currentSort.value.index,
      };

      _savedFilters.value = filtersMap;
      await prefs.setString('saved_hotel_filters', jsonEncode(filtersMap));

      Core.showGlobalSnackBar(
        'تم الحفظ: تم حفظ الفلاتر المفضلة بنجاح',
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Core.showGlobalSnackBar(
        'خطأ: فشل في حفظ الفلاتر',
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// تحميل الفلاتر المحفوظة
  Future<void> loadSavedFilters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedFiltersString = prefs.getString('saved_hotel_filters');

      if (savedFiltersString != null) {
        final filtersMap =
            jsonDecode(savedFiltersString) as Map<String, dynamic>;
        _savedFilters.value = filtersMap;

        _showFreeCancellationOnly.value =
            filtersMap['freeCancellation'] ?? false;
        _showBreakfastIncludedOnly.value = filtersMap['breakfast'] ?? false;
        _showRoomOnlyFilter.value = filtersMap['roomOnly'] ?? false;
        _showHalfBoardFilter.value = filtersMap['halfBoard'] ?? false;
        _showWiFiFilter.value = filtersMap['wifi'] ?? false;
        _showPoolFilter.value = filtersMap['pool'] ?? false;
        _selectedStarRating.value = filtersMap['starRating'] ?? 0;
        _minPriceFilter.value =
            filtersMap['minPrice'] ?? _dynamicMinPrice.value;
        _maxPriceFilter.value =
            filtersMap['maxPrice'] ?? _dynamicMaxPrice.value;
        _selectedFacilities.value =
            List<String>.from(filtersMap['facilities'] ?? []);
        // Removed includeTaxes restoration - always use final prices
        _sortBy.value = filtersMap['sortBy'] ?? 'price_low_to_high';
        _currentSort.value = SortOption.values[filtersMap['currentSort'] ?? 0];

        // تطبيق الفلاتر
        _applyFilters();

      }
    } catch (e) {
      // فشل في تحميل الفلاتر المحفوظة
    }
  }

  /// حذف الفلاتر المحفوظة
  Future<void> clearSavedFilters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('saved_hotel_filters');
      _savedFilters.clear();

      Core.showGlobalSnackBar(
        'تم الحذف: تم حذف الفلاتر المحفوظة',
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Core.showGlobalSnackBar(
        'خطأ: فشل في حذف الفلاتر',
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// التحقق من وجود فلاتر محفوظة
  bool get hasSavedFilters => _savedFilters.isNotEmpty;

  /// الحصول على نص وصف الترتيب الحالي
  String get currentSortDescription {
    switch (_currentSort.value) {
      case SortOption.priceAsc:
        return 'السعر: من الأقل للأعلى';
      case SortOption.priceDesc:
        return 'السعر: من الأعلى للأقل';
      case SortOption.rating:
        return 'التقييم: الأعلى أولاً';
      case SortOption.distance:
        return 'المسافة: الأقرب أولاً';
      case SortOption.popularity:
        return 'الشعبية: الأكثر شعبية';
      case SortOption.availability:
        return 'التوفر: الأكثر توفراً';
    }
  }
}
