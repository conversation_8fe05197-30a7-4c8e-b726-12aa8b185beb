import 'package:fandooq/feature_plus/feature_plus.dart';
import 'package:fandooq/feature_plus/home/<USER>/guests_search.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/core.dart';
import '../../../core/components/logger.dart';
import '../../../core/models/hotels/hotels.dart';
import '../../user/controllers/user_controller_plus.dart';
import '../../favorites/controllers/favorites_controller_plus.dart';
import '../controllers/hotels_controller_plus.dart';
import '../models/hotel_models.dart';
import '../models/hotel_search_request.dart';
import '../models/hotels_data_search.dart';
import '../models/pre_booking_models.dart';
import '../views/pre_booking_screen_plus.dart';
import '../services/deep_link_service.dart';
import '../services/hotels_service.dart';
import '../../settings/controllers/settings_controller.dart';

class HotelDetailsControllerPlus extends GetxController {
  final HotelsServicePlus _hotelsService = HotelsServicePlus.instance;

  static HotelDetailsControllerPlus get to => Get.find();

  // Observable variables
  final _isLoading = false.obs;
  final _hotel = Rxn<HotelPlus>();
  final _selectedRoom = Rxn<RoomPlus>();
  final _selectedImageIndex = 0.obs;
  final _showAllAmenities = false.obs;
  final _showAllFacilities = false.obs;
  final _includeTaxes = true.obs;
  bool _fromDeepLink = false;
  final _selectedTab = 0.obs;
  final _isSearching = false.obs;

  // Search parameters
  final _searchCheckIn = Rxn<DateTime>();
  final _searchCheckOut = Rxn<DateTime>();
  final _searchAdults = 2.obs;
  final _searchChildren = 0.obs;
  final _searchRooms = 1.obs;
  final _searchPaxRooms = <PaxRoom>[].obs;

  // Original search parameters (for comparison)
  DateTime? _originalCheckIn;
  DateTime? _originalCheckOut;
  int _originalAdults = 2;
  int _originalChildren = 0;
  int _originalRooms = 1;
  List<PaxRoom> _originalPaxRooms = [];

  // Scroll Controller for auto-scroll functionality
  late ScrollController scrollController;
  final GlobalKey roomsSectionKey = GlobalKey();

  // Getters
  bool get isLoading => _isLoading.value;
  HotelPlus? get hotel => _hotel.value;
  RoomPlus? get selectedRoom => _selectedRoom.value;
  int get selectedImageIndex => _selectedImageIndex.value;
  bool get showAllAmenities => _showAllAmenities.value;
  bool get showAllFacilities => _showAllFacilities.value;
  bool get includeTaxes => _includeTaxes.value;
  int get selectedTab => _selectedTab.value;
  bool get isSearching => _isSearching.value;

  // Search parameters getters
  DateTime? get searchCheckIn => _searchCheckIn.value;
  DateTime? get searchCheckOut => _searchCheckOut.value;
  int get searchAdults => _searchAdults.value;
  int get searchChildren => _searchChildren.value;
  int get searchRooms => _searchRooms.value;
  List<PaxRoom> get searchPaxRooms => _searchPaxRooms.toList();

  // Computed getters
  int get numberOfNights {
    if (searchCheckIn != null && searchCheckOut != null) {
      return searchCheckOut!.difference(searchCheckIn!).inDays;
    }
    return 1;
  }

  bool get hasSearchParamsChanged {
    return searchCheckIn != _originalCheckIn ||
        searchCheckOut != _originalCheckOut ||
        searchAdults != _originalAdults ||
        searchChildren != _originalChildren ||
        searchRooms != _originalRooms ||
        !_areListsEqual(searchPaxRooms, _originalPaxRooms);
  }

  // Hotel details
  String get hotelName => hotel?.name ?? '';
  String get hotelAddress => hotel?.address ?? '';
  int get starRating => hotel?.starRating ?? 0;
  String get description => hotel?.description ?? '';
  List<String> get images => hotel?.images ?? [];
  List<String> get amenities => hotel?.amenities ?? [];
  List<String> get facilities => hotel?.facilities ?? [];

  bool scrollToRooms = false;

  // Location details - get from hotel model, fallback to search location
  double? get latitude {
    // First try to get from hotel model
    if (hotel?.latitude != null) {
      return hotel!.latitude;
    }

    // Fallback to search location
    try {
      final hotelsData = Get.find<HotelsDataSearch>();
      return hotelsData.request?.location?.latitude;
    } catch (e) {
      // Default coordinates for testing (Jeddah)
      return 21.4925;
    }
  }

  double? get longitude {
    // First try to get from hotel model
    if (hotel?.longitude != null) {
      return hotel!.longitude;
    }

    // Fallback to search location
    try {
      final hotelsData = Get.find<HotelsDataSearch>();
      return hotelsData.request?.location?.longitude;
    } catch (e) {
      // Default coordinates for testing (Jeddah)
      return 39.1776;
    }
  }

  // Room details
  List<RoomPlus> get availableRooms => hotel?.availability?.rooms ?? [];
  bool get hasRooms => availableRooms.isNotEmpty;

  // Pricing
  num get minPrice => hotel?.minPrice ?? 0.0;
  num get maxPrice => hotel?.maxPrice ?? 0.0;

  // Get currency from selected room's pricing first, then fallback to hotel currency
  String get currency {
    // Try to get currency from selected room first
    if (selectedRoom?.pricing?.converted.currency != null) {
      return selectedRoom!.pricing!.converted.currency;
    }

    // Fallback to hotel currency
    if (hotel?.currency != null) {
      return hotel!.currency;
    }

    // Fallback to settings controller currency
    if (Get.isRegistered<SettingsController>()) {
      try {
        final settingsController = Get.find<SettingsController>();
        return settingsController.appSettings.currency;
      } catch (e) {
        Log.warning('⚠️ Could not get currency from SettingsController: $e');
      }
    }

    // Final fallback
    return 'USD';
  }

  String get getLocalCurrency {
    // Fallback to settings controller currency
    if (Get.isRegistered<SettingsController>()) {
      try {
        final settingsController = Get.find<SettingsController>();
        return settingsController.appSettings.currency;
      } catch (e) {
        Log.warning('⚠️ Could not get currency from SettingsController: $e');
      }
    }
    // Final fallback
    return 'USD';
  }

  // Features
  bool get hasFreeCancellation => hotel?.hasFreeCancellation ?? false;
  bool get hasBreakfast => hotel?.hasBreakfast ?? false;
  bool get hasWiFi => hotel?.hasWiFi ?? false;
  bool get hasPool => hotel?.hasPool ?? false;
  List<String> get promotions => hotel?.promotions ?? [];

  @override
  void onInit() {
    initializeHotel();
    super.onInit();
    scrollController = ScrollController();
    _initializeSearchParams();

    // Auto-load detailed hotel information if we have a hotel code
    // but limited data (e.g., from search results)
    if (hotel?.code != null && _shouldLoadDetailedInfo()) {
      loadHotelDetails(hotel!.code);
    }

    if (_fromDeepLink) {
      checkDeepLinkData();
    }
  }

  /// 🔗 **Deep Link Support for Hotel Details**
  ///
  /// Handles scenarios where user lands directly on hotel details via deep link
  /// without going through hotel search/list screens first.
  ///
  /// This method:
  /// - Detects if we have a hotel code but missing detailed data
  /// - Fetches hotel availability and details from TBO API
  /// - Provides graceful fallback when no geolocation context is available
  void checkDeepLinkData() {
    try {
      print('🔗 [DEEP_LINK] Checking for deep link hotel data...');

      // Check if we have a hotel code but missing essential data
      final hotelCode = hotel?.code;
      if (hotelCode != null && hotelCode.isNotEmpty) {
        print('🔗 [DEEP_LINK] Found hotel code: $hotelCode');

        // Check if we need to fetch more data
        if (_shouldFetchHotelFromDeepLink()) {
          print('🔗 [DEEP_LINK] Hotel data incomplete, fetching from API...');
          _fetchHotelFromDeepLink(hotelCode);
        } else {
          print(
              '🔗 [DEEP_LINK] Hotel data seems complete, no additional fetch needed');
        }
      } else {
        print('🔗 [DEEP_LINK] No hotel code found, skipping deep link check');
      }
    } catch (e) {
      print('❌ [DEEP_LINK] Error in checkDeepLinkData: $e');
    }
  }

  /// Check if we should fetch hotel data from deep link
  bool _shouldFetchHotelFromDeepLink() {
    if (hotel == null) return true;

    // If we're missing availability data, we should fetch
    if (hotel!.availability == null) return true;

    // If we're missing pricing data, we should fetch
    if (hotel!.pricing == null) return true;

    // If we have very basic data only (like from a deep link), fetch more
    if (hotel!.description.isEmpty && hotel!.facilities.isEmpty) return true;

    return false;
  }

  /// Fetch hotel data specifically for deep link scenarios
  Future<void> _fetchHotelFromDeepLink(String hotelCode) async {
    try {
      Log.debug('🔗 [DEEP_LINK] Starting hotel fetch for code: $hotelCode');
      _isLoading.value = true;

      // First, try to get detailed hotel information
      await loadHotelDetails(hotelCode);

      // If we still don't have availability/pricing, try to search for it
      if (hotel?.availability == null || hotel?.pricing == null) {
        await _searchHotelAvailability(hotelCode);
      }

      Log.debug('✅ [DEEP_LINK] Hotel data fetch completed successfully');
    } catch (e) {
      Log.error('❌ [DEEP_LINK] Error fetching hotel from deep link: $e');
      Core.showGlobalSnackBar(
        'تعذر تحميل بيانات الفندق',
        backgroundColor: Colors.orange,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// Search for hotel availability using hotel code and current search parameters
  Future<void> _searchHotelAvailability(String hotelCode,
      {bool useDeepLinkParams = true}) async {
    final logPrefix = useDeepLinkParams ? '[DEEP_LINK]' : '[SEARCH_UPDATE]';
    try {
      Log.debug('🔍 $logPrefix Searching hotel availability for: $hotelCode');

      // Get search parameters from current search state
      DateTime checkIn =
          searchCheckIn ?? DateTime.now().add(const Duration(days: 1));
      DateTime checkOut =
          searchCheckOut ?? DateTime.now().add(const Duration(days: 2));
      int adults = searchAdults;
      int children = searchChildren;
      int rooms = searchRooms;
      String currency = getLocalCurrency;
      List<PaxRoom> paxRooms = searchPaxRooms;

      // Only use deep link arguments if explicitly requested (for initial load)
      if (useDeepLinkParams) {
        final arguments = Get.arguments as Map<String, dynamic>?;
        if (arguments != null) {
          if (arguments['checkInDate'] != null) {
            checkIn = arguments['checkInDate'] as DateTime;
            Log.debug('🔍 [DEEP_LINK] Using checkIn from deep link: $checkIn');
          }
          if (arguments['checkOutDate'] != null) {
            checkOut = arguments['checkOutDate'] as DateTime;
            Log.debug(
                '🔍 [DEEP_LINK] Using checkOut from deep link: $checkOut');
          }
          if (arguments['adults'] != null) {
            adults = arguments['adults'] as int;
            Log.debug('🔍 [DEEP_LINK] Using adults from deep link: $adults');
          }
          if (arguments['children'] != null) {
            children = arguments['children'] as int;
            Log.debug(
                '🔍 [DEEP_LINK] Using children from deep link: $children');
          }
          if (arguments['rooms'] != null) {
            rooms = arguments['rooms'] as int;
            Log.debug('🔍 [DEEP_LINK] Using rooms from deep link: $rooms');
          }
          if (arguments['paxRooms'] != null) {
            paxRooms = arguments['paxRooms'] as List<PaxRoom>;
            Log.debug(
                '🔍 [DEEP_LINK] Using paxRooms from deep link: ${paxRooms.length} rooms');
          }
        }
      } else {
        Log.debug('🔍 [SEARCH_UPDATE] Using current search parameters');
        Log.debug('🔍 [SEARCH_UPDATE] checkIn: $checkIn, checkOut: $checkOut');
        Log.debug(
            '🔍 [SEARCH_UPDATE] adults: $adults, children: $children, rooms: $rooms');
      }

      // Fallback: Try to get search controller if available
      if (Get.isRegistered<HotelsControllerPlus>()) {
        final searchController = Get.find<HotelsControllerPlus>();
        // Only use controller rooms if not using deep link params
        if (!useDeepLinkParams) {
          rooms = searchController.rooms;
        }
      }

      // Fallback: Try to get dates from HotelsData if using deep link params but not provided
      if (useDeepLinkParams) {
        final arguments = Get.arguments as Map<String, dynamic>?;
        if (arguments?['checkInDate'] == null ||
            arguments?['checkOutDate'] == null) {
          try {
            final hotelsData = HotelsDataSearch.to;
            if (hotelsData.request?.stay != null) {
              if (arguments?['checkInDate'] == null) {
                checkIn = hotelsData.request!.stay!.checkIn ?? checkIn;
              }
              if (arguments?['checkOutDate'] == null) {
                checkOut = hotelsData.request!.stay!.checkOut ?? checkOut;
              }
            }
            if (hotelsData.request?.rooms != null &&
                arguments?['rooms'] == null) {
              rooms = hotelsData.request!.rooms.length;
            }
          } catch (e) {
            Log.warning(
                '⚠️ [DEEP_LINK] Could not get HotelsData, using defaults: $e');
          }
        }
      }

      // Create default paxRooms if not provided
      if (paxRooms.isEmpty) {
        paxRooms = _createDefaultPaxRooms(adults, children, rooms);
      }

      // Create a search request with the hotel code and deep link parameters
      final request = HotelSearchRequest(
        latitude: hotel?.latitude ?? 24.7136, // Default to Riyadh
        longitude: hotel?.longitude ?? 46.6753,
        checkIn: checkIn,
        checkOut: checkOut,
        adults: adults,
        children: children,
        rooms: rooms,
        currency: currency,
        limit: 10, // Small limit for specific hotel search
        paxRooms: paxRooms,
        hotelCode: hotelCode, // Specific hotel code
      );

      Log.debug(
          '🔍 $logPrefix Search request: checkIn=$checkIn, checkOut=$checkOut, adults=$adults, children=$children, rooms=$rooms');

      // Search for the specific hotel
      final response = await _hotelsService.searchHotelsByLocation(request);

      if (response.success && response.hotels.isNotEmpty) {
        // Find our hotel in the results
        final foundHotel = response.hotels.firstWhereOrNull(
          (h) => h.code == hotelCode,
        );

        if (foundHotel != null) {
          // Update our hotel with the found data (availability, pricing, etc.)
          _hotel.value = foundHotel;
          Log.debug('✅ $logPrefix Found hotel availability data');

        } else {
          Log.warning('⚠️ $logPrefix Hotel not found in search results');
        }
      } else {
        Log.warning('⚠️ $logPrefix No hotels found in search response');
      }
    } catch (e) {
      Log.error('❌ $logPrefix Error searching hotel availability: $e');
      // Don't show error to user as this is a fallback operation
    }
  }

  // Scroll to rooms section
  void scrollToRoomsSection() {
    if (scrollToRooms) {
      if (roomsSectionKey.currentContext != null) {
        final RenderBox renderBox =
            roomsSectionKey.currentContext!.findRenderObject() as RenderBox;
        final position = renderBox.localToGlobal(Offset.zero);

        scrollController.animateTo(
          position.dy - 100, // Offset to show section title
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  // Initialize with hotel data
  void initializeHotel() {
    _hotel.value = Get.arguments["hotel"];
    _fromDeepLink = Get.arguments["fromDeepLink"] ?? false;
    scrollToRooms = Get.arguments["scrollToRooms"];

    print('🏨 Initializing hotel details: ${_hotel.value?.name}');

    // Select first available room by default
    if (availableRooms.isNotEmpty) {
      _selectedRoom.value = availableRooms.first;
    }

    // Removed tax inclusion synchronization - always use final prices
    _includeTaxes.value = true; // Not used anymore, but kept for compatibility

    print('✅ Hotel details initialized successfully');
  }

  // Load hotel details by code
  Future<void> loadHotelDetails(String hotelCode) async {
    try {
      print('🔍 Loading hotel details for code: $hotelCode');
      _isLoading.value = true;

      // Call the API to get detailed hotel information
      final response = await _hotelsService.getHotelDetails(hotelCode);

      if (response.success && response.data != null) {
        // Convert the API response to HotelPlus and update the current hotel
        final updatedHotel = response.data!.toHotelPlus(
          currency: _hotel.value?.currency ?? 'USD',
          availability: _hotel.value?.availability,
          pricing: _hotel.value?.pricing,
        );

        _hotel.value = updatedHotel;
        print('✅ Hotel details loaded successfully: ${updatedHotel.name}');

      } else {
        print('❌ Failed to load hotel details: ${response.message}');
        Core.showGlobalSnackBar(
          response.message ?? 'فشل في تحميل تفاصيل الفندق',
          backgroundColor: Colors.orange,
        );
      }
    } catch (e) {
      print('❌ Error loading hotel details: $e');
      Core.showGlobalSnackBar(
        'خطأ: حدث خطأ في تحميل تفاصيل الفندق',
        backgroundColor: Colors.red,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  // Check if we should load detailed hotel information
  bool _shouldLoadDetailedInfo() {
    if (hotel == null) return false;

    // Load details if we have minimal information (e.g., from search results)
    // Check if we're missing key details like description, facilities, or amenities
    return hotel!.description.isEmpty ||
        hotel!.facilities.isEmpty ||
        hotel!.amenities.isEmpty ||
        hotel!.images.isEmpty;
  }

  // Refresh hotel details from API
  Future<void> refreshHotelDetails() async {
    if (hotel?.code != null) {
      await loadHotelDetails(hotel!.code);
    }
  }

  // Select room
  void selectRoom(RoomPlus room) {
    print('🛏️ Selecting room: ${room.roomType}');
    _selectedRoom.value = room;
  }

  // Image gallery methods
  void selectImage(int index) {
    if (index >= 0 && index < images.length) {
      _selectedImageIndex.value = index;
    }
  }

  void nextImage() {
    if (images.isNotEmpty) {
      _selectedImageIndex.value =
          (_selectedImageIndex.value + 1) % images.length;
    }
  }

  void previousImage() {
    if (images.isNotEmpty) {
      _selectedImageIndex.value =
          (_selectedImageIndex.value - 1 + images.length) % images.length;
    }
  }

  // Toggle methods
  void toggleShowAllAmenities() {
    _showAllAmenities.value = !_showAllAmenities.value;
  }

  void toggleShowAllFacilities() {
    _showAllFacilities.value = !_showAllFacilities.value;
  }

  void toggleIncludeTaxes() {
    _includeTaxes.value = !_includeTaxes.value;
  }

  // Tab selection
  void selectTab(int index) {
    _selectedTab.value = index;
  }

  // Get displayed amenities
  List<String> get displayedAmenities {
    if (showAllAmenities || amenities.length <= 6) {
      return amenities;
    }
    return amenities.take(6).toList();
  }

  // Get displayed facilities
  List<String> get displayedFacilities {
    if (showAllFacilities || facilities.length <= 6) {
      return facilities;
    }
    return facilities.take(6).toList();
  }

  // Price display mode
  final RxBool _showPerNightPrice = false.obs;
  bool get showPerNightPrice => _showPerNightPrice.value;

  // Toggle price display mode
  void togglePriceDisplayMode() {
    _showPerNightPrice.value = !_showPerNightPrice.value;
    update();
  }

  // Get room price - always use totalFare (final price customer pays)
  num getRoomPrice(RoomPlus room) {
    return room.totalFare; // السعر النهائي الذي يدفعه العميل
  }

  // Get per night price from daily rates
  double getPerNightPrice(RoomPlus room) {
    if (room.dailyRates.isEmpty) return 0.0;

    // Calculate average per night price from daily rates
    double totalDailyPrice = 0.0;
    for (var dailyRate in room.dailyRates) {
      totalDailyPrice += includeTaxes
          ? (dailyRate.basePrice + dailyRate.taxes)
          : dailyRate.originalBasePrice;
    }

    return room.dailyRates.isNotEmpty
        ? totalDailyPrice / room.dailyRates.length
        : 0.0;
  }

  // Get selected room price based on display mode
  num get selectedRoomPrice {
    if (selectedRoom == null) return 0.0;

    return showPerNightPrice
        ? getPerNightPrice(selectedRoom!)
        : getRoomPrice(selectedRoom!);
  }

  // Get price label based on display mode
  String get priceLabel {
    return 'per stay';
  }


  // Book hotel - Navigate to pre-booking
  Future<void> bookHotel() async {
    print('🟠 [HOTEL_DETAILS_CONTROLLER] bookHotel started');

    if (hotel == null || selectedRoom == null) {
      print(
          '🔴 [HOTEL_DETAILS_CONTROLLER] Missing data - hotel: ${hotel?.name}, selectedRoom: ${selectedRoom?.roomType}');
      Core.showGlobalSnackBar(
        'خطأ: يرجى اختيار غرفة أولاً',
        backgroundColor: Colors.red,
      );
      return;
    }

    try {
      print('🟠 [HOTEL_DETAILS_CONTROLLER] Starting hotel booking process...');
      print('🟠 [HOTEL_DETAILS_CONTROLLER] Hotel: ${hotel!.name}');
      print('🟠 [HOTEL_DETAILS_CONTROLLER] Hotel Code: ${hotel!.code}');
      print('🟠 [HOTEL_DETAILS_CONTROLLER] Room: ${selectedRoom!.roomType}');
      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] Room Booking Code: ${selectedRoom!.bookingCode}');
      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] Price: $selectedRoomPrice $currency');

      // Get guest data from HotelsData (original search request)
      int adultsCount = 2; // Default
      int childrenCount = 0; // Default
      int roomsCount = 1; // Default
      DateTime checkInDate = DateTime.now().add(const Duration(days: 1));
      DateTime checkOutDate = DateTime.now().add(const Duration(days: 2));

      try {
        final hotelsData = Get.find<HotelsDataSearch>();
        if (hotelsData.request != null) {
          // Get adults and children from HotelRequest
          adultsCount = hotelsData.request!.adults;
          childrenCount = hotelsData.request!.childrenCount;
          roomsCount = hotelsData.request!.rooms.length;

          // Get dates from stay request
          if (hotelsData.request!.stay != null) {
            checkInDate = hotelsData.request!.stay!.checkIn ?? checkInDate;
            checkOutDate = hotelsData.request!.stay!.checkOut ?? checkOutDate;
          }

          print('🟠 [HOTEL_DETAILS_CONTROLLER] Using data from HotelsData:');
          print('🟠 [HOTEL_DETAILS_CONTROLLER] - Adults: $adultsCount');
          print('🟠 [HOTEL_DETAILS_CONTROLLER] - Children: $childrenCount');
          print('🟠 [HOTEL_DETAILS_CONTROLLER] - Rooms: $roomsCount');
          print('🟠 [HOTEL_DETAILS_CONTROLLER] - Check In: $checkInDate');
          print('🟠 [HOTEL_DETAILS_CONTROLLER] - Check Out: $checkOutDate');
        }
      } catch (e) {
        print(
            '🟡 [HOTEL_DETAILS_CONTROLLER] HotelsData not found, using defaults: $e');
      }

      // Create booking request with real data from search
      final bookingRequest = PreBookingRequestPlus(
        hotelCode: hotel!.code,
        roomCode: selectedRoom!.bookingCode,
        checkIn: checkInDate,
        checkOut: checkOutDate,
        adults: adultsCount,
        children: childrenCount,
        rooms: roomsCount,
        currency: currency,
      );

      print('🟠 [HOTEL_DETAILS_CONTROLLER] Booking request created:');
      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] - Hotel Code: ${bookingRequest.hotelCode}');
      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] - Room Code: ${bookingRequest.roomCode}');
      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] - Check In: ${bookingRequest.checkIn}');
      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] - Check Out: ${bookingRequest.checkOut}');
      print('🟠 [HOTEL_DETAILS_CONTROLLER] - Adults: ${bookingRequest.adults}');
      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] - Children: ${bookingRequest.children}');
      print('🟠 [HOTEL_DETAILS_CONTROLLER] - Rooms: ${bookingRequest.rooms}');
      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] - Currency: ${bookingRequest.currency}');

      print(
          '🟠 [HOTEL_DETAILS_CONTROLLER] Navigating to PreBookingScreenPlus...');

      // Navigate to pre-booking screen
      HotelsNavigation.toPreBooking(
          arguments: {
            "hotel": hotel,
            "room": selectedRoom!,
            "bookingRequest": bookingRequest
          }
      );

      print('🟠 [HOTEL_DETAILS_CONTROLLER] Navigation initiated');
    } catch (e) {
      print('🔴 [HOTEL_DETAILS_CONTROLLER] Error starting booking process: $e');
      print('🔴 [HOTEL_DETAILS_CONTROLLER] Stack trace: ${StackTrace.current}');
      Core.showGlobalSnackBar(
        'خطأ في الحجز: حدث خطأ أثناء بدء عملية الحجز، يرجى المحاولة مرة أخرى',
        backgroundColor: Colors.red,
      );
    }
  }

  // Share hotel
  void shareHotel() async {
    if (hotel == null) return;

    try {
      // Get deep link service
      final deepLinkService = Get.find<DeepLinkService>();

      // Create both app and web deep links
      final appDeepLink = deepLinkService.createHotelShareLink(hotel!);
      final webDeepLink = deepLinkService.createHotelWebLink(hotel!);

      final shareText = '''
🏨 ${hotel!.name}
⭐ ${hotel!.starRating} نجوم
📍 ${hotel!.address}
💰 من $minPrice $currency

🔗 شاهد تفاصيل الفندق:
$webDeepLink

📱 افتح في التطبيق:
$appDeepLink
    ''';

      // Use share_plus to share the content
      await Share.share(
        shareText,
        subject: 'فندق ${hotel!.name} - ${hotel!.starRating} نجوم',
      );

      print('📤 Hotel shared successfully: ${hotel!.name}');
    } catch (e) {
      print('❌ Error sharing hotel: $e');
      Core.showGlobalSnackBar(
        'خطأ: حدث خطأ أثناء مشاركة الفندق',
        backgroundColor: Colors.red,
      );
    }
  }

  // Add to favorites using new API
  Future<void> toggleFavorite() async {
    if (hotel == null) return;

    try {
      // استخدام النظام الجديد للمفضلات
      final favoritesController = Get.find<FavoritesControllerPlus>();

      final response = await favoritesController.toggleFavoriteWithAPI(
        hotelId: hotel!.code,
        hotelName: hotel!.name,
        hotelImage: hotel!.images.isNotEmpty ? hotel!.images.first : null,
        hotelLocation: hotel!.address,
        hotelStarRating: hotel!.starRating,
      );

      if (response.success) {
        final action = response.data?.action ?? 'unknown';
        final message = action == 'added'
            ? '❤️ تم إضافة ${hotel!.name} للمفضلة'
            : '💔 تم إزالة ${hotel!.name} من المفضلة';

        print(message);
        Core.showGlobalSnackBar(
          message,
          backgroundColor: action == 'added' ? Colors.green : Colors.orange,
        );

        // تحديث UI
        update();
      } else {
        print('❌ Failed to toggle favorite: ${response.message}');
        Core.showGlobalSnackBar(
          'خطأ: ${response.message}',
          backgroundColor: Colors.red,
        );
      }
    } catch (e) {
      print('❌ Error toggling favorite: $e');

      // Fallback للنظام القديم في حالة الخطأ
      try {
        final userController = Get.find<UserControllerPlus>();
        final hotelId = hotel!.code;

        if (userController.isFavorite(hotelId)) {
          await userController.removeFromFavorites(hotelId);
          print('💔 Fallback: Removed hotel from favorites: ${hotel!.name}');
        } else {
          await userController.addToFavorites(hotelId);
          print('❤️ Fallback: Added hotel to favorites: ${hotel!.name}');
        }
        update();
      } catch (fallbackError) {
        print('❌ Fallback also failed: $fallbackError');
        Core.showGlobalSnackBar(
          'خطأ: حدث خطأ في إدارة المفضلة',
          backgroundColor: Colors.red,
        );
      }
    }
  }

  /// Create default paxRooms based on adults, children, and rooms count
  List<PaxRoom> _createDefaultPaxRooms(int adults, int children, int rooms) {
    final List<PaxRoom> paxRooms = [];

    // Distribute adults and children across rooms
    final adultsPerRoom = (adults / rooms).floor();
    final childrenPerRoom = (children / rooms).floor();
    final extraAdults = adults % rooms;
    final extraChildren = children % rooms;

    for (int i = 0; i < rooms; i++) {
      final roomAdults = adultsPerRoom + (i < extraAdults ? 1 : 0);
      final roomChildren = childrenPerRoom + (i < extraChildren ? 1 : 0);

      // Create default children ages (assuming 8 years old)
      final childrenAges = List.generate(roomChildren, (index) => 8);

      paxRooms.add(PaxRoom(
        adults: roomAdults,
        children: roomChildren,
        childrenAges: childrenAges,
      ));
    }

    Log.debug(
        '🔍 [HOTEL_DETAILS] Created default paxRooms: ${paxRooms.length} rooms, $adults adults, $children children');
    return paxRooms;
  }

  /// Compare two lists for equality
  bool _areListsEqual<T>(List<T> list1, List<T> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  /// Initialize search parameters from arguments or defaults
  void _initializeSearchParams() {
    final arguments = Get.arguments as Map<String, dynamic>?;

    if (arguments != null) {
      // Set search parameters from arguments
      _searchCheckIn.value = arguments['checkInDate'] as DateTime?;
      _searchCheckOut.value = arguments['checkOutDate'] as DateTime?;
      _searchAdults.value = arguments['adults'] as int? ?? 2;
      _searchChildren.value = arguments['children'] as int? ?? 0;
      _searchRooms.value = arguments['rooms'] as int? ?? 1;
      _searchPaxRooms.value = arguments['paxRooms'] as List<PaxRoom>? ?? [];

      // Store original values for comparison
      _originalCheckIn = _searchCheckIn.value;
      _originalCheckOut = _searchCheckOut.value;
      _originalAdults = _searchAdults.value;
      _originalChildren = _searchChildren.value;
      _originalRooms = _searchRooms.value;
      _originalPaxRooms = List.from(_searchPaxRooms);
    } else {
      // Set default values
      _searchCheckIn.value = DateTime.now();
      _searchCheckOut.value = DateTime.now().add(const Duration(days: 1));
      _searchAdults.value = 2;
      _searchChildren.value = 0;
      _searchRooms.value = 1;
      _searchPaxRooms.value = _createDefaultPaxRooms(2, 0, 1);

      // Store original values
      _originalCheckIn = _searchCheckIn.value;
      _originalCheckOut = _searchCheckOut.value;
      _originalAdults = 2;
      _originalChildren = 0;
      _originalRooms = 1;
      _originalPaxRooms = List.from(_searchPaxRooms);
    }
  }

  /// Update check-in date
  void updateCheckInDate(DateTime date) {
    _searchCheckIn.value = date;

    // Ensure check-out is at least one day after check-in
    if (_searchCheckOut.value != null &&
        _searchCheckOut.value!.isBefore(date.add(const Duration(days: 1)))) {
      _searchCheckOut.value = date.add(const Duration(days: 1));
    }
  }

  /// Update check-out date
  void updateCheckOutDate(DateTime date) {
    _searchCheckOut.value = date;
  }

  /// Update guest parameters
  void updateGuestParams(
      int adults, int children, int rooms, List<PaxRoom> paxRooms) {
    _searchAdults.value = adults;
    _searchChildren.value = children;
    _searchRooms.value = rooms;
    _searchPaxRooms.value = paxRooms;
  }

  /// Update search parameters and refresh hotel data
  Future<void> updateSearchParams() async {
    if (!hasSearchParamsChanged || isSearching) return;

    try {
      _isSearching.value = true;

      // Update original values
      _originalCheckIn = _searchCheckIn.value;
      _originalCheckOut = _searchCheckOut.value;
      _originalAdults = _searchAdults.value;
      _originalChildren = _searchChildren.value;
      _originalRooms = _searchRooms.value;
      _originalPaxRooms = List.from(_searchPaxRooms);

      // Refresh hotel availability with new parameters
      final hotelCode = hotel?.code;
      if (hotelCode != null) {
        await _searchHotelAvailability(hotelCode, useDeepLinkParams: false);
      }

      Log.debug('✅ [SEARCH_PARAMS] Search parameters updated successfully');
    } catch (e) {
      Log.error('❌ [SEARCH_PARAMS] Error updating search parameters: $e');
      Core.showGlobalSnackBar(
        'خطأ في تحديث معاملات البحث',
        backgroundColor: Colors.red,
      );
    } finally {
      _isSearching.value = false;
    }
  }

  @override
  void onClose() {
    scrollController.dispose();
    print('🔄 Hotel details controller disposed');
    super.onClose();
  }
}
