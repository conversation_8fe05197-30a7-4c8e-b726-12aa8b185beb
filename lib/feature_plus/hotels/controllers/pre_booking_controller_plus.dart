import 'package:fandooq/feature_plus/feature_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/core.dart';
import '../models/hotel_models.dart';
import '../models/pre_booking_models.dart';
import '../views/guest_details_screen_plus.dart';
import '../services/pre_booking_service_plus.dart';
import '../controllers/hotels_controller_plus.dart';
import '../../settings/controllers/settings_controller.dart';

class PreBookingControllerPlus extends GetxController {
  // Services
  final PreBookingServicePlus _preBookingService = PreBookingServicePlus();

  // Observable variables
  final _isLoading = false.obs;
  final _preBookingData = Rxn<PreBookingResponsePlus>();
  final _selectedRoom = Rxn<RoomPlus>();
  final _hotel = Rxn<HotelPlus>();
  final _bookingRequest = Rxn<PreBookingRequestPlus>();

  // Getters
  bool get isLoading => _isLoading.value;
  PreBookingResponsePlus? get preBookingData => _preBookingData.value;
  RoomPlus? get selectedRoom => _selectedRoom.value;
  HotelPlus? get hotel => _hotel.value;
  PreBookingRequestPlus? get bookingRequest => _bookingRequest.value;

  // Booking details
  String get hotelName => hotel?.name ?? '';
  String get roomType => selectedRoom?.roomType ?? '';

  String get bookingCode => selectedRoom?.bookingCode ?? '';

  // Booking policies
  bool get isRefundable => preBookingData?.isRefundable ?? false;
  List<String> get rateConditions {
    final conditions = preBookingData?.rateConditions ?? [];
    print(
        '🔵 [PRE_BOOKING_CONTROLLER] Getting rate conditions: ${conditions.length} found');
    for (int i = 0; i < conditions.length; i++) {
      print(
          '🔵 [PRE_BOOKING_CONTROLLER] Condition $i: ${conditions[i].substring(0, conditions[i].length > 50 ? 50 : conditions[i].length)}...');
    }
    return conditions;
  }

  String get cancellationPolicy {
    final room = preBookingData?.firstRoom;
    if (room?.cancellationPolicies.isNotEmpty == true) {
      final policy = room!.cancellationPolicies.first;
      return '${policy.chargeType}: ${policy.cancellationCharge}% من ${policy.fromDate}';
    }
    return 'غير محدد';
  }

  // Guest requirements from PreBookingRequestPlus (which comes from HotelRequest)
  int get requiredGuests => bookingRequest?.adults ?? 0;
  int get requiredChildren => bookingRequest?.children ?? 0;
  int get totalGuests => requiredGuests + requiredChildren;

  // Initialize with booking data
  void initializePreBooking() {
    print('🔵 [PRE_BOOKING_CONTROLLER] initializePreBooking started');

    try {
      _hotel.value = Get.arguments["hotel"];
      _selectedRoom.value = Get.arguments["room"];
      _bookingRequest.value = Get.arguments["bookingRequest"];

      print('🔵 [PRE_BOOKING_CONTROLLER] Data assigned successfully');
      print('🔵 [PRE_BOOKING_CONTROLLER] Starting pre-booking process...');

      // Start pre-booking process
      _performPreBooking();
    } catch (e) {
      print('🔴 [PRE_BOOKING_CONTROLLER] Error in initializePreBooking: $e');
      print('🔴 [PRE_BOOKING_CONTROLLER] Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  // Perform pre-booking API call
  Future<void> _performPreBooking() async {
    print('🟡 [PRE_BOOKING_CONTROLLER] _performPreBooking started');

    print('🟡 [PRE_BOOKING_CONTROLLER] Setting loading to true');
    _isLoading.value = true;

    print('🟡 [PRE_BOOKING_CONTROLLER] Checking data availability...');
    print(
        '🟡 [PRE_BOOKING_CONTROLLER] selectedRoom: ${selectedRoom?.bookingCode}');
    print('🟡 [PRE_BOOKING_CONTROLLER] hotel: ${hotel?.name}');
    print('🟡 [PRE_BOOKING_CONTROLLER] hotel currency: ${hotel?.currency}');

    if (selectedRoom == null) {
      throw Exception('Selected room is null');
    }

    if (hotel == null) {
      throw Exception('Hotel is null');
    }

    // الحصول على العملة المختارة من المستخدم
    final appCurrency = _getCurrentCurrency();

    print('🟡 [PRE_BOOKING_CONTROLLER] Calling pre-booking service...');
    print(
        '🟡 [PRE_BOOKING_CONTROLLER] Booking Code: ${selectedRoom!.bookingCode}');
    print('🟡 [PRE_BOOKING_CONTROLLER] Payment Mode: Limit');
    print('🟡 [PRE_BOOKING_CONTROLLER] App Currency: $appCurrency');
    print('🟡 [PRE_BOOKING_CONTROLLER] Hotel Currency: ${hotel!.currency}');

    // Call real pre-booking API
    final preBookingResponse = await _preBookingService.performPreBooking(
      bookingCode: selectedRoom!.bookingCode,
      paymentMode: 'Limit',
      currency: appCurrency, // استخدام العملة المختارة من المستخدم
    );

    print(
        '🟡 [PRE_BOOKING_CONTROLLER] Pre-booking service completed successfully');
    print(
        '🟡 [PRE_BOOKING_CONTROLLER] Response booking ID: ${preBookingResponse.bookingId}');
    print(
        '🟡 [PRE_BOOKING_CONTROLLER] Response total price: ${preBookingResponse.totalPrice}');

    _preBookingData.value = preBookingResponse;

    print('🟡 [PRE_BOOKING_CONTROLLER] Pre-booking data assigned successfully');

    try {} on SessionExpiredException catch (e) {
      print('🔴 [PRE_BOOKING_CONTROLLER] Session expired: $e');
      _handleSessionExpired();
    } catch (e) {
      print('🔴 [PRE_BOOKING_CONTROLLER] Error in _performPreBooking: $e');
      print('🔴 [PRE_BOOKING_CONTROLLER] Stack trace: ${StackTrace.current}');

      // Handle different types of errors
      if (e.toString().contains('خدمة الحجز غير متاحة حالياً') ||
          e.toString().contains('502') ||
          e.toString().contains('Bad Gateway')) {
        _handleServiceUnavailable();
      } else {
        Core.showGlobalSnackBar(
          'خطأ في الحجز المسبق: ${e.toString().replaceAll('Exception: ', '')}',
          backgroundColor: Colors.red,
        );
      }
    } finally {
      print('🟡 [PRE_BOOKING_CONTROLLER] Setting loading to false');
      _isLoading.value = false;
      print('🟡 [PRE_BOOKING_CONTROLLER] _performPreBooking completed');
    }
  }

  // Proceed to guest details
  void proceedToGuestDetails() {
    print('🟢 [PRE_BOOKING_CONTROLLER] proceedToGuestDetails started');

    // Store current values to avoid null check issues
    final currentPreBookingData = preBookingData;
    final currentHotel = hotel;
    final currentSelectedRoom = selectedRoom;
    final currentBookingRequest = bookingRequest;

    // Validate all required data
    if (currentPreBookingData == null) {
      print('🔴 [PRE_BOOKING_CONTROLLER] preBookingData is null');
      Get.snackbar(
        'خطأ',
        'لم يتم إكمال عملية الحجز المسبق بعد',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (currentHotel == null) {
      print('🔴 [PRE_BOOKING_CONTROLLER] hotel is null');
      Get.snackbar(
        'خطأ',
        'بيانات الفندق غير متوفرة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (currentSelectedRoom == null) {
      print('🔴 [PRE_BOOKING_CONTROLLER] selectedRoom is null');
      Get.snackbar(
        'خطأ',
        'بيانات الغرفة غير متوفرة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (currentBookingRequest == null) {
      print('� [PRE_BOOKING_CONTROLLER] bookingRequest is null');
      Get.snackbar(
        'خطأ',
        'بيانات طلب الحجز غير متوفرة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    print('�🟢 [PRE_BOOKING_CONTROLLER] Pre-booking data available');
    print('🟢 [PRE_BOOKING_CONTROLLER] Hotel: ${currentHotel.name}');
    print('🟢 [PRE_BOOKING_CONTROLLER] Room: ${currentSelectedRoom.roomType}');
    print(
        '🟢 [PRE_BOOKING_CONTROLLER] Booking request: ${currentBookingRequest.adults} adults');
    print(
        '🟢 [PRE_BOOKING_CONTROLLER] Navigating to GuestDetailsScreenPlus...');

    try {
      // Navigate to guest details screen
      HotelsNavigation.toGuestDetails(arguments: {
        "preBookingData": currentPreBookingData,
        "hotel": currentHotel,
        "room": currentSelectedRoom,
        "bookingRequest": currentBookingRequest,
      });

      print('🟢 [PRE_BOOKING_CONTROLLER] Navigation initiated');
    } catch (e) {
      print('🔴 [PRE_BOOKING_CONTROLLER] Navigation error: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء الانتقال إلى صفحة تفاصيل النزلاء',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Cancel pre-booking
  void cancelPreBooking() {
    Get.back();
  }

  // Retry pre-booking
  void retryPreBooking() {
    _performPreBooking();
  }

  // Check if pre-booking is expired
  bool get isExpired {
    // For now, assume pre-booking is valid for 15 minutes
    return false; // TODO: Implement proper expiry logic
  }

  // Get remaining time
  String get remainingTime {
    // For now, return a default time
    return '15:00'; // TODO: Implement proper remaining time logic
  }

  // Handle session expired error
  void _handleSessionExpired() {
    print('🔴 [PRE_BOOKING_CONTROLLER] Handling session expired');

    Get.dialog(
      AlertDialog(
        title: const Text(
          'انتهت صلاحية الجلسة',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        content: const Text(
          'انتهت صلاحية جلسة الحجز. سيتم إعادتك لصفحة الفنادق لإعادة البحث.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog
              _returnToHotelsAndRefresh();
            },
            child: const Text(
              'موافق',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  // Return to hotels page and refresh search
  void _returnToHotelsAndRefresh() {
    print('🔄 [PRE_BOOKING_CONTROLLER] Returning to hotels and refreshing');

    // Go back to hotels page (pop twice: pre-booking -> hotel details -> hotels)
    Get.back(); // Close pre-booking screen
    Get.back(); // Close hotel details screen

    // Get hotels controller and refresh search
    try {
      final hotelsController = Get.find<HotelsControllerPlus>();

      // Show loading message
      Get.snackbar(
        'جاري إعادة البحث',
        'يتم إعادة تحميل الفنادق...',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );

      // Refresh hotels search with existing criteria
      hotelsController.refreshHotels();

      print('🔄 [PRE_BOOKING_CONTROLLER] Hotels search refreshed');
    } catch (e) {
      print('🔴 [PRE_BOOKING_CONTROLLER] Error refreshing hotels: $e');

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إعادة تحميل الفنادق. يرجى المحاولة مرة أخرى.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }

  // Handle service unavailable error (502, Bad Gateway, etc.)
  void _handleServiceUnavailable() {
    print('🔴 [PRE_BOOKING_CONTROLLER] Handling service unavailable');

    Get.dialog(
      AlertDialog(
        title: const Text(
          'خدمة الحجز غير متاحة',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        content: const Text(
          'خدمة الحجز غير متاحة حالياً بسبب مشكلة تقنية. يمكنك المحاولة مرة أخرى أو العودة لاختيار فندق آخر.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog
              retryPreBooking(); // Retry the same booking
            },
            child: const Text(
              'إعادة المحاولة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog
              Get.back(); // Go back to hotel details
            },
            child: const Text(
              'العودة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// الحصول على العملة الحالية من الإعدادات
  String _getCurrentCurrency() {
    try {
      // Try SettingsController from feature_plus
      if (Get.isRegistered<SettingsController>()) {
        final settingsController = Get.find<SettingsController>();
        final currency = settingsController.appSettings.currency;
        if (currency.isNotEmpty) {
          print(
              '💱 ✅ Got currency from SettingsController (pre-booking): $currency');
          return currency;
        }
      }

      print(
          '💱 ⚠️ No settings controller found in pre-booking, using USD default');
      return 'USD';
    } catch (e) {
      print(
          '💱 ⚠️ Error getting currency in pre-booking: $e, using USD default');
      return 'USD';
    }
  }

  @override
  void onInit() {
    initializePreBooking();
    super.onInit();
  }
}
