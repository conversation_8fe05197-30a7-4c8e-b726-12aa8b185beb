import 'package:fandooq/core/router/app_pages.dart';
import 'package:fandooq/feature_plus/feature_plus.dart';
import 'package:get/get.dart';
import '../models/hotel_models.dart';

/// Hotels Route Constants

/// Hotels Navigation Helper Class
class HotelsNavigation {
  /// Navigate to Hotels List Screen
  static Future<T?>? toHotels<T>({
    Map<String, dynamic>? arguments,
  }) {
    return Get.toNamed<T>(Routes.HOTELS_PLUS, arguments: arguments);
  }

  /// Navigate to Hotel Details Screen
  static Future<T?>? toHotelDetails<T>({
    required HotelPlus hotel,
    bool scrollToRooms = false,
    Map<String, dynamic>? additionalArguments,
  }) {
    final arguments = {
      "hotel": hotel,
      "scrollToRooms": scrollToRooms,
      ...?additionalArguments,
    };
    return Get.toNamed<T>(Routes.HOTEL_DETAILS_PLUS, arguments: arguments);
  }

  /// Navigate to Pre-booking Screen
  static Future<T?>? toPreBooking<T>({
    Map<String, dynamic>? arguments,
  }) {
    return Get.toNamed<T>(Routes.PRE_BOOKING_PLUS, arguments: arguments);
  }

  /// Navigate to Guest Details Screen
  static Future<T?>? toGuestDetails<T>({
    Map<String, dynamic>? arguments,
  }) {
    return Get.toNamed<T>(Routes.GUEST_DETAILS_PLUS, arguments: arguments);
  }

  /// Navigate to Payment Screen
  static Future<T?>? toPayment<T>({
    Map<String, dynamic>? arguments,
  }) {
    return Get.toNamed<T>(Routes.PAYMENT_PLUS, arguments: arguments);
  }

  /// Navigate to Booking Confirmation Screen
  static Future<T?>? toBookingConfirmation<T>({
    Map<String, dynamic>? arguments,
  }) {
    return Get.toNamed<T>(Routes.BOOKING_CONFIRMATION_PLUS,
        arguments: arguments);
  }

  /// Navigate to Booking Success Screen
  static Future<T?>? toBookingSuccess<T>({
    required dynamic bookingResponse,
    Map<String, dynamic>? additionalArguments,
  }) {
    final arguments = {
      'bookingResponse': bookingResponse,
      ...?additionalArguments,
    };
    return Get.toNamed<T>(Routes.BOOKING_SUCCESS_PLUS, arguments: arguments);
  }

  /// Navigate back to Hotels from any screen
  static Future<T?>? backToHotels<T>() {
    return Get.offAllNamed<T>(Routes.HOTELS_PLUS);
  }

  /// Navigate back
  static void back<T>([T? result]) {
    Get.back<T>(result: result);
  }

  /// Navigate back until Hotels screen
  static void backUntilHotels() {
    Get.until((route) => route.settings.name == Routes.HOTELS_PLUS);
  }

  static void backUntilHomePage() {
    Get.until((route) {
      print("ROUTE: ${route.settings.name}");
      return route.settings.name == HomeNavigator.home;
    });
  }
}
