
import 'hotel_models.dart';

// Pre-Booking Request Model
class PreBookingRequestPlus {
  final String hotelCode;
  final String roomCode;
  final DateTime checkIn;
  final DateTime checkOut;
  final int adults;
  final int children;
  final int rooms;
  final String currency;

  PreBookingRequestPlus({
    required this.hotelCode,
    required this.roomCode,
    required this.checkIn,
    required this.checkOut,
    required this.adults,
    required this.children,
    required this.rooms,
    required this.currency,
  });

  Map<String, dynamic> toJson() {
    return {
      'hotelCode': hotelCode,
      'roomCode': roomCode,
      'checkIn': checkIn.toIso8601String(),
      'checkOut': checkOut.toIso8601String(),
      'adults': adults,
      'children': children,
      'rooms': rooms,
      'currency': currency,
    };
  }

  factory PreBookingRequestPlus.fromJson(Map<String, dynamic> json) {
    return PreBookingRequestPlus(
      hotelCode: json['hotelCode'] ?? '',
      roomCode: json['roomCode'] ?? '',
      checkIn: DateTime.parse(json['checkIn']),
      checkOut: DateTime.parse(json['checkOut']),
      adults: json['adults'] ?? 0,
      children: json['children'] ?? 0,
      rooms: json['rooms'] ?? 1,
      currency: json['currency'] ?? 'USD',
    );
  }
}

// Pre-Booking Response Model
class PreBookingResponsePlus {
  final String bookingId; // Generated locally
  final String hotelCode;
  final String currency;
  final int totalRooms;
  final List<RoomPlus> rooms;
  final List<String> rateConditions;

  PreBookingResponsePlus({
    required this.bookingId,
    required this.hotelCode,
    required this.currency,
    required this.totalRooms,
    required this.rooms,
    required this.rateConditions,
  });

  // Convenience getters for first room (most common case)
  RoomPlus? get firstRoom => rooms.isNotEmpty ? rooms.first : null;
  num get totalPrice => firstRoom?.totalFare ?? 0.0;
  num get totalTax => firstRoom?.totalTax ?? 0.0;
  num get originalPrice => firstRoom?.pricing?.converted.price ?? 0.0;
  bool get isRefundable => firstRoom?.isRefundable ?? false;
  String get bookingCode => firstRoom?.bookingCode ?? '';
  String get roomType => firstRoom?.roomType ?? '';

  Map<String, dynamic> toJson() {
    return {
      'bookingId': bookingId,
      'hotelCode': hotelCode,
      'currency': currency,
      'totalRooms': totalRooms,
      'rooms': rooms.map((room) => room.toJson()).toList(),
      'rateConditions': rateConditions,
    };
  }

  factory PreBookingResponsePlus.fromJson(Map<String, dynamic> json) {
    return PreBookingResponsePlus(
      bookingId: json['bookingId'] ?? '',
      hotelCode: json['hotelCode'] ?? '',
      currency: json['currency'] ?? 'USD',
      totalRooms: json['totalRooms'] ?? 1,
      rooms: (json['rooms'] as List? ?? [])
          .map((room) => RoomPlus.fromJson(room)) // ✅ استخدم fromApi هنا
          .toList(),
      rateConditions: List<String>.from(json['rateConditions'] ?? []),
    );
  }


  // ✅ CopyWith method
  PreBookingResponsePlus copyWith({
    String? bookingId,
    String? hotelCode,
    String? currency,
    int? totalRooms,
    List<RoomPlus>? rooms,
    List<String>? rateConditions,
    DateTime? checkIn,
    DateTime? checkOut,
    int? adults,
    int? children,
  }) {
    return PreBookingResponsePlus(
      bookingId: bookingId ?? this.bookingId,
      hotelCode: hotelCode ?? this.hotelCode,
      currency: currency ?? this.currency,
      totalRooms: totalRooms ?? this.totalRooms,
      rooms: rooms ?? this.rooms,
      rateConditions: rateConditions ?? this.rateConditions,
    );
  }

}

// Guest Details Model
class GuestDetailsPlus {
  final String title; // Added title field
  final String firstName;
  final String lastName;
  final String nationality;
  final String? passportNumber; // Made optional
  final DateTime? passportExpiry;
  final String email;
  final String phone;
  final bool isMainGuest;
  final String guestType; // adult, child

  GuestDetailsPlus({
    this.title = 'Mr', // Default value
    required this.firstName,
    required this.lastName,
    required this.nationality,
    this.passportNumber, // Made optional
    this.passportExpiry,
    required this.email,
    required this.phone,
    required this.isMainGuest,
    required this.guestType,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'firstName': firstName,
      'lastName': lastName,
      'nationality': nationality,
      'passportNumber': passportNumber ?? '',
      'passportExpiry': passportExpiry?.toIso8601String(),
      'email': email,
      'phone': phone,
      'isMainGuest': isMainGuest,
      'guestType': guestType,
    };
  }

  factory GuestDetailsPlus.fromJson(Map<String, dynamic> json) {
    return GuestDetailsPlus(
      title: json['title'] ?? 'Mr',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      nationality: json['nationality'] ?? '',
      passportNumber: json['passportNumber']?.toString().isEmpty == true
          ? null
          : json['passportNumber']?.toString(),
      passportExpiry: json['passportExpiry'] != null
          ? DateTime.parse(json['passportExpiry'])
          : null,
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      isMainGuest: json['isMainGuest'] ?? false,
      guestType: json['guestType'] ?? 'adult',
    );
  }

  String get fullName => '$firstName $lastName';
}

// Final Booking Request Model
class FinalBookingRequestPlus {
  final String preBookingId;
  final List<GuestDetailsPlus> guests;
  final String paymentMethod;
  final Map<String, dynamic> paymentDetails;
  final String specialRequests;

  FinalBookingRequestPlus({
    required this.preBookingId,
    required this.guests,
    required this.paymentMethod,
    required this.paymentDetails,
    required this.specialRequests,
  });

  Map<String, dynamic> toJson() {
    return {
      'preBookingId': preBookingId,
      'guests': guests.map((guest) => guest.toJson()).toList(),
      'paymentMethod': paymentMethod,
      'paymentDetails': paymentDetails,
      'specialRequests': specialRequests,
    };
  }

  factory FinalBookingRequestPlus.fromJson(Map<String, dynamic> json) {
    return FinalBookingRequestPlus(
      preBookingId: json['preBookingId'] ?? '',
      guests: (json['guests'] as List<dynamic>?)
              ?.map((guest) => GuestDetailsPlus.fromJson(guest))
              .toList() ??
          [],
      paymentMethod: json['paymentMethod'] ?? '',
      paymentDetails: json['paymentDetails'] ?? {},
      specialRequests: json['specialRequests'] ?? '',
    );
  }
}

// Booking Confirmation Model
class BookingConfirmationPlus {
  final String bookingReference;
  final String confirmationNumber;
  final String status;
  final DateTime bookingDate;
  final PreBookingResponsePlus bookingDetails;
  final List<GuestDetailsPlus> guests;
  final String paymentStatus;
  final double totalPaid;
  final String currency;

  BookingConfirmationPlus({
    required this.bookingReference,
    required this.confirmationNumber,
    required this.status,
    required this.bookingDate,
    required this.bookingDetails,
    required this.guests,
    required this.paymentStatus,
    required this.totalPaid,
    required this.currency,
  });

  Map<String, dynamic> toJson() {
    return {
      'bookingReference': bookingReference,
      'confirmationNumber': confirmationNumber,
      'status': status,
      'bookingDate': bookingDate.toIso8601String(),
      'bookingDetails': bookingDetails.toJson(),
      'guests': guests.map((guest) => guest.toJson()).toList(),
      'paymentStatus': paymentStatus,
      'totalPaid': totalPaid,
      'currency': currency,
    };
  }

  factory BookingConfirmationPlus.fromJson(Map<String, dynamic> json) {
    return BookingConfirmationPlus(
      bookingReference: json['bookingReference'] ?? '',
      confirmationNumber: json['confirmationNumber'] ?? '',
      status: json['status'] ?? '',
      bookingDate: DateTime.parse(json['bookingDate']),
      bookingDetails: PreBookingResponsePlus.fromJson(json['bookingDetails']),
      guests: (json['guests'] as List<dynamic>?)
              ?.map((guest) => GuestDetailsPlus.fromJson(guest))
              .toList() ??
          [],
      paymentStatus: json['paymentStatus'] ?? '',
      totalPaid: (json['totalPaid'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'USD',
    );
  }
}

// Guest model for booking
class GuestPlus {
  final String title; // Added title field
  final String firstName;
  final String lastName;
  final String nationality;
  final String type; // 'adult' or 'child'
  final String passportNumber;
  final String email;
  final String phone;

  GuestPlus({
    this.title = 'Mr', // Default value
    required this.firstName,
    required this.lastName,
    required this.nationality,
    required this.type,
    this.passportNumber = '',
    this.email = '',
    this.phone = '',
  });

  factory GuestPlus.fromJson(Map<String, dynamic> json) {
    return GuestPlus(
      title: json['title']?.toString() ?? 'Mr',
      firstName: json['firstName']?.toString() ?? '',
      lastName: json['lastName']?.toString() ?? '',
      nationality: json['nationality']?.toString() ?? '',
      type: json['type']?.toString() ?? 'adult',
      passportNumber: json['passportNumber']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'firstName': firstName,
      'lastName': lastName,
      'nationality': nationality,
      'type': type,
      'passportNumber': passportNumber,
      'email': email,
      'phone': phone,
    };
  }

  bool get isValid {
    return firstName.isNotEmpty &&
        lastName.isNotEmpty &&
        nationality.isNotEmpty;
  }

  String get fullName => '$firstName $lastName';
}

// Room with guests model for booking
class RoomWithGuestsPlus {
  final List<GuestPlus> guests;

  RoomWithGuestsPlus({
    required this.guests,
  });

  factory RoomWithGuestsPlus.fromJson(Map<String, dynamic> json) {
    return RoomWithGuestsPlus(
      guests: (json['guests'] as List<dynamic>?)
              ?.map((guest) => GuestPlus.fromJson(guest))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'guests': guests.map((guest) => guest.toJson()).toList(),
    };
  }

  bool get isValid {
    return guests.isNotEmpty && guests.every((guest) => guest.isValid);
  }
}

// Booking Success Response Model
class BookingSuccessResponsePlus {
  final bool success;
  final BookingDataPlus data;
  final String message;

  BookingSuccessResponsePlus({
    required this.success,
    required this.data,
    required this.message,
  });

  factory BookingSuccessResponsePlus.fromJson(Map<String, dynamic> json) {
    return BookingSuccessResponsePlus(
      success: json['success'] ?? false,
      data: BookingDataPlus.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }
}

class BookingDataPlus {
  final bool success;
  final String bookingId;
  final String bookingReferenceId;
  final String confirmationNumber;
  final String bookingStatus;
  final String statusNote;
  final double invoiceAmount;
  final String currency;
  final BookingDetailsPlus bookingDetails;

  BookingDataPlus({
    required this.success,
    required this.bookingId,
    required this.bookingReferenceId,
    required this.confirmationNumber,
    required this.bookingStatus,
    required this.statusNote,
    required this.invoiceAmount,
    required this.currency,
    required this.bookingDetails,
  });

  factory BookingDataPlus.fromJson(Map<String, dynamic> json) {
    return BookingDataPlus(
      success: json['success'] ?? false,
      bookingId: json['bookingId'] ?? '',
      bookingReferenceId: json['bookingReferenceId'] ?? '',
      confirmationNumber: json['confirmationNumber'] ?? '',
      bookingStatus: json['bookingStatus'] ?? '',
      statusNote: json['statusNote'] ?? '',
      invoiceAmount: (json['invoiceAmount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'USD',
      bookingDetails: BookingDetailsPlus.fromJson(json['bookingDetails'] ?? {}),
    );
  }
}

class BookingDetailsPlus {
  final String emailId;
  final String phoneNumber;
  final double totalFare;
  final double originalPrice;
  final String paymentMode;
  final HotelDetailsPlus hotelDetails;
  final List<BookedRoomPlus> rooms;
  final DateTime bookingDate;
  final DateTime checkIn;
  final DateTime checkOut;
  final int noOfRooms;

  BookingDetailsPlus({
    required this.emailId,
    required this.phoneNumber,
    required this.totalFare,
    required this.originalPrice,
    required this.paymentMode,
    required this.hotelDetails,
    required this.rooms,
    required this.bookingDate,
    required this.checkIn,
    required this.checkOut,
    required this.noOfRooms,
  });

  factory BookingDetailsPlus.fromJson(Map<String, dynamic> json) {
    return BookingDetailsPlus(
      emailId: json['emailId'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      totalFare: (json['totalFare'] ?? 0.0).toDouble(),
      originalPrice: (json['originalPrice'] ?? 0.0).toDouble(),
      paymentMode: json['paymentMode'] ?? '',
      hotelDetails: HotelDetailsPlus.fromJson(json['hotelDetails'] ?? {}),
      rooms: (json['rooms'] as List? ?? [])
          .map((room) => BookedRoomPlus.fromJson(room))
          .toList(),
      bookingDate: DateTime.parse(
          json['bookingDate'] ?? DateTime.now().toIso8601String()),
      checkIn:
          DateTime.parse(json['checkIn'] ?? DateTime.now().toIso8601String()),
      checkOut:
          DateTime.parse(json['checkOut'] ?? DateTime.now().toIso8601String()),
      noOfRooms: json['noOfRooms'] ?? 1,
    );
  }
}

class HotelDetailsPlus {
  final String hotelName;
  final String rating;
  final String addressLine1;
  final String map;
  final String city;

  HotelDetailsPlus({
    required this.hotelName,
    required this.rating,
    required this.addressLine1,
    required this.map,
    required this.city,
  });

  factory HotelDetailsPlus.fromJson(Map<String, dynamic> json) {
    return HotelDetailsPlus(
      hotelName: json['HotelName'] ?? '',
      rating: json['Rating'] ?? '',
      addressLine1: json['AddressLine1'] ?? '',
      map: json['Map'] ?? '',
      city: json['City'] ?? '',
    );
  }
}

class BookedRoomPlus {
  final String currency;
  final String status;
  final List<String> name;
  final String inclusion;
  final double totalFare;
  final double totalTax;
  final List<CancelPolicyPlus> cancelPolicies;
  final String mealType;
  final bool isRefundable;
  final List<CustomerDetailsPlus> customerDetails;

  BookedRoomPlus({
    required this.currency,
    required this.status,
    required this.name,
    required this.inclusion,
    required this.totalFare,
    required this.totalTax,
    required this.cancelPolicies,
    required this.mealType,
    required this.isRefundable,
    required this.customerDetails,
  });

  factory BookedRoomPlus.fromJson(Map<String, dynamic> json) {
    return BookedRoomPlus(
      currency: json['Currency'] ?? '',
      status: json['Status'] ?? '',
      name: List<String>.from(json['Name'] ?? []),
      inclusion: json['Inclusion'] ?? '',
      totalFare: (json['TotalFare'] ?? 0.0).toDouble(),
      totalTax: (json['TotalTax'] ?? 0.0).toDouble(),
      cancelPolicies: (json['CancelPolicies'] as List? ?? [])
          .map((policy) => CancelPolicyPlus.fromJson(policy))
          .toList(),
      mealType: json['MealType'] ?? '',
      isRefundable: json['IsRefundable'] ?? false,
      customerDetails: (json['CustomerDetails'] as List? ?? [])
          .map((customer) => CustomerDetailsPlus.fromJson(customer))
          .toList(),
    );
  }
}

class CancelPolicyPlus {
  final String fromDate;
  final String chargeType;
  final int cancellationCharge;

  CancelPolicyPlus({
    required this.fromDate,
    required this.chargeType,
    required this.cancellationCharge,
  });

  factory CancelPolicyPlus.fromJson(Map<String, dynamic> json) {
    return CancelPolicyPlus(
      fromDate: json['FromDate'] ?? '',
      chargeType: json['ChargeType'] ?? '',
      cancellationCharge: json['CancellationCharge'] ?? 0,
    );
  }
}

class CustomerDetailsPlus {
  final List<CustomerNamePlus> customerNames;

  CustomerDetailsPlus({
    required this.customerNames,
  });

  factory CustomerDetailsPlus.fromJson(Map<String, dynamic> json) {
    return CustomerDetailsPlus(
      customerNames: (json['CustomerNames'] as List? ?? [])
          .map((name) => CustomerNamePlus.fromJson(name))
          .toList(),
    );
  }
}

class CustomerNamePlus {
  final String title;
  final String firstName;
  final String lastName;
  final String type;

  CustomerNamePlus({
    required this.title,
    required this.firstName,
    required this.lastName,
    required this.type,
  });

  factory CustomerNamePlus.fromJson(Map<String, dynamic> json) {
    return CustomerNamePlus(
      title: json['Title'] ?? '',
      firstName: json['FirstName'] ?? '',
      lastName: json['LastName'] ?? '',
      type: json['Type'] ?? '',
    );
  }

  String get fullName => '$firstName $lastName';
}

// Alternative Booking Success Response Model (for different API structure)
class AlternativeBookingSuccessResponsePlus {
  final bool success;
  final AlternativeBookingDataPlus data;

  AlternativeBookingSuccessResponsePlus({
    required this.success,
    required this.data,
  });

  factory AlternativeBookingSuccessResponsePlus.fromJson(
      Map<String, dynamic> json) {
    return AlternativeBookingSuccessResponsePlus(
      success: json['success'] ?? false,
      data: AlternativeBookingDataPlus.fromJson(json['data'] ?? {}),
    );
  }
}

class AlternativeBookingDataPlus {
  final String id;
  final String bookingCode;
  final String clientReferenceId;
  final String bookingReferenceId;
  final String confirmationNumber;
  final String status;
  final DateTime checkIn;
  final DateTime checkOut;
  final double totalFare;
  final String currency;
  final String guestNationality;
  final String emailId;
  final String phoneNumber;
  final String bookingType;
  final String paymentMode;
  final String paymentStatus;
  final String userId;
  final String hotelId;
  final double invoiceAmount;
  final String notes;
  final List<String> rateConditions;

  AlternativeBookingDataPlus({
    required this.id,
    required this.bookingCode,
    required this.clientReferenceId,
    required this.bookingReferenceId,
    required this.confirmationNumber,
    required this.status,
    required this.checkIn,
    required this.checkOut,
    required this.totalFare,
    required this.currency,
    required this.guestNationality,
    required this.emailId,
    required this.phoneNumber,
    required this.bookingType,
    required this.paymentMode,
    required this.paymentStatus,
    required this.userId,
    required this.hotelId,
    required this.invoiceAmount,
    required this.notes,
    required this.rateConditions,
  });

  factory AlternativeBookingDataPlus.fromJson(Map<String, dynamic> json) {
    return AlternativeBookingDataPlus(
      id: json['id'] ?? '',
      bookingCode: json['bookingCode'] ?? '',
      clientReferenceId: json['clientReferenceId'] ?? '',
      bookingReferenceId: json['bookingReferenceId'] ?? '',
      confirmationNumber: json['confirmationNumber'] ?? '',
      status: json['status'] ?? '',
      checkIn:
          DateTime.parse(json['checkIn'] ?? DateTime.now().toIso8601String()),
      checkOut:
          DateTime.parse(json['checkOut'] ?? DateTime.now().toIso8601String()),
      totalFare: (json['totalFare'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'USD',
      guestNationality: json['guestNationality'] ?? '',
      emailId: json['emailId'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      bookingType: json['bookingType'] ?? '',
      paymentMode: json['paymentMode'] ?? '',
      paymentStatus: json['paymentStatus'] ?? '',
      userId: json['userId'] ?? '',
      hotelId: json['hotelId'] ?? '',
      invoiceAmount: (json['invoiceAmount'] ?? 0.0).toDouble(),
      notes: json['notes'] ?? '',
      rateConditions: List<String>.from(json['rateConditions'] ?? []),
    );
  }

  // Convert to the standard BookingSuccessResponsePlus format
  BookingSuccessResponsePlus toStandardFormat({
    String? hotelName,
    String? hotelRating,
    String? hotelAddress,
    String? hotelCity,
    String? roomType,
    String? mealType,
    List<String>? guestNames,
  }) {
    // Create room data with available information
    final roomData = BookedRoomPlus(
      currency: currency,
      status: status == 'CONFIRMED' ? 'Not Cancelled' : status,
      name: roomType != null ? [roomType] : ['Standard Room'],
      inclusion: mealType ?? 'Room Only',
      totalFare: totalFare,
      totalTax: 0.0, // Not available in this response
      cancelPolicies: [], // Rate conditions can be used here if needed
      mealType: mealType ?? 'Room_Only',
      isRefundable:
          paymentStatus != 'CONFIRMED', // Assume non-refundable if confirmed
      customerDetails: guestNames != null && guestNames.isNotEmpty
          ? [
              CustomerDetailsPlus(
                customerNames: guestNames
                    .map((name) => CustomerNamePlus(
                          title: 'Mr',
                          firstName: name.split(' ').first,
                          lastName: name.split(' ').length > 1
                              ? name.split(' ').sublist(1).join(' ')
                              : '',
                          type: 'Adult',
                        ))
                    .toList(),
              )
            ]
          : [],
    );

    return BookingSuccessResponsePlus(
      success: true,
      data: BookingDataPlus(
        success: true,
        bookingId: bookingReferenceId,
        bookingReferenceId: bookingReferenceId,
        confirmationNumber: confirmationNumber,
        bookingStatus: status,
        statusNote: notes,
        invoiceAmount: invoiceAmount,
        currency: currency,
        bookingDetails: BookingDetailsPlus(
          emailId: emailId,
          phoneNumber: phoneNumber,
          totalFare: totalFare,
          originalPrice: totalFare, // Use totalFare as original price
          paymentMode: paymentMode,
          hotelDetails: HotelDetailsPlus(
            hotelName: hotelName ?? 'Hotel Name',
            rating: hotelRating ?? 'Unknown',
            addressLine1: hotelAddress ?? 'Address not available',
            map: '',
            city: hotelCity ?? 'Unknown',
          ),
          rooms: [roomData],
          bookingDate: DateTime.now(),
          checkIn: checkIn,
          checkOut: checkOut,
          noOfRooms: 1, // Default
        ),
      ),
      message: 'Booking confirmed successfully',
    );
  }
}
