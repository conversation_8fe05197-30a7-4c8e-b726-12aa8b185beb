
import 'dart:convert';
import 'dart:ui';

import 'package:fandooq/core/components/free_place_search/lib/models/location_info.dart';
import 'package:fandooq/core/lang/app_strings.dart';
import 'package:fandooq/core/models/hotels/hotels.dart';
import 'package:fandooq/feature_plus/location_search/models/location_models.dart';
import 'package:get/get.dart';
import '../../home/<USER>/guests_search.dart';
import 'hotel_data.dart';





class HotelsDataSearch extends GetxController{

  static HotelsDataSearch get to => Get.find();

  List<HotelData> hotels = [];

  HotelData? hotelDetails;

  HotelRequest? request;


  Future<void> updateHotels(HotelsSearchResponse hotelsListResponse) async{
    HotelsDataSearch.to.hotels.clear();
    HotelsDataSearch.to.hotels.addAll(hotelsListResponse.hotelList);
    return;
  }



  void clearDetails() {
    hotelDetails = null;
  }

  void clearHotels() {
    hotels = [];
  }

  void clearAll(){
    clearDetails();
    clearHotels();
  }

  void updateRequest(HotelRequest request) {
    this.request = request;
  }


  Map<String,Size> sizes = {};

  bool updateSizeHotelItem(HotelData hotel,Size size) {
    var state = true;
    if(sizes.containsKey(hotel.customHash1)){
      state = false;
    }
    sizes[hotel.customHash1!] = size;
    return state;
  }

}


class HotelRequest {
  get info => rooms.info;

  List<GuestDataRequest> get children =>
      rooms.map((e) => e.children).expand((e) => e.toList()).toList();

  // Get total number of adults across all rooms
  int get adults =>
      rooms.map((e) => e.adults.length).fold(0, (sum, count) => sum + count);

  // Get total number of children across all rooms
  int get childrenCount => children.length;

  final String? sourceMarket;
  final StayRequest? stay;
  final List<RoomRequestModel> rooms;
  final List<String> hotelsIds;
  final List<ReviewRequest> reviews;
  final FilterRequest? filter;
  final List<String> accommodations;
  final List<int> keywords;
  final LocationPlace? location;

  final RxBool isBreakfastIncluded = false.obs;

  final RxBool isFreeCancellation = false.obs;

  final List<int> selectedRatings;

  num get nightsCount {
    return totalNights();
  }

  int totalNights() {
    // Ensure the check-in and check-out times are set to midnight
    DateTime adjustedCheckIn =
    DateTime(stay!.checkIn!.year, stay!.checkIn!.month, stay!.checkIn!.day);
    DateTime adjustedCheckOut = DateTime(
        stay!.checkOut!.year, stay!.checkOut!.month, stay!.checkOut!.day);
    // Calculate the difference in days
    int nights = adjustedCheckOut.difference(adjustedCheckIn).inDays;
    return nights;
  }

  HotelRequest({
    this.sourceMarket,
    this.stay,
    this.rooms = const [],
    this.hotelsIds = const [],
    this.reviews = const [],
    this.filter,
    this.accommodations = const [],
    this.keywords = const [],
    this.location,
    this.selectedRatings = const [],
  });

  HotelRequest copyWith({
    String? sourceMarket,
    StayRequest? stay,
    List<RoomRequestModel>? rooms,
    List<String>? hotelsIds,
    List<ReviewRequest>? reviews,
    FilterRequest? filter,
    List<String>? accommodations,
    List<int>? keywords,
    double? distValueKmValue,
    LocationPlace? location,
    List<int>? selectedRatings,
    bool? perNight,
  }) {
    return HotelRequest(
      sourceMarket: sourceMarket ?? this.sourceMarket,
      stay: stay ?? this.stay,
      rooms: rooms ?? this.rooms,
      hotelsIds: hotelsIds ?? this.hotelsIds,
      reviews: reviews ?? this.reviews,
      filter: filter ?? this.filter,
      accommodations: accommodations ?? this.accommodations,
      keywords: keywords ?? this.keywords,
      location: location ?? this.location,
      selectedRatings: selectedRatings ?? this.selectedRatings,
    );
  }

  factory HotelRequest.fromJson(dynamic _json) {
    String jsonString = utf8.decode(base64.decode(_json));

    // تحويل السلسلة النصية إلى بيانات JSON
    Map<String, dynamic> json = jsonDecode(jsonString);

    return HotelRequest(
      sourceMarket: json['sourceMarket'] != null ? json['sourceMarket'] : null,
      stay: json['stay'] != null ? StayRequest.fromJson(json['stay']) : null,
      rooms: (json['occupancies'] as List<dynamic>)
          .map((roomJson) => RoomRequestModel.fromJson(roomJson))
          .toList(),
      hotelsIds: (json['hotels']['hotel'] as List<dynamic>).cast<String>(),
      reviews: (json['reviews'] as List<dynamic>)
          .map((reviewJson) => ReviewRequest.fromJson(reviewJson))
          .toList(),
      filter: json['filter'] != null
          ? FilterRequest.fromJson(json['filter'])
          : null,
      location: json['location'] != null
          ? LocationPlace.fromJson(json['location'])
          : null,

    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stay': stay?.toApi(),
      'occupancies': rooms.map((o) => o.toJson()).toList(),
      'hotels': {'hotel': hotelsIds},
      'reviews': reviews.map((r) => r.toApi()).toList(),
      'filter': filter?.toJson(),
      'accommodations': accommodations,
      'keywords': {'keyword': keywords},
      "location": location?.toJson(),
    };
  }

  // Future<Map<String, dynamic>> toApi() async {
  //   // var sourceMarket = await Repos.settings.getSettingsApp();
  //   return {
  //     "Lat": geolocation?.latitude,
  //     "Lng": geolocation?.longitude,
  //     "CheckIn": stay?.checkIn?.toIso8601String(),
  //     "CheckOut": stay?.checkOut?.toIso8601String(),
  //     "GuestNationality": SettingsAppController.to.country,
  //     "Currency": SettingsAppController.to.currency?.currency,
  //     // "guestNationality": sourceMarket.country,
  //     "PaxRooms": rooms.map((o) => o.toApi()).toList(),
  //     'ResponseTime': 23.0,
  //     'IsDetailedResponse': true,
  //     'Filters': {
  //       // "Refundable": false,
  //       "NoOfRooms": 0,
  //       "MealType": "All"
  //     },
  //   };
  // }

  factory HotelRequest.defaultRequest() {
    return HotelRequest(
      stay: StayRequest.defaultStay(),
      rooms: [
        RoomRequestModel.defaultRoom(),
      ],
      // destination: Destination(
      //   code: "EG"
      // ),
      // hotelsIds: [
      //   77,
      //   168,
      //   264,
      //   265,
      //   297,
      //   311
      // ],
      // filter: FilterRequest(
      //   paymentType: "AT_HOTEL",
      // ),
      // geolocation: GeoLocationRequest.defaultLocation(),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is HotelRequest &&
              runtimeType == other.runtimeType &&
              stay == other.stay &&
              rooms == other.rooms &&
              hotelsIds == other.hotelsIds &&
              reviews == other.reviews &&
              filter == other.filter &&
              accommodations == other.accommodations &&
              keywords == other.keywords;

  @override
  int get hashCode =>
      stay.hashCode ^
      rooms.hashCode ^
      hotelsIds.hashCode ^
      reviews.hashCode ^
      filter.hashCode ^
      accommodations.hashCode ^
      keywords.hashCode;

  List<PaxRoom> get paxRooms => rooms.map((o) => PaxRoom(
      adults: o.adults.length,
      children: o.children.length,
      childrenAges: o.children.map((e)=> e.age!).toList()
  )).toList().reversed.toList();


}

class ReviewRequest {
  final String? type;
  final int? maxRate;
  final int? minRate;
  final int? minReviewCount;

  ReviewRequest({this.type, this.maxRate, this.minRate, this.minReviewCount});

  factory ReviewRequest.fromJson(dynamic json) {
    return ReviewRequest(
      type: json['type'],
      maxRate: json['maxRate'],
      minRate: json['minRate'],
      minReviewCount: json['minReviewCount'],
    );
  }

  Map<String, dynamic> toApi() {
    return {
      'type': type,
      'maxRate': maxRate,
      'minRate': minRate,
      'minReviewCount': minReviewCount,
    };
  }
}

class FilterRequest {
  final num? minRate;
  final num? maxRate;
  final int? minCategory;
  final int? maxCategory;
  final int? maxRooms;
  final int? maxRatesPerRoom;
  final String? paymentType;
  final bool? packaging;
  final String? hotelPackage;
  final String? contract;

  FilterRequest({
    this.minRate,
    this.maxRate,
    this.minCategory,
    this.maxCategory,
    this.maxRooms,
    this.maxRatesPerRoom,
    this.paymentType,
    this.packaging,
    this.hotelPackage,
    this.contract,
  });

  FilterRequest.fromJson(dynamic json)
      : minRate = (json['minRate'] ?? 0.0).toDouble(),
        maxRate = (json['maxRate'] ?? 0.0).toDouble(),
        minCategory = json['minCategory'],
        maxCategory = json['maxCategory'],
        maxRooms = json['maxRooms'],
        maxRatesPerRoom = json['maxRatesPerRoom'],
        paymentType = json['paymentType'],
        packaging = json['packaging'],
        hotelPackage = json['hotelPackage'],
        contract = json['contract'];

  Map<String, dynamic> toJson() => {
    'minRate': minRate,
    'maxRate': maxRate,
    'minCategory': minCategory,
    'maxCategory': maxCategory,
    'maxRooms': maxRooms,
    'maxRatesPerRoom': maxRatesPerRoom,
    'paymentType': paymentType,
    'packaging': packaging,
    'hotelPackage': hotelPackage,
    'contract': contract,
  };

  Map<String, dynamic> toApis() => {
    // 'minRate': minRate,
    // 'maxRate': maxRate,
    'minCategory': minCategory,
    'maxCategory': maxCategory,
    'maxRooms': maxRooms,
    'maxRatesPerRoom': maxRatesPerRoom,
    'paymentType': paymentType,
    'packaging': packaging,
    'hotelPackage': hotelPackage,
    'contract': contract,
  };

  FilterRequest copyWith({
    double? minRate,
    double? maxRate,
    int? minCategory,
    int? maxCategory,
    int? maxRooms,
    int? maxRatesPerRoom,
    String? paymentType,
    bool? packaging,
    String? hotelPackage,
    String? contract,
  }) {
    return FilterRequest(
      minRate: minRate ?? this.minRate,
      maxRate: maxRate ?? this.maxRate,
      minCategory: minCategory ?? this.minCategory,
      maxCategory: maxCategory ?? this.maxCategory,
      maxRooms: maxRooms ?? this.maxRooms,
      maxRatesPerRoom: maxRatesPerRoom ?? this.maxRatesPerRoom,
      paymentType: paymentType ?? this.paymentType,
      packaging: packaging ?? this.packaging,
      hotelPackage: hotelPackage ?? this.hotelPackage,
      contract: contract ?? this.contract,
    );
  }
}


extension RoomRequestModelEx on List<RoomRequestModel> {
  String get info {
    // حساب عدد الغرف
    final totalRooms = length;

    // حساب إجمالي عدد البالغين
    final totalAdults =
    map((e) => e.adults.length).reduce((value, element) => value + element);

    // جمع أعمار جميع الأطفال
    final childrenAges =
    expand((e) => e.children).map((child) => child.age).toList();

    // حساب إجمالي عدد الأطفال
    final totalChildren = childrenAges.length;

    // إنشاء نص الأعمار بجانب كل طفل
    String childrenInfo = "";
    if (totalChildren > 0) {
      childrenInfo = "${AppStrings.children.tr} (";
      for (int i = 0; i < childrenAges.length; i++) {
        childrenInfo +=
        "${AppStrings.child.tr} ${i + 1}: ${childrenAges[i]} ${AppStrings.yearsOld.tr}";
        if (i != childrenAges.length - 1) {
          childrenInfo += ", ";
        }
      }
      childrenInfo += ")";
    } else {
      childrenInfo = "0 ${AppStrings.children.tr}";
    }

    return [
      "$totalRooms ${AppStrings.rooms.tr},",
      "$totalAdults ${AppStrings.adults.tr},",
      childrenInfo
    ].join(" ");
  }
}

// class GeoLocationRequest {
//
//   final double? latitude;
//   final double? longitude;
//   final num? radius;
//   final String? unit;
//
//   GeoLocationRequest({
//     this.latitude,
//     this.longitude,
//     this.radius,
//     this.unit,
//   });
//
//   static double get defaultRadius => 200;
//   static double get max => 200;
//
//   factory GeoLocationRequest.defaultLocation() {
//     return GeoLocationRequest(
//       latitude: 30.043490,
//       longitude: 31.235290,
//       radius: GeoLocationRequest.defaultRadius, // max is 200
//       unit: "km",
//     );
//   }
//
//   GeoLocationRequest.fromJson(dynamic json)
//       : latitude = json['latitude'],
//         longitude = json['longitude'],
//         radius = json['radius'],
//         unit = json['unit'];
//
//   Map<String, dynamic> toJson() => {
//     'latitude': latitude,
//     'longitude': longitude,
//     'radius': radius,
//     'unit': unit,
//   };
//
//   GeoLocationRequest copyWith({
//     double? latitude,
//     double? longitude,
//     int? radius,
//     String? unit,
//   }) {
//     return GeoLocationRequest(
//       latitude: latitude ?? this.latitude,
//       longitude: longitude ?? this.longitude,
//       radius: radius ?? this.radius,
//       unit: unit ?? this.unit,
//     );
//   }
//
// }