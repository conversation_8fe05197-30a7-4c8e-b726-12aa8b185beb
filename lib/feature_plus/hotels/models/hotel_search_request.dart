import 'dart:convert';

import 'package:fandooq/core/models/hotels/hotels.dart';

import '../../home/<USER>/guests_search.dart';

class HotelSearchRequest {
  final double latitude;
  final double longitude;
  final DateTime checkIn;
  final DateTime checkOut;
  final int adults;
  final int children;
  final int rooms;
  final String currency;
  final int limit;
  final List<PaxRoom> paxRooms;
  final String? hotelCode;

  HotelSearchRequest({
    required this.latitude,
    required this.longitude,
    required this.checkIn,
    required this.checkOut,
    required this.adults,
    required this.children,
    required this.rooms,
    this.currency = 'USD',
    this.limit = 100,
    required this.paxRooms,
    this.hotelCode,
  });

  Map<String, dynamic> toQueryParams() {
    return {
      'latitude': latitude.toString(),
      'longitude': longitude.toString(),
      'checkIn': checkIn.toIso8601String(),
      'checkOut': checkOut.toIso8601String(),
      'adults': adults.toString(),
      'children': children.toString(),
      'rooms': rooms.toString(),
      'currency': currency,
      'limit': limit.toString(),
      'paxRooms': jsonEncode(paxRooms.map((e) => e.toJson()).toList()),
      'hotelCode': hotelCode,
    };
  }

  HotelSearchRequest copyWith({
    double? latitude,
    double? longitude,
    DateTime? checkIn,
    DateTime? checkOut,
    int? adults,
    int? children,
    int? rooms,
    String? currency,
    int? limit,
    List<PaxRoom>? paxRooms,
    String? hotelCode,
  }) {
    return HotelSearchRequest(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
      adults: adults ?? this.adults,
      children: children ?? this.children,
      rooms: rooms ?? this.rooms,
      currency: currency ?? this.currency,
      limit: limit ?? this.limit,
      paxRooms: paxRooms ?? this.paxRooms,
      hotelCode: hotelCode ?? this.hotelCode,
    );
  }
}
