

import 'package:collection/collection.dart';
import 'package:fandooq/core/components/currency/lib/currency.dart';
import 'package:intl/intl.dart';

extension TboHotelAvailabilityListEx on List<TboHotelAvailability>{
  TboHotelAvailability? getByHotelCode(String code) {
    return firstWhereOrNull((e) => e.hotelCode == code);
  }
}


class TboHotelAvailability {

  final String? hotelCode;
  final String? currency;
  final num? minRate;
  final num? maxRate;
  final List<Room>? rooms;
  final String? bookingStatus;
  final bool? voucherStatus;
  final String? confirmationNumber;
  final String? invoiceNumber;
  final DateTime? checkIn;
  final DateTime? checkOut;
  final DateTime? bookingDate;
  final int? noOfRooms;
  final List<String>? rateConditions;
  final String? exchangeRate;

  // CancelPolicy? get nearestCancellationPolicy {
  //   var roomsS = rooms!.map((e) => e.cancelPolicies).expand((element) => (element!)).toList();
  //   var ratesS = roomsS.map((e) => e).toList();
  //   ratesS.sort((a, b) => a.date.compareTo(b.date));
  //   return ratesS.first;
  // }

  TboHotelAvailability({
    required this.hotelCode,
    required this.currency,
    required this.minRate,
    required this.maxRate,
    required this.rooms,
    required this.bookingStatus,
    required this.voucherStatus,
    required this.confirmationNumber,
    required this.invoiceNumber,
    required this.checkIn,
    required this.checkOut,
    required this.bookingDate,
    required this.noOfRooms,
    required this.rateConditions,
    this.exchangeRate,
  });

  factory TboHotelAvailability.fromJson(Map<String, dynamic> json) {
    List<NativeRoomsRoom> nativeRoomsRoom = ((json['Rooms'] ?? []) as List).map((e)=> NativeRoomsRoom.fromJson(e)).toList();
    var rooms = NativeRoomsRoom.groupRooms(nativeRoomsRoom);
    return TboHotelAvailability(
      hotelCode: json['HotelCode'],
      currency: json['Currency'],
      minRate: json['MinRate'],
      maxRate: json['MaxRate'],
      rooms: rooms,
      bookingStatus: json['BookingStatus'],
      voucherStatus: json['VoucherStatus'],
      confirmationNumber: json['ConfirmationNumber'],
      invoiceNumber: json['InvoiceNumber'],
      checkIn: json['CheckIn'] != null ? DateTime.parse(json['CheckIn']) : null,
      checkOut: json['CheckOut'] != null ? DateTime.parse(json['CheckOut']) : null,
      bookingDate: json['BookingDate'] != null ? DateTime.parse(json['BookingDate']) : null,
      noOfRooms: json['NoOfRooms'],
      rateConditions: List<String>.from((json['RateConditions'] ?? [])),
      exchangeRate: json['ExchangeRate'],
    );
  }

  TboHotelAvailability copyWith({
    String? hotelCode,
    String? currency,
    num? minRate,
    num? maxRate,
    List<Room>? rooms,
    String? bookingStatus,
    bool? voucherStatus,
    String? confirmationNumber,
    String? invoiceNumber,
    DateTime? checkIn,
    DateTime? checkOut,
    DateTime? bookingDate,
    int? noOfRooms,
    List<String>? rateConditions,
    String? exchangeRate,
  }) {
    return TboHotelAvailability(
      hotelCode: hotelCode ?? this.hotelCode,
      currency: currency ?? this.currency,
      minRate: minRate ?? this.minRate,
      maxRate: maxRate ?? this.maxRate,
      rooms: rooms ?? this.rooms,
      bookingStatus: bookingStatus ?? this.bookingStatus,
      voucherStatus: voucherStatus ?? this.voucherStatus,
      confirmationNumber: confirmationNumber ?? this.confirmationNumber,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
      bookingDate: bookingDate ?? this.bookingDate,
      noOfRooms: noOfRooms ?? this.noOfRooms,
      rateConditions: rateConditions ?? this.rateConditions,
      exchangeRate: exchangeRate ?? this.exchangeRate,
    );
  }

  get containsFreeCancellation => null;

  get containsBreakfastInclude => null;

  bool get canCancel => true;

  String get rateCommentsShort{
    String text = rateConditions?.join(".") ?? '';
    return text.split(". ").map((e)=>e.trim()).join("\n\n");
  }

  List<String> get rateCommentsFull{
    String text = rateConditions?.join(".") ?? '';
    return text.split(".").map((e)=>e.trim()).toList();
  }

  Rate get rate => rooms!.first.rates.first;
  Room get room => rooms!.first;

  Map<String, dynamic> toJson() => {
    'HotelCode': hotelCode,
    'Currency': currency,
    'Rooms': rooms?.map((r) => r.toJson()).toList(),
    'BookingStatus': bookingStatus,
    'VoucherStatus': voucherStatus,
    'ConfirmationNumber': confirmationNumber,
    'InvoiceNumber': invoiceNumber,
    'CheckIn': checkIn?.toIso8601String(),
    'CheckOut': checkOut?.toIso8601String(),
    'BookingDate': bookingDate?.toIso8601String(),
    'NoOfRooms': noOfRooms,
    'RateConditions': rateConditions,
    'ExchangeRate': exchangeRate,
  };




  Room? getMinRate({
    required bool? freeCancel,
    required bool? breakfast,
    required bool? halfBoard,
    required bool? roomOnly,
  }) {
    if (rooms == null || rooms!.isEmpty) return null;

    final filteredRooms = rooms!.where((room) {
      final hasBreakfast = room.rates.any((rate) => rate.hasBreakfast == true);
      final hasFreeCancel = room.rates.any((rate) => rate.hasFreeCancellation == true);
      final hasHalfBoard = room.rates.any((rate) => rate.hasHalfBoard == true);
      final hasRoomOnly = room.rates.any((rate) => rate.hasRoomOnly == true);

      if (breakfast == true && !hasBreakfast) return false;
      if (freeCancel == true && !hasFreeCancel) return false;
      if (halfBoard == true && !hasHalfBoard) return false;
      if (roomOnly == true && !hasRoomOnly) return false;

      return true;
    }).where((room) => room.rates.isNotEmpty).toList();

    if (filteredRooms.isEmpty) return null;

    filteredRooms.sort((a, b) => a.rates.first.totalFare!.compareTo(b.rates.first.totalFare!));

    return filteredRooms.first;
  }

  DateTime getCheckInTime(String time) {
    return parseHotelTime(time,checkIn!);
  }

  DateTime getCheckOutTime(String time) {
    return parseHotelTime(time,checkOut!);
  }

  DateTime parseHotelTime(String time, DateTime baseDate) {
    try {
      DateTime parsed;
      if (time.contains('AM') || time.contains('PM')) {
        parsed = DateFormat.jm().parse(time); // "2:00 PM"
      } else {
        parsed = DateFormat.Hm().parse(time); // "14:00"
      }
      return DateTime(
        baseDate.year,
        baseDate.month,
        baseDate.day,
        parsed.hour,
        parsed.minute,
      );
    } catch (e) {
      print('❌ Invalid time format: $time');
      return baseDate;
    }
  }


}






class Supplement {
  final int index;
  final String type;
  final String description;
  final double price;
  final String currency;

  Supplement({
    required this.index,
    required this.type,
    required this.description,
    required this.price,
    required this.currency,
  });

  factory Supplement.fromJson(Map<String, dynamic> json) {
    return Supplement(
      index: json['Index'] as int,
      type: json['Type'] as String,
      description: json['Description'] as String,
      price: (json['Price'] as num).toDouble(),
      currency: json['Currency'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Index': index,
      'Type': type,
      'Description': description,
      'Price': price,
      'Currency': currency,
    };
  }
}

class HotelDetails {
  final String hotelName;
  final String rating;
  final String addressLine1;
  final String map;
  final String city;

  HotelDetails({
    required this.hotelName,
    required this.rating,
    required this.addressLine1,
    required this.map,
    required this.city,
  });

  factory HotelDetails.fromJson(Map<String, dynamic> json) {
    return HotelDetails(
      hotelName: json['HotelName'],
      rating: json['Rating'],
      addressLine1: json['AddressLine1'],
      map: json['Map'],
      city: json['City'],
    );
  }

  Map<String, dynamic> toJson() => {
    'HotelName': hotelName,
    'Rating': rating,
    'AddressLine1': addressLine1,
    'Map': map,
    'City': city,
  };
}

class NativeRoomsRoom {
  final List<String>? name;
  final String? status;
  final String? bookingCode;
  final String? inclusion;
  final List<List<DayRate>>? dayRates;
  final num? totalFare;
  final String? totalFareHash;
  final String? totalFareHashWithCommission;
  final num? totalTax;
  final List<String>? roomPromotion;
  final List<String>? amenities;
  final List<CancelPolicy>? cancelPolicies;
  final List<List<Supplement>>? supplements;
  final String? mealType;
  final bool? isRefundable;
  final bool? withTransfers;

  NativeRoomsRoom({
    required this.name,
    required this.status,
    required this.bookingCode,
    required this.inclusion,
    required this.dayRates,
    required this.totalFare,
    required this.totalFareHash,
    required this.totalFareHashWithCommission,
    required this.totalTax,
    required this.roomPromotion,
    required this.cancelPolicies,
    required this.supplements,
    required this.mealType,
    required this.isRefundable,
    required this.withTransfers,
    required this.amenities,
  });

  factory NativeRoomsRoom.fromJson(Map<String, dynamic> json) {
    return NativeRoomsRoom(
      name: List<String>.from(json['Name'].map((e) => e.toString())).toList(),
      bookingCode: json['BookingCode'],
      status: json['Status'],
      inclusion: json['Inclusion'],
      dayRates: ((json['DayRates'] ?? []) as List)
          .map((list) => (list as List)
          .map((item) => DayRate.fromJson(item))
          .toList())
          .toList(),
      totalFare: json['TotalFare'],
      totalFareHash: json['TotalFareHash'],
      totalFareHashWithCommission: json['TotalFareHashWithCommission'],
      totalTax: json['TotalTax'],
      roomPromotion: handlePromotions(json['RoomPromotion']),
      amenities: handlePromotions(json['Amenities']),
      cancelPolicies: (json['CancelPolicies'] as List)
          .map((e) => CancelPolicy.fromJson(e).copyWith(totalFare: (json['TotalFare'] as num)))
          .toList(),
      supplements: ((json['Supplements'] ?? []) as List)
          .map((e) => ((e ?? []) as List).map((x)=> Supplement.fromJson(x)).toList())
          .toList(),
      mealType: json['MealType'],
      isRefundable: json['IsRefundable'],
      withTransfers: json['WithTransfers'],
    );
  }


  static List<String> handlePromotions(dynamic data){
    if(data is List){
      return List<String>.from((data ?? []).map((e) => e.toString()));
    }
    return [data.toString()];
  }

  Map<String, dynamic> toJson() => {
    'Name': name,
    'Status': status,
    'BookingCode': bookingCode,
    'Inclusion': inclusion,
    'DayRates': dayRates
        ?.map((list) => list.map((dr) => dr.toJson()).toList())
        .toList(),
    'TotalFare': totalFare,
    'TotalFareHash': totalFareHash,
    'TotalFareHashWithCommission': totalFareHashWithCommission,
    'TotalTax': totalTax,
    'RoomPromotion': roomPromotion,
    'Amenities': amenities,
    'CancelPolicies': cancelPolicies?.map((c) => c.toJson()).toList(),
    'Supplements': supplements?.map((c) => c.map((e)=> e.toJson()).toList()).toList(),
    'MealType': mealType,
    'IsRefundable': isRefundable,
    'WithTransfers': withTransfers,
  };


  static List<Room> groupRooms(List<NativeRoomsRoom> rooms) {
    final Map<String, Room> grouped = {};

    for (final room in rooms) {
      final key = room.name?.join("-");
      if (key == null) continue;

      grouped.putIfAbsent(
        key,
            () => Room(
          name: room.name!.first,
          status: room.status,
          rates: [],
        ),
      );

      grouped[key]!.rates.add(Rate.fromRoom(room));
    }

    // 🔽 ترتيب العروض داخل كل مجموعة غرف
    final groupedList = grouped.values.toList()
      ..forEach((group) =>
          group.rates.sort((a, b) => a.totalFare!.compareTo(b.totalFare!)));

    return groupedList;
  }




}

class DayRate {
  final double? basePrice;

  DayRate({required this.basePrice});

  factory DayRate.fromJson(Map<String, dynamic> json) {
    return DayRate(
      basePrice: (json['BasePrice'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() => {
    'BasePrice': basePrice,
  };
}

class CancelPolicy {
  final String? index;
  final String? fromDate;
  final String? chargeType;
  final num? cancellationCharge;

  CancelPolicy({
    required this.index,
    required this.fromDate,
    required this.chargeType,
    required this.cancellationCharge,
  });

  factory CancelPolicy.fromJson(Map<String, dynamic> json) {
    return CancelPolicy(
      index: json['Index'],
      fromDate: json['FromDate'],
      chargeType: json['ChargeType'],
      cancellationCharge: (json['CancellationCharge'] as num).toDouble(),
    );
  }

  DateTime get date {
    try {
      return DateFormat("dd-MM-yyyy HH:mm:ss").parse(fromDate!);
    } catch (e) {
      print('❌ Invalid date format: $fromDate');
      return DateTime.now();
    }
  }

  get nearestCancellationPolicy => null;

  bool get hasFreeCancellation => false;


  Map<String, dynamic> toJson() => {
    'Index': index,
    'FromDate': fromDate,
    'ChargeType': chargeType,
    'CancellationCharge': cancellationCharge,
  };

  /// ✅ copyWith method
  CancelPolicy copyWith({
    String? index,
    String? fromDate,
    String? chargeType,
    num? cancellationCharge,
    num? totalFare,
  }) {
    return CancelPolicy(
      index: index ?? this.index,
      fromDate: fromDate ?? this.fromDate,
      chargeType: chargeType ?? this.chargeType,
      cancellationCharge: cancellationCharge ?? this.cancellationCharge,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is CancelPolicy &&
        other.hashCode == hashCode;
  }

  @override
  int get hashCode => index.hashCode;

}

class Room {

  final String? name;
  final String? status;
  final List<Rate> rates;

  Room({
    required this.name,
    required this.status,
    required this.rates,
  });

  factory Room.fromJson(Map<String, dynamic> json) {
    return Room(
      name: json['Name'],
      status: json['Status'],
      rates: ((json['Rates'] ?? []) as List).map((e)=> Rate.fromJson(e).copyWith(roomName: json['Name'])).toList(),
    );
  }

  /// ✅ copyWith method
  Room copyWith({
    String? name,
    String? status,
    List<Rate>? rates,
  }) {
    return Room(
      name: name ?? this.name,
      status: status ?? this.status,
      rates: rates ?? this.rates,
    );
  }

  Map<String, dynamic> toJson() => {
    'Name': name,
    'Status': status,
    'Rates': rates.map((r) => r.toJson()).toList(),
  };

  get allotment => 1;

  get hasOffers => false;

}

class Rate {

  final int? pageId;
  final String? bookingCode;
  final String? inclusion;
  final List<List<DayRate>>? dayRates;
  final num? totalFare;
  final String? totalFareHash;
  final String? totalFareHashWithCommission;
  final num? totalTax;
  final List<String>? roomPromotion;
  final List<CancelPolicy>? cancelPolicies;
  final List<List<Supplement>>? supplements;
  final String? mealType;
  final bool? isRefundable;
  final bool? withTransfers;
  final String? roomName;

  String get tagRoomSelection => '$pageId-$bookingCode';

  Rate({
    required this.pageId,
    required this.bookingCode,
    required this.inclusion,
    required this.dayRates,
    required this.totalFare,
    required this.totalFareHash,
    required this.totalFareHashWithCommission,
    required this.totalTax,
    required this.roomPromotion,
    required this.cancelPolicies,
    required this.supplements,
    required this.mealType,
    required this.isRefundable,
    required this.withTransfers,
    required this.roomName,
  });


  factory Rate.fromRoom(NativeRoomsRoom room) => Rate(
    bookingCode: room.bookingCode,
    totalFare: room.totalFare,
    totalFareHash: room.totalFareHash,
    totalFareHashWithCommission: room.totalFareHashWithCommission,
    totalTax: room.totalTax,
    dayRates: room.dayRates,
    roomPromotion: room.roomPromotion,
    cancelPolicies: room.cancelPolicies,
    supplements: room.supplements,
    mealType: room.mealType,
    isRefundable: room.isRefundable,
    withTransfers: room.withTransfers,
    inclusion: room.inclusion,
    pageId: 0,
    roomName: room.name?.first,
  );

  factory Rate.fromJson(Map<String, dynamic> json) {
    return Rate(
      pageId: json['roomId'],
      bookingCode: json['BookingCode'],
      inclusion: json['Inclusion'],
      dayRates: ((json['DayRates'] ?? []) as List)
          .map((list) => (list as List)
          .map((item) => DayRate.fromJson(item))
          .toList())
          .toList(),
      totalFare: json['TotalFare'],
      totalFareHash: json['TotalFareHash'],
      totalFareHashWithCommission: json['TotalFareHashWithCommission'],
      totalTax: json['TotalTax'],
      roomPromotion: List<String>.from((json['RoomPromotion'] ?? []).map((e) => e.toString())),
      cancelPolicies: (json['CancelPolicies'] as List)
          .map((e) => CancelPolicy.fromJson(e).copyWith(totalFare: (json['TotalFare'] as num)))
          .toList(),
      supplements: ((json['Supplements'] ?? []) as List)
          .map((e) => ((e ?? []) as List).map((x)=> Supplement.fromJson(x)).toList())
          .toList(),
      mealType: json['MealType'],
      isRefundable: json['IsRefundable'],
      withTransfers: json['WithTransfers'],
      roomName: json['RoomName'],
    );
  }


  Map<String, dynamic> toJson() => {
    'roomId': pageId,
    'BookingCode': bookingCode,
    'Inclusion': inclusion,
    'DayRates': dayRates
        ?.map((list) => list.map((dr) => dr.toJson()).toList())
        .toList(),
    'TotalFare': totalFare,
    'TotalFareHash': totalFareHash,
    'TotalFareHashWithCommission': totalFareHashWithCommission,
    'TotalTax': totalTax,
    'RoomPromotion': roomPromotion,
    'CancelPolicies': cancelPolicies?.map((c) => c.toJson()).toList(),
    'Supplements': supplements?.map((c) => c.map((e)=> e.toJson()).toList()).toList(),
    'MealType': mealType,
    'IsRefundable': isRefundable,
    'WithTransfers': withTransfers,
    'RoomName': roomName,
  };

  Rate copyWith({
    int? roomId,
    String? bookingCode,
    String? inclusion,
    List<List<DayRate>>? dayRates,
    double? totalFare,
    String? totalFareHash,
    String? totalFareHashWithCommission,
    double? totalTax,
    List<String>? roomPromotion,
    List<CancelPolicy>? cancelPolicies,
    List<List<Supplement>>? supplements,
    String? mealType,
    bool? isRefundable,
    bool? withTransfers,
    String? roomName,
  }) {
    return Rate(
      pageId: roomId ?? this.pageId,
      bookingCode: bookingCode ?? this.bookingCode,
      inclusion: inclusion ?? this.inclusion,
      dayRates: dayRates ?? this.dayRates,
      totalFare: totalFare ?? this.totalFare,
      totalFareHash: totalFareHash ?? this.totalFareHash,
      totalFareHashWithCommission: totalFareHashWithCommission ?? this.totalFareHashWithCommission,
      totalTax: totalTax ?? this.totalTax,
      roomPromotion: roomPromotion ?? this.roomPromotion,
      cancelPolicies: cancelPolicies ?? this.cancelPolicies,
      supplements: supplements ?? this.supplements,
      mealType: mealType ?? this.mealType,
      isRefundable: isRefundable ?? this.isRefundable,
      withTransfers: withTransfers ?? this.withTransfers,
      roomName: roomName ?? this.roomName,
    );
  }

  get allotment => 1;



  bool get hasBreakfast =>
      mealType.containsInsensitive('breakfast') ||
          inclusion.containsInsensitive('breakfast');

  bool get hasFreeCancellation => isRefundable == true;

  bool get hasRoomOnly =>
      mealType.containsInsensitive('room_only') ||
          inclusion.containsInsensitive('room_only');

  bool get hasHalfBoard =>
      mealType.containsInsensitive('half_board') ||
          inclusion.containsInsensitive('half_board');

  bool get freeParking =>
      mealType.containsInsensitive('free self parking') ||
          inclusion.containsInsensitive('free self parking');

  bool get freeValet =>
      mealType.containsInsensitive('free valet parking') ||
          inclusion.containsInsensitive('free valet parking');

  bool get freeBreakFast =>
      mealType.containsInsensitive('free breakfast') ||
          inclusion.containsInsensitive('free breakfast');

  bool get breakFastBuffet =>
      mealType.containsInsensitive('breakfast buffet') ||
          inclusion.containsInsensitive('breakfast buffet');

  bool get freeBeachTransfer =>
      mealType.containsInsensitive('free beach transfer') ||
          inclusion.containsInsensitive('free beach transfer');

  bool get nonSmoking =>
      mealType.containsInsensitive('nonsmoking') ||
          inclusion.containsInsensitive('nonsmoking');

  bool get oneDoubleBed =>
      mealType.containsInsensitive('1 double bed') ||
          inclusion.containsInsensitive('1 double bed');

  bool get hasFullBoard =>
      mealType.containsInsensitive('full_board') ||
          inclusion.containsInsensitive('full_board');

  bool get privateSale =>
      roomPromotion?.any((p) => p.toLowerCase().contains('private sale')) ?? false;

  bool get hasFandBDiscount =>
      roomName.containsInsensitive('f&b discount') || roomName.containsInsensitive('fandb discount');

  bool get oneKingBed =>
      roomName.containsInsensitive('1 king bed');

  bool get twoTwinBeds =>
      roomName.containsInsensitive('2 twin beds');

  bool get oneQueenBed =>
      roomName.containsInsensitive('1 queen bed');

  bool get freeWifi =>
      mealType.containsInsensitive('free wifi') ||
          inclusion.containsInsensitive('free wifi');

  bool get seaView =>
      roomName.containsInsensitive('sea view');

  bool get cityView =>
      roomName.containsInsensitive('city view');

  bool get balcony =>
      roomName.containsInsensitive('balcony');

  bool get nonRefundable => !(isRefundable ?? false);

  bool get smokingAllowed => !nonSmoking;

  bool get hasPromotions => (roomPromotion ?? []).isNotEmpty;

  bool get hasOffers => discountPrice > 0;

  num get discountPrice {
    List<String> promotions = roomPromotion ?? [];
    for (String promotion in promotions) {
      if (promotion.contains('%')) {
        final RegExp regex = RegExp(r'(\d+(\.\d+)?)%');
        final match = regex.firstMatch(promotion);

        if (match != null) {
          final discountPercent = num.parse(match.group(1)!);
          final original = totalFare! / (1 - discountPercent / 100);
          final discount = original - totalFare!;
          return discount;
        }
      }
    }
    return 0;
  }


  num get priceBeforeDiscount {
    // لو مفيش خصم، رجع نفس السعر
    return totalFare! + discountPrice;
  }


  bool get hasSupplements => (supplements ?? []).isNotEmpty;

  num get excludeTaxesFee {
    final flatSupplements = (supplements ?? <List<Supplement>>[])
        .expand((List<Supplement> e) => e) // نحدد نوع e
        .where((s) => s.type.toLowerCase() == 'atproperty')
        .map((s) => s.price);
    return flatSupplements.isEmpty ? 0 : flatSupplements.reduce((a, b) => a + b);
  }

  List<Supplement> get excludeTaxesFeeData {
    final flatSupplements = (supplements ?? <List<Supplement>>[])
        .expand((List<Supplement> e) => e) // نحدد نوع e
        .where((s) => s.type.toLowerCase() == 'atproperty');
    return flatSupplements.toList();
  }

  String get excludeTaxesFeeCurrency {
    final flatSupplements = (supplements ?? <List<Supplement>>[])
        .expand((List<Supplement> e) => e)
        .where((s) => s.type.toLowerCase() == 'atproperty');

    if (flatSupplements.isEmpty) return '';

    final firstCurrency = flatSupplements.first.currency;

    final allSameCurrency = flatSupplements.every((s) => s.currency == firstCurrency);

    return allSameCurrency ? firstCurrency : 'Mixed';
  }

  bool get allIncludeTaxesAndFee => excludeTaxesFee == 0;

}

extension on String? {
  bool containsInsensitive(String keyword) {
    if (this == null) return false;
    return this!.toLowerCase().contains(keyword.toLowerCase());
  }
}



