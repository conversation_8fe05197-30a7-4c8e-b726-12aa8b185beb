// نماذج البيانات البسيطة للفنادق الجديدة

class HotelPlus {
  final String code;
  final String name;
  final String description;
  final int starRating;
  final String address;
  final String city;
  final String country;
  final String currency;
  final double? latitude;
  final double? longitude;
  final List<String> images;
  final List<String> amenities;
  final List<String> facilities;
  final HotelAvailabilityPlus? availability;
  final PricingPlus? pricing;

  HotelPlus({
    required this.code,
    required this.name,
    required this.description,
    required this.starRating,
    required this.address,
    required this.city,
    required this.country,
    required this.currency,
    this.latitude,
    this.longitude,
    required this.images,
    required this.amenities,
    required this.facilities,
    this.availability,
    this.pricing,
  });

  factory HotelPlus.fromJson(Map<String, dynamic> json) {
    final hotel = json['hotel'] ?? {};
    final availability = json['availability'];
    final pricing = json['pricing'];
    return HotelPlus(
      code: hotel['code']?.toString() ?? '',
      name: hotel['name'] ?? '',
      description: hotel['description'] ?? '',
      starRating: hotel['starRating'] ?? 0,
      address: hotel['address'] ?? '',
      city: hotel['city'] ?? '',
      country: hotel['country'] ?? '',
      currency: hotel['currency'] ?? 'USD',
      latitude: hotel['latitude']?.toDouble(),
      longitude: hotel['longitude']?.toDouble(),
      images: List<String>.from(hotel['images'] ?? []),
      amenities: List<String>.from(hotel['amenities'] ?? []),
      facilities: List<String>.from(hotel['facilities'] ?? []),
      availability: availability != null
          ? HotelAvailabilityPlus.fromJson(availability)
          : null,
      pricing: pricing != null ? PricingPlus.fromJson(pricing) : null,
    );
  }

  /// إنشاء HotelPlus من API الفنادق القريبة
  factory HotelPlus.fromNearbyApi(Map<String, dynamic> json) {
    return HotelPlus(
      code: json['code']?.toString() ?? '',
      name: json['name'] ?? '',
      description: '', // لا يوجد وصف في API القريبة
      starRating: json['starRating'] ?? 0,
      address: json['address'] ?? '',
      city: json['cityName'] ?? '',
      country: json['countryName'] ?? '',
      currency: 'USD',
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      images: List<String>.from(json['images'] ?? []),
      amenities: List<String>.from(json['amenities'] ?? []),
      facilities: [], // لا توجد مرافق في API القريبة
      availability: null, // لا توجد معلومات توفر
      pricing: null, // لا توجد معلومات أسعار
    );
  }

  // الحصول على أقل سعر
  num get minPrice {
    if (availability?.rooms.isEmpty ?? true) return 0.0;

    final allPrices = availability!.rooms
        .map((room) => room.totalFare)
        .where((price) => price > 0);

    return allPrices.isEmpty ? 0.0 : allPrices.reduce((a, b) => a < b ? a : b);
  }

  // الحصول على أعلى سعر
  num get maxPrice {
    if (availability?.rooms.isEmpty ?? true) return 0.0;

    final allPrices = availability!.rooms
        .map((room) => room.totalFare)
        .where((price) => price > 0);

    return allPrices.isEmpty ? 0.0 : allPrices.reduce((a, b) => a > b ? a : b);
  }

  // التحقق من وجود إلغاء مجاني
  bool get hasFreeCancellation {
    return availability?.rooms.any((room) => room.isRefundable) ?? false;
  }

  // التحقق من وجود إفطار
  bool get hasBreakfast {
    return availability?.rooms.any((room) => room.hasBreakfast) ?? false;
  }

  // التحقق من وجود WiFi
  bool get hasWiFi {
    return facilities.any((facility) =>
        facility.toLowerCase().contains('wifi') ||
        facility.toLowerCase().contains('internet'));
  }

  // التحقق من وجود مسبح
  bool get hasPool {
    return facilities.any((facility) =>
        facility.toLowerCase().contains('pool') ||
        facility.toLowerCase().contains('swimming'));
  }

  // الحصول على العروض الترويجية
  List<String> get promotions {
    List<String> promos = [];
    if (hasFreeCancellation) promos.add('Free Cancellation');
    if (hasBreakfast) promos.add('Breakfast Included');
    if (hasWiFi) promos.add('Free WiFi');
    if (hasPool) promos.add('Swimming Pool');

    // إضافة العروض من الغرف
    availability?.rooms.forEach((room) {
      if (room.roomPromotions.isNotEmpty) {
        promos.addAll(room.roomPromotions);
      }
    });

    return promos.toSet().toList(); // إزالة التكرار
  }
}

class HotelAvailabilityPlus {
  final int totalRooms;
  final List<RoomPlus> rooms;

  HotelAvailabilityPlus({
    required this.totalRooms,
    required this.rooms,
  });

  factory HotelAvailabilityPlus.fromJson(Map<String, dynamic> json) {
    return HotelAvailabilityPlus(
      totalRooms: json['totalRooms'] ?? 0,
      rooms: (json['rooms'] as List? ?? [])
          .map((room) => RoomPlus.fromJson(room))
          .toList(),
    );
  }
}

class RoomPlus {
  final String roomType;
  final String bookingCode;
  final String inclusion;
  final String mealType;
  final bool isRefundable;
  final bool withTransfers;
  final List<DailyRatePlus> dailyRates;
  final List<CancellationPolicyPlus> cancellationPolicies;
  final List<String> roomPromotions;
  final List<List<SupplementPlus>> supplements;
  final PricingPlus? pricing;

  RoomPlus({
    required this.roomType,
    required this.bookingCode,
    required this.inclusion,
    required this.mealType,
    required this.isRefundable,
    required this.withTransfers,
    required this.dailyRates,
    required this.cancellationPolicies,
    required this.roomPromotions,
    required this.supplements,
    this.pricing,
  });

  factory RoomPlus.fromJson(Map<String, dynamic> json) {
    return RoomPlus(
      roomType: json['roomType'] ?? '',
      bookingCode: json['bookingCode'] ?? '',
      inclusion: json['inclusion'] ?? '',
      mealType: json['mealType'] ?? '',
      isRefundable: json['isRefundable'] ?? false,
      withTransfers: json['withTransfers'] ?? false,
      dailyRates: (json['dailyRates'] as List? ?? [])
          .map((rate) => DailyRatePlus.fromJson(rate))
          .toList(),
      cancellationPolicies: (json['cancellationPolicies'] as List? ?? [])
          .map((policy) => CancellationPolicyPlus.fromJson(policy))
          .toList(),
      roomPromotions: List<String>.from(json['roomPromotions'] ?? []),
      supplements: (json['supplements'] as List? ?? [])
          .map((supplementGroup) => (supplementGroup as List? ?? [])
              .map((supplement) => SupplementPlus.fromJson(supplement))
              .toList())
          .toList(),
      pricing: json['pricing'] != null
          ? PricingPlus.fromJson(json['pricing'])
          : null,
    );
  }

  bool get hasBreakfast {
    return mealType.toLowerCase().contains('breakfast') ||
        inclusion.toLowerCase().contains('breakfast');
  }

  bool get hasHalfBoard {
    return mealType.toLowerCase().contains('half') ||
        inclusion.toLowerCase().contains('half');
  }

  bool get hasRoomOnly {
    return mealType.toLowerCase().contains('room') ||
        inclusion.toLowerCase().contains('room');
  }

  num get totalFare => pricing?.converted.price ?? 0.0;
  num get totalTax => pricing?.converted.tax ?? 0.0;
  num get totalPrice => totalFare;

  List<String> get promotions => roomPromotions;

  Map<String, dynamic> toJson() {
    return {
      'roomType': roomType,
      'bookingCode': bookingCode,
      'inclusion': inclusion,
      'mealType': mealType,
      'isRefundable': isRefundable,
      'withTransfers': withTransfers,
      'dailyRates': dailyRates.map((rate) => rate.toJson()).toList(),
      'cancellationPolicies':
          cancellationPolicies.map((policy) => policy.toJson()).toList(),
      'roomPromotions': roomPromotions,
      'supplements': supplements
          .map((group) =>
              group.map((supplement) => supplement.toJson()).toList())
          .toList(),
      'pricing': pricing?.toJson(),
    };
  }
}

class RatePlus {
  final double totalFare;
  final double totalTax;
  final bool isRefundable;
  final String mealType;
  final String inclusion;
  final String bookingCode;

  RatePlus({
    required this.totalFare,
    required this.totalTax,
    required this.isRefundable,
    required this.mealType,
    required this.inclusion,
    required this.bookingCode,
  });

  // التحقق من وجود إفطار
  bool get hasBreakfast {
    return mealType.toLowerCase().contains('breakfast') ||
        inclusion.toLowerCase().contains('breakfast');
  }

  // التحقق من وجود نصف إقامة
  bool get hasHalfBoard {
    return mealType.toLowerCase().contains('half') ||
        inclusion.toLowerCase().contains('half');
  }

  // التحقق من غرفة فقط
  bool get hasRoomOnly {
    return mealType.toLowerCase().contains('room') ||
        inclusion.toLowerCase().contains('room');
  }

  // السعر الإجمالي (convertedPrice already includes all taxes and charges)
  double get totalPrice =>
      totalFare; // totalFare = convertedPrice (includes everything)
}

// استجابة تفاصيل الفندق
class HotelDetailsResponse {
  final bool success;
  final String? message;
  final HotelDetailsData? data;
  final Map<String, dynamic>? meta;

  HotelDetailsResponse({
    required this.success,
    this.message,
    this.data,
    this.meta,
  });

  factory HotelDetailsResponse.fromJson(Map<String, dynamic> json) {
    return HotelDetailsResponse(
      success: json['success'] ?? false,
      message: json['message'],
      data:
          json['data'] != null ? HotelDetailsData.fromJson(json['data']) : null,
      meta: json['meta'],
    );
  }

  factory HotelDetailsResponse.error(String message) {
    return HotelDetailsResponse(
      success: false,
      message: message,
      data: null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data?.toJson(),
      'meta': meta,
    };
  }
}

// بيانات تفاصيل الفندق
class HotelDetailsData {
  final String code;
  final String name;
  final String description;
  final int starRating;
  final String address;
  final String city;
  final String country;
  final double? latitude;
  final double? longitude;
  final List<String> images;
  final List<String> amenities;
  final List<String> facilities;

  HotelDetailsData({
    required this.code,
    required this.name,
    required this.description,
    required this.starRating,
    required this.address,
    required this.city,
    required this.country,
    this.latitude,
    this.longitude,
    required this.images,
    required this.amenities,
    required this.facilities,
  });

  factory HotelDetailsData.fromJson(Map<String, dynamic> json) {
    return HotelDetailsData(
      code: json['code']?.toString() ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      starRating: json['starRating'] ?? 0,
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      images: List<String>.from(json['images'] ?? []),
      amenities: List<String>.from(json['amenities'] ?? []),
      facilities: List<String>.from(json['facilities'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'description': description,
      'starRating': starRating,
      'address': address,
      'city': city,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'images': images,
      'amenities': amenities,
      'facilities': facilities,
    };
  }

  // تحويل إلى HotelPlus للتوافق مع الكود الحالي
  HotelPlus toHotelPlus({
    String currency = 'USD',
    HotelAvailabilityPlus? availability,
    PricingPlus? pricing,
  }) {
    return HotelPlus(
      code: code,
      name: name,
      description: description,
      starRating: starRating,
      address: address,
      city: city,
      country: country,
      currency: currency,
      latitude: latitude,
      longitude: longitude,
      images: images,
      amenities: amenities,
      facilities: facilities,
      availability: availability,
      pricing: pricing,
    );
  }
}

// استجابة البحث
class HotelSearchResponse {
  final bool success;
  final String? message;
  final List<HotelPlus> hotels;
  final int totalAvailable;
  final int totalRequested;

  HotelSearchResponse({
    required this.success,
    this.message,
    required this.hotels,
    required this.totalAvailable,
    required this.totalRequested,
  });

  factory HotelSearchResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'] ?? {};
    final hotelsData = data['hotels'] ?? {};
    final hotelsList = hotelsData['hotels'] as List? ?? [];

    return HotelSearchResponse(
      success: json['success'] ?? false,
      message: json['message'],
      hotels: hotelsList.map((hotel) => HotelPlus.fromJson(hotel)).toList(),
      totalAvailable: hotelsData['totalAvailable'] ?? 0,
      totalRequested: hotelsData['totalRequested'] ?? 0,
    );
  }

  factory HotelSearchResponse.error(String message) {
    return HotelSearchResponse(
      success: false,
      message: message,
      hotels: [],
      totalAvailable: 0,
      totalRequested: 0,
    );
  }
}

class DailyRatePlus {
  final double basePrice;
  final double originalBasePrice;
  final double taxes;
  final String currency;

  DailyRatePlus({
    required this.basePrice,
    required this.originalBasePrice,
    required this.taxes,
    required this.currency,
  });

  factory DailyRatePlus.fromJson(Map<String, dynamic> json) {
    return DailyRatePlus(
      basePrice: (json['basePrice'] ?? 0.0).toDouble(),
      originalBasePrice: (json['originalBasePrice'] ?? 0.0).toDouble(),
      taxes: (json['taxes'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'USD',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'basePrice': basePrice,
      'originalBasePrice': originalBasePrice,
      'taxes': taxes,
      'currency': currency,
    };
  }
}

class CancellationPolicyPlus {
  final String fromDate;
  final String chargeType;
  final double cancellationCharge;

  CancellationPolicyPlus({
    required this.fromDate,
    required this.chargeType,
    required this.cancellationCharge,
  });

  factory CancellationPolicyPlus.fromJson(Map<String, dynamic> json) {
    return CancellationPolicyPlus(
      fromDate: json['FromDate'] ?? '',
      chargeType: json['ChargeType'] ?? '',
      cancellationCharge: (json['CancellationCharge'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'FromDate': fromDate,
      'ChargeType': chargeType,
      'CancellationCharge': cancellationCharge,
    };
  }
}

class SupplementPlus {
  final int index;
  final String type;
  final String description;
  final double price;
  final String currency;

  SupplementPlus({
    required this.index,
    required this.type,
    required this.description,
    required this.price,
    required this.currency,
  });

  factory SupplementPlus.fromJson(Map<String, dynamic> json) {
    return SupplementPlus(
      index: json['Index'] ?? 0,
      type: json['Type'] ?? '',
      description: json['Description'] ?? '',
      price: (json['Price'] ?? 0.0).toDouble(),
      currency: json['Currency'] ?? 'USD',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Index': index,
      'Type': type,
      'Description': description,
      'Price': price,
      'Currency': currency,
    };
  }
}

class PricingPlus {
  final OriginalPricing original;
  final ConvertedPricing converted;
  final MarkupPricing markup;
  final PricingMeta meta;

  PricingPlus({
    required this.original,
    required this.converted,
    required this.markup,
    required this.meta,
  });

  factory PricingPlus.fromJson(Map<String, dynamic> json) {
    print("sssssss ${json}");
    return PricingPlus(
      original: json['original'] != null
          ? OriginalPricing.fromJson(json['original'])
          : OriginalPricing.defaultVal(),
      converted: json['converted'] != null
          ? ConvertedPricing.fromJson(json['converted'])
          : ConvertedPricing.defaultVal(),
      markup: json['markup'] != null
          ? MarkupPricing.fromJson(json['markup'])
          : MarkupPricing.defaultVal(),
      meta: json['meta'] != null
          ? PricingMeta.fromJson(json['meta'])
          : PricingMeta.defaultVal(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'original': original.toJson(),
      'converted': converted.toJson(),
      'markup': markup.toJson(),
      'meta': meta.toJson(),
    };
  }
}

class OriginalPricing {
  final String currency;
  final num price;
  final num tax;
  final num total;

  OriginalPricing({
    required this.currency,
    required this.price,
    required this.tax,
    required this.total,
  });

  factory OriginalPricing.fromJson(Map<String, dynamic> json) {
    return OriginalPricing(
      currency: json['currency'],
      price: json['price'] ?? 0,
      tax: json['tax'] ?? 0,
      total: json['total'] ?? 0,
    );
  }

  factory OriginalPricing.defaultVal() {
    return OriginalPricing(
      currency: "",
      price: 0,
      tax: 0,
      total: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currency': currency,
      'price': price,
      'tax': tax,
      'total': total,
    };
  }
}

class ConvertedPricing {
  final num price;
  final num tax;
  final num total;
  final String currency;
  final num exchangeRate;
  final bool isConverted;
  final String note;
  final String source;

  ConvertedPricing({
    required this.price,
    required this.tax,
    required this.total,
    required this.currency,
    required this.exchangeRate,
    required this.isConverted,
    required this.note,
    required this.source,
  });

  factory ConvertedPricing.fromJson(Map<String, dynamic> json) {
    return ConvertedPricing(
      price: json['price'],
      tax: json['tax'],
      total: json['total'],
      currency: json['currency'],
      exchangeRate: json['exchangeRate'],
      isConverted: json['isConverted'],
      note: json['note'],
      source: json['source'],
    );
  }

  factory ConvertedPricing.defaultVal() {
    return ConvertedPricing(
      price: 0,
      tax: 0,
      total: 0,
      currency: "",
      exchangeRate: 0,
      isConverted: false,
      note: "",
      source: "",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'price': price,
      'tax': tax,
      'total': total,
      'currency': currency,
      'exchangeRate': exchangeRate,
      'isConverted': isConverted,
      'note': note,
      'source': source,
    };
  }
}

class MarkupPricing {
  final num percentage;
  final num amount;
  final String model;

  MarkupPricing({
    required this.percentage,
    required this.amount,
    required this.model,
  });

  factory MarkupPricing.fromJson(Map<String, dynamic> json) {
    return MarkupPricing(
      percentage: json['percentage'] ?? 0,
      amount: json['amount'] ?? 0,
      model: json['model'] ?? '',
    );
  }

  factory MarkupPricing.defaultVal() {
    return MarkupPricing(
      percentage: 0,
      amount: 0,
      model: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'percentage': percentage,
      'amount': amount,
      'model': model,
    };
  }
}

class PricingMeta {
  final int numberOfNights;
  final num basePricePerNight;
  final List<String> notes;

  PricingMeta({
    required this.numberOfNights,
    required this.basePricePerNight,
    required this.notes,
  });

  factory PricingMeta.fromJson(Map<String, dynamic> json) {
    return PricingMeta(
      numberOfNights: json['numberOfNights'],
      basePricePerNight: json['basePricePerNight'],
      notes: List<String>.from(json['notes']),
    );
  }

  factory PricingMeta.defaultVal() {
    return PricingMeta(
      numberOfNights: 0,
      basePricePerNight: 0,
      notes: [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'numberOfNights': numberOfNights,
      'basePricePerNight': basePricePerNight,
      'notes': notes,
    };
  }
}
