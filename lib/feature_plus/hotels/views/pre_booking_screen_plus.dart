import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../../../core/extensions/string.dart';
import '../controllers/pre_booking_controller_plus.dart';
import '../models/hotel_models.dart';
import '../models/pre_booking_models.dart';

class PreBookingScreenPlus extends StatefulWidget {
  const PreBookingScreenPlus({
    super.key,
  });
  @override
  State<PreBookingScreenPlus> createState() => _PreBookingScreenPlusState();
}

class _PreBookingScreenPlusState extends State<PreBookingScreenPlus>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _animationController;
  late Animation<double> _bounceAnimation;
  bool _hasScrolledToBottom = false;

  final controller = Get.put(PreBookingControllerPlus());

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    // Initialize animation for bounce effect
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticInOut,
    ));

    // Start the bounce animation
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _animationController.dispose();
    Get.delete<PreBookingControllerPlus>();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;

      // Check if user has scrolled to within 50 pixels of the bottom
      final hasReachedBottom = currentScroll >= maxScroll - 50;

      // Only update state if the status actually changed
      if (hasReachedBottom != _hasScrolledToBottom) {
        setState(() {
          _hasScrolledToBottom = hasReachedBottom;
        });

        if (hasReachedBottom) {
          // Stop the bounce animation when user reaches bottom
          _animationController.stop();
          print(
              '🟣 [PRE_BOOKING_SCREEN] User has scrolled to bottom - button enabled');
        } else {
          // Restart animation if user scrolls back up
          _animationController.repeat(reverse: true);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          // Clean up controller when leaving the screen
          _cleanupController();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text(
            'تأكيد الحجز',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 18,
            ),
          ),
          backgroundColor: ColorManager.primary,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: Obx(() {
          if (controller.isLoading) {
            return _buildLoadingState();
          }

          if (controller.preBookingData == null) {
            return _buildErrorState(controller);
          }

          return Column(
            children: [
              // Main content with scroll indicator
              Expanded(
                child: Stack(
                  children: [
                    SingleChildScrollView(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Timer Section
                          _buildTimerSection(controller),

                          const SizedBox(height: 16),

                          // Booking Summary
                          _buildBookingSummary(controller),

                          const SizedBox(height: 16),

                          // Price Breakdown
                          _buildPriceBreakdown(controller),

                          const SizedBox(height: 16),

                          // Cancellation Policy
                          _buildCancellationPolicy(controller),

                          const SizedBox(height: 16),

                          // Important Notes
                          _buildImportantNotes(controller),

                          const SizedBox(height: 16),

                          // Rate Conditions
                          _buildRateConditions(controller),

                          const SizedBox(height: 16),

                          // Daily Rates
                          _buildDailyRates(controller),

                          // Extra space to ensure scrolling is needed
                          const SizedBox(height: 100),
                        ],
                      ),
                    ),

                    // Scroll indicator with animation
                    if (!_hasScrolledToBottom)
                      AnimatedBuilder(
                        animation: _bounceAnimation,
                        builder: (context, child) {
                          return Positioned(
                            bottom: 20 + _bounceAnimation.value,
                            right: 20,
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: ColorManager.primary.withOpacity(0.9),
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.keyboard_arrow_down,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    'تمرر لأسفل',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),

              // Bottom Action Bar
              _buildBottomActionBar(controller),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(ColorManager.primary),
          ),
          SizedBox(height: 16),
          Text(
            'جاري معالجة طلب الحجز...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'يرجى الانتظار',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(PreBookingControllerPlus controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'حدث خطأ في معالجة الحجز',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم نتمكن من معالجة طلب الحجز، يرجى المحاولة مرة أخرى',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: controller.cancelPreBooking,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[300],
                    foregroundColor: Colors.black87,
                  ),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: controller.retryPreBooking,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorManager.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimerSection(PreBookingControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: controller.isExpired ? Colors.red[50] : Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: controller.isExpired ? Colors.red[300]! : Colors.orange[300]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            controller.isExpired ? Icons.timer_off : Icons.timer,
            color: controller.isExpired ? Colors.red : Colors.orange,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  controller.isExpired
                      ? 'preBooking.bookingExpired'.tr
                      : 'preBooking.bookingExpiresIn'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color:
                        controller.isExpired ? Colors.red : Colors.orange[800],
                  ),
                ),
                Text(
                  controller.isExpired
                      ? 'preBooking.pleaseRetry'.tr
                      : 'preBooking.timeRemaining'
                          .tr
                          .replaceAll('{time}', controller.remainingTime),
                  style: TextStyle(
                    fontSize: 12,
                    color: controller.isExpired
                        ? Colors.red[600]
                        : Colors.orange[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingSummary(PreBookingControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'preBooking.bookingSummary'.tr,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Hotel Name
          _buildSummaryRow('الفندق', controller.hotelName),

          // Room Type
          _buildSummaryRow('نوع الغرفة', controller.roomType),

          // Dates
          _buildSummaryRow(
            'تاريخ الوصول',
            _formatDate(controller.bookingRequest!.checkIn),
          ),
          _buildSummaryRow(
            'تاريخ المغادرة',
            _formatDate(controller.bookingRequest!.checkOut),
          ),

          // Guests
          _buildSummaryRow(
            'عدد النزلاء',
            '${controller.requiredGuests} بالغ${controller.requiredChildren > 0 ? '، ${controller.requiredChildren} طفل' : ''}',
          ),

          // Booking ID
          _buildSummaryRow(
              'رقم الحجز المسبق', controller.preBookingData!.bookingId),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceBreakdown(PreBookingControllerPlus controller) {
    if (controller.preBookingData?.firstRoom?.pricing == null) {
      return const SizedBox();
    }

    // استخدام PricingPlus من الغرفة المحددة
    final selectedRoom = controller.preBookingData!.firstRoom;
    final pricing = selectedRoom?.pricing;

    // استخدام السعر المحول النهائي فقط (convertedPrice يتضمن جميع الضرائب والرسوم)
    final totalPrice = pricing!.converted.price;
    final convertedTax = pricing.converted.tax; // للمرجعية فقط
    final currency = pricing.converted.currency;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'preBooking.priceBreakdown'.tr,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildPriceRow('السعر الإجمالي (شامل الضرائب)', totalPrice, currency),
          if (convertedTax > 0)
            _buildTaxReferenceRow(
                'منها الضرائب والرسوم', convertedTax, currency),
          const Divider(height: 24),
          _buildPriceRow(
            'المبلغ الإجمالي',
            totalPrice,
            currency,
            isTotal: true,
          ),

          // عرض الـ supplements إن وجدت
          if (_hasSelectedRoomSupplements(selectedRoom)) ...[
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),
            _buildSupplementsNote(selectedRoom!),
          ],

          // عرض العروض الترويجية إن وجدت
          if (_hasSelectedRoomPromotions(selectedRoom)) ...[
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),
            _buildRoomPromotionsNote(selectedRoom!),
          ],
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, num amount, String currency,
      {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? Colors.black : Colors.grey[700],
            ),
          ),
          Text(
            amount.toString().toMoneyWithSymbol(currencyCode: currency),
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? ColorManager.primary : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCancellationPolicy(PreBookingControllerPlus controller) {
    final room = controller.preBookingData?.firstRoom;
    final cancellationPolicies = room?.cancellationPolicies ?? [];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                controller.isRefundable ? Icons.check_circle : Icons.cancel,
                color: controller.isRefundable ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'preBooking.cancellationPolicy'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: controller.isRefundable ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            controller.isRefundable
                ? 'preBooking.refundable'.tr
                : 'preBooking.nonRefundable'.tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color:
                  controller.isRefundable ? Colors.green[700] : Colors.red[700],
            ),
          ),
          if (cancellationPolicies.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'preBooking.cancellationPolicyDetails'.tr,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            ...cancellationPolicies
                .map((policy) => _buildCancellationPolicyItem(policy)),
          ],
        ],
      ),
    );
  }

  Widget _buildCancellationPolicyItem(dynamic policy) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber,
            color: Colors.red[600],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${'preBooking.policyFromDate'.tr} ${policy.fromDate.toString()}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${'preBooking.policyChargeType'.tr} ${policy.chargeType.toString()}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[700],
                  ),
                ),
                Text(
                  '${'preBooking.policyCancellationCharge'.tr} ${policy.cancellationCharge.toString()}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.red[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImportantNotes(PreBookingControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: ColorManager.primary,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'preBooking.importantNotes'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildImportantNoteItem(
            'preBooking.importantNoteReviewTerms'.tr,
          ),
          _buildImportantNoteItem(
            'preBooking.importantNoteCheckGuestData'.tr,
          ),
          _buildImportantNoteItem(
            'preBooking.importantNoteExtraFees'.tr,
          ),
          _buildImportantNoteItem(
            'preBooking.importantNoteValidID'.tr,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar(PreBookingControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Cancel Button
            Expanded(
              child: OutlinedButton(
                onPressed: controller.cancelPreBooking,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: Colors.grey[400]!),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'preBooking.cancel'.tr,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Continue Button
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: (controller.isExpired || !_hasScrolledToBottom)
                    ? null
                    : controller.proceedToGuestDetails,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorManager.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: Text(
                  _hasScrolledToBottom
                      ? 'preBooking.continueToGuestDetails'.tr
                      : 'preBooking.readAllDetailsFirst'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: _hasScrolledToBottom ? Colors.white : Colors.white70,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImportantNoteItem(String note) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: const BoxDecoration(
              color: ColorManager.primary,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              note,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRateConditions(PreBookingControllerPlus controller) {
    final rateConditions = controller.rateConditions;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.description,
                  color: Colors.blue[700],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'preBooking.rateConditionsTitle'.tr,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // Content
          if (rateConditions.isEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    color: Colors.green[600],
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'preBooking.noSpecialConditions'.tr,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            )
          else
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: rateConditions
                    .map((condition) => _buildSimpleConditionItem(condition))
                    .toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSimpleConditionItem(String condition) {
    // Clean up the condition text
    String cleanCondition = condition
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&amp;', '&')
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&quot;', '"')
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .trim();

    // Skip empty or very short conditions
    if (cleanCondition.length < 10) {
      return const SizedBox.shrink();
    }

    // Translate common terms to Arabic
    String displayText = _translateCondition(cleanCondition);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 2),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.blue[600],
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              displayText,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _translateCondition(String condition) {
    // Handle specific condition patterns
    if (condition.startsWith('CheckIn Time-Begin:')) {
      final time = condition.substring(19).trim();
      return '${'preBooking.checkInTimeBegin'.tr} $time';
    } else if (condition.startsWith('CheckOut Time:')) {
      final time = condition.substring(14).trim();
      return '${'preBooking.checkOutTime'.tr} $time';
    } else if (condition.startsWith('Minimum CheckIn Age:')) {
      final age = condition.substring(20).trim();
      return '${'preBooking.minimumCheckInAge'.tr} $age';
    } else if (condition.startsWith('Cards Accepted:')) {
      final cards = condition.substring(15).trim();
      return '${'preBooking.cardsAccepted'.tr} $cards';
    }

    // Handle common keywords
    String translated = condition;

    // Payment related
    if (translated.toLowerCase().contains('payment')) {
      translated = translated.replaceAll(
          RegExp(r'payment', caseSensitive: false), 'preBooking.payment'.tr);
    }
    if (translated.toLowerCase().contains('deposit')) {
      translated = translated.replaceAll(
          RegExp(r'deposit', caseSensitive: false), 'preBooking.deposit'.tr);
    }

    // Cancellation related
    if (translated.toLowerCase().contains('cancellation')) {
      translated = translated.replaceAll(
          RegExp(r'cancellation', caseSensitive: false),
          'preBooking.cancellation'.tr);
    }
    if (translated.toLowerCase().contains('refund')) {
      translated = translated.replaceAll(
          RegExp(r'refund', caseSensitive: false), 'preBooking.refund'.tr);
    }

    // Hotel amenities
    if (translated.toLowerCase().contains('wifi')) {
      translated = translated.replaceAll(
          RegExp(r'wifi', caseSensitive: false), 'preBooking.wifi'.tr);
    }
    if (translated.toLowerCase().contains('parking')) {
      translated = translated.replaceAll(
          RegExp(r'parking', caseSensitive: false), 'preBooking.parking'.tr);
    }
    if (translated.toLowerCase().contains('breakfast')) {
      translated = translated.replaceAll(
          RegExp(r'breakfast', caseSensitive: false),
          'preBooking.breakfast'.tr);
    }

    return translated;
  }

  Widget _buildDailyRates(PreBookingControllerPlus controller) {
    final room = controller.preBookingData?.firstRoom;

    if (room == null || room.dailyRates.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                color: Colors.orange[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'preBooking.dailyRates'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...room.dailyRates.map((rate) => _buildDailyRateItem(rate)),
        ],
      ),
    );
  }

  Widget _buildDailyRateItem(dynamic rate) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          _buildDetailRow('preBooking.basePrice'.tr,
              '${rate.basePrice.toStringAsFixed(2)} ${rate.currency}'),
          _buildDetailRow('preBooking.originalPrice'.tr,
              '${rate.originalBasePrice.toStringAsFixed(2)} ${rate.currency}'),
          _buildDetailRow('preBooking.taxes'.tr,
              '${rate.taxes.toStringAsFixed(2)} ${rate.currency}'),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getMealTypeArabic(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'room_only':
        return 'preBooking.mealTypeRoomOnly'.tr;
      case 'breakfast':
        return 'preBooking.mealTypeBreakfast'.tr;
      case 'half_board':
        return 'preBooking.mealTypeHalfBoard'.tr;
      case 'full_board':
        return 'preBooking.mealTypeFullBoard'.tr;
      case 'all_inclusive':
        return 'preBooking.mealTypeAllInclusive'.tr;
      default:
        return mealType;
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  /// Check if selected room has supplements
  bool _hasSelectedRoomSupplements(RoomPlus? room) {
    if (room == null) return false;

    return room.supplements.isNotEmpty &&
        room.supplements.any((group) => group.isNotEmpty);
  }

  /// Build supplements note widget
  Widget _buildSupplementsNote(RoomPlus room) {
    // Get first supplement for display
    for (final group in room.supplements) {
      if (group.isNotEmpty) {
        final supplement = group.first;
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.orange[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Additional ${supplement.currency} ${supplement.price.toStringAsFixed(0)} may be paid at the property',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      }
    }

    return const SizedBox.shrink();
  }

  /// Check if selected room has promotions
  bool _hasSelectedRoomPromotions(RoomPlus? room) {
    if (room == null) return false;

    return room.roomPromotions.isNotEmpty;
  }

  /// Build room promotions note widget
  Widget _buildRoomPromotionsNote(RoomPlus room) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_offer,
                color: Colors.green[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Special Offers',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...room.roomPromotions.map((promotion) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.green[600],
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        promotion,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.green[700],
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  /// Build tax reference row (for display purposes only)
  Widget _buildTaxReferenceRow(String label, num amount, String currency) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} $currency',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// Clean up controller when leaving the screen
  void _cleanupController() {
    try {
      print('🧹 [PRE_BOOKING] Cleaning up controller...');

      if (Get.isRegistered<PreBookingControllerPlus>()) {
        Get.delete<PreBookingControllerPlus>();
        print('✅ [PRE_BOOKING] PreBookingController cleaned up');
      }
    } catch (e) {
      print('❌ [PRE_BOOKING] Error cleaning up controller: $e');
    }
  }
}
