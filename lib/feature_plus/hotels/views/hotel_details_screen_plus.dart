import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../controllers/hotel_details_controller_plus.dart';
import '../../user/controllers/user_controller_plus.dart';
import '../models/hotel_models.dart';
import 'widgets/hotel_image_gallery_plus.dart';
import 'widgets/hotel_info_section_plus.dart';
import 'widgets/hotel_amenities_section_plus.dart';
import 'widgets/hotel_rooms_section_plus.dart';
import 'widgets/hotel_booking_bottom_bar_plus.dart';
import 'widgets/hotel_search_params_section_plus.dart';

class HotelDetailsScreenPlus extends StatefulWidget {
  const HotelDetailsScreenPlus({super.key});
  @override
  State<HotelDetailsScreenPlus> createState() => _HotelDetailsScreenPlusState();
}

class _HotelDetailsScreenPlusState extends State<HotelDetailsScreenPlus> {
  final controller = Get.put(HotelDetailsControllerPlus());

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 500), () {
        controller.scrollToRoomsSection();
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<HotelDetailsControllerPlus>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: Column(
        children: [
          // Main content
          Expanded(
            child: CustomScrollView(
              controller: controller.scrollController,
              physics: const BouncingScrollPhysics(),
              slivers: [
                // App Bar with Image Gallery
                _buildAppBar(controller),

                // Search Parameters Section
                const SliverToBoxAdapter(
                  child: HotelSearchParamsSectionPlus(),
                ),

                // Hotel Information with improved spacing
                const SliverToBoxAdapter(
                  child: HotelInfoSectionPlus(),
                ),

                // Elegant Section Divider
                SliverToBoxAdapter(
                  child: _buildSectionDivider(),
                ),

                // Hotel Rooms Section
                SliverToBoxAdapter(
                  child: Container(
                    key: controller.roomsSectionKey,
                    child: const HotelRoomsSectionPlus(),
                  ),
                ),

                // Elegant Section Divider
                SliverToBoxAdapter(
                  child: _buildSectionDivider(),
                ),

                // Hotel Amenities Section
                const SliverToBoxAdapter(
                  child: HotelAmenitiesSectionPlus(),
                ),

                // Bottom spacing for booking bar
                const SliverToBoxAdapter(
                  child: SizedBox(height: 100),
                ),
              ],
            ),
          ),

          // Bottom Booking Bar
          const HotelBookingBottomBarPlus(),
        ],
      ),
    );
  }

  Widget _buildAppBar(HotelDetailsControllerPlus controller) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: ColorManager.primary,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Get.back(),
      ),
      actions: [
        // Refresh button
        Obx(() => IconButton(
              icon: controller.isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.refresh, color: Colors.white),
              onPressed:
                  controller.isLoading ? null : controller.refreshHotelDetails,
              tooltip: 'تحديث تفاصيل الفندق',
            )),
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: controller.shareHotel,
        ),
        // GetBuilder<UserControllerPlus>(
        //   builder: (userController) {
        //     final isFavorite =
        //         userController.isFavorite(controller.hotel?.code ?? '');
        //     return IconButton(
        //       icon: Icon(
        //         isFavorite ? Icons.favorite : Icons.favorite_border,
        //         color: Colors.white,
        //       ),
        //       onPressed: controller.toggleFavorite,
        //     );
        //   },
        // ),
      ],
      flexibleSpace: const FlexibleSpaceBar(
        background: HotelImageGalleryPlus(),
      ),
    );
  }

  Widget _buildSectionDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 24),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.transparent,
                    Colors.grey[300]!,
                    Colors.grey[400]!,
                    Colors.grey[300]!,
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              Icons.hotel_rounded,
              size: 16,
              color: Colors.grey[400],
            ),
          ),
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.transparent,
                    Colors.grey[300]!,
                    Colors.grey[400]!,
                    Colors.grey[300]!,
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
