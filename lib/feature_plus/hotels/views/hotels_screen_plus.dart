import 'package:fandooq/feature_plus/home/<USER>/home_controller_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/components/default_loading.dart';
import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../controllers/hotels_controller_plus.dart';
import '../models/hotel_models.dart';
import '../routes/hotels_routes_plus.dart';
import '../../settings/controllers/settings_controller.dart';

class HotelsScreenPlus extends StatefulWidget {
  const HotelsScreenPlus({super.key});
  @override
  State<HotelsScreenPlus> createState() => _HotelsScreenPlusState();
}

class _HotelsScreenPlusState extends State<HotelsScreenPlus> {
  final controller = Get.put(HotelsControllerPlus());

  @override
  void initState() {
    // بدء البحث فوراً عند دخول الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.searchHotelsWithData();
    });
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<HotelsControllerPlus>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HotelsControllerPlus>(
      builder: (controller) {
        return Scaffold(
          body: RefreshIndicator(
            onRefresh: controller.refreshHotels,
            child: CustomScrollView(
              slivers: [
                // App Bar مع Animation مثل المسافر
                SliverAppBar(
                  expandedHeight: 140,
                  floating: false,
                  pinned: true,
                  backgroundColor: ColorManager.primary,
                  foregroundColor: ColorManager.white,
                  title: Obx(() {
                    return Text(
                      controller.locationName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                      ),
                    );
                  }),
                  centerTitle: true,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            ColorManager.primary,
                            ColorManager.primaryLight,
                            ColorManager.primary.withOpacity(0.8),
                            ColorManager.primaryLight.withOpacity(0.9),
                          ],
                          stops: const [0.0, 0.3, 0.7, 1.0],
                        ),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              ColorManager.primary.withOpacity(0.1),
                            ],
                          ),
                        ),
                        child: SafeArea(
                          child: Padding(
                            padding: const EdgeInsets.only(
                                top: 60, left: 16, right: 16, bottom: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // الصف الثاني - تفاصيل إضافية
                                Row(
                                  children: [
                                    // عدد الضيوف
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.15),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(
                                            Icons.people,
                                            color: ColorManager.white,
                                            size: 14,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '${controller.adults} Adults',
                                            style: const TextStyle(
                                              color: ColorManager.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    const SizedBox(width: 8),

                                    // عدد الغرف
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.15),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(
                                            Icons.bed,
                                            color: ColorManager.white,
                                            size: 14,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '${controller.rooms} Room${controller.rooms > 1 ? 's' : ''}',
                                            style: const TextStyle(
                                              color: ColorManager.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    const Spacer(),

                                    // حالة البحث
                                    GetBuilder<HotelsControllerPlus>(
                                      builder: (controller) => Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10, vertical: 6),
                                        decoration: BoxDecoration(
                                          color: controller.hasData
                                              ? Colors.green.withOpacity(0.2)
                                              : Colors.orange.withOpacity(0.2),
                                          borderRadius:
                                              BorderRadius.circular(15),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              controller.hasData
                                                  ? Icons.check_circle
                                                  : Icons.search,
                                              color: ColorManager.white,
                                              size: 12,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              controller.hasData
                                                  ? 'Found'
                                                  : 'Searching',
                                              style: TextStyle(
                                                color: ColorManager.white
                                                    .withOpacity(0.9),
                                                fontSize: 11,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  actions: [
                    // Currency selector button
                    Obx(() {
                      final settingsController = Get.find<SettingsController>();
                      final currentCurrency =
                          settingsController.appSettings.currency;

                      return InkWell(
                        onTap: () async {
                          // Check if currency actually changed
                          final currencyChanged =
                              await settingsController.changeCurrency();

                          // Only reload hotels if currency actually changed
                          if (currencyChanged) {
                            print('💱 Currency changed, refreshing hotels...');
                            await controller.refreshSearchWithNewCurrency();
                          } else {
                            print(
                                '💱 Currency not changed, skipping hotel refresh');
                          }
                        },
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          margin: const EdgeInsets.only(right: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            currentCurrency,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      );
                    }),

                    // أيقونة الفلتر
                    IconButton(
                      icon: Stack(
                        children: [
                          const Icon(Icons.tune, size: 24),
                          if (controller.showFreeCancellationOnly ||
                              controller.showBreakfastIncludedOnly)
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  color: ColorManager.orange,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ),
                        ],
                      ),
                      onPressed: () =>
                          _showAdvancedFiltersBottomSheet(context, controller),
                    ),
                    // أيقونة الترتيب
                    IconButton(
                      icon: const Icon(Icons.sort, size: 24),
                      onPressed: () =>
                          _showSortBottomSheet(context, controller),
                    ),
                  ],
                ),

                // فلاتر اختصارية
                SliverToBoxAdapter(
                  child: _buildQuickFilters(controller),
                ),

                // محتوى الصفحة
                Obx(() => _buildSliverBody(controller)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.hotel,
            size: 100,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 20),
          Text(
            'Welcome to Hotels Plus',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'Tap the search button to find hotels',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: () =>
                Get.find<HotelsControllerPlus>().searchHotelsWithData(),
            icon: const Icon(Icons.search),
            label: const Text('Search Hotels'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorManager.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHotelLoadingSkeleton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة الفندق - Skeleton
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(12)),
              color: Colors.grey[300],
            ),
            child: _buildShimmerEffect(),
          ),

          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اسم الفندق - Skeleton
                Container(
                  height: 20,
                  width: 250,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.grey[300],
                  ),
                  child: _buildShimmerEffect(),
                ),

                const SizedBox(height: 8),

                // العنوان - Skeleton
                Container(
                  height: 14,
                  width: 180,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.grey[300],
                  ),
                  child: _buildShimmerEffect(),
                ),

                const SizedBox(height: 12),

                // النجوم والتقييم - Skeleton
                Row(
                  children: [
                    // النجوم
                    Row(
                      children: List.generate(
                          5,
                          (index) => Container(
                                margin: const EdgeInsets.only(right: 2),
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(2),
                                  color: Colors.grey[300],
                                ),
                                child: _buildShimmerEffect(),
                              )),
                    ),

                    const SizedBox(width: 8),

                    // التقييم
                    Container(
                      height: 14,
                      width: 40,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: Colors.grey[300],
                      ),
                      child: _buildShimmerEffect(),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // السعر والحجز - Skeleton
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 12,
                          width: 60,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: Colors.grey[300],
                          ),
                          child: _buildShimmerEffect(),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          height: 18,
                          width: 80,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: Colors.grey[300],
                          ),
                          child: _buildShimmerEffect(),
                        ),
                      ],
                    ),

                    // زر الحجز
                    Container(
                      height: 36,
                      width: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[300],
                      ),
                      child: _buildShimmerEffect(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerEffect() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Colors.grey[300]!,
            Colors.grey[100]!,
            Colors.white.withOpacity(0.8),
            Colors.grey[100]!,
            Colors.grey[300]!,
          ],
          stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message, HotelsControllerPlus controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red[400],
            ),
            const SizedBox(height: 20),
            Text(
              'Oops! Something went wrong',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 10),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () => controller.refreshHotels(),
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorManager.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(HotelsControllerPlus controller) {
    return Center(
      child: TweenAnimationBuilder<double>(
        duration: const Duration(milliseconds: 800),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.scale(
            scale: 0.8 + (0.2 * value),
            child: Opacity(
              opacity: value,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: ColorManager.lightGrey.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.hotel_outlined,
                      size: 80, // حجم متوازن
                      color: ColorManager.primary.withOpacity(0.6),
                    ),
                  ),
                  const SizedBox(height: 32),
                  Text(
                    'hotelsScreen.noHotelsFound'.tr,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      'Try adjusting your search criteria or filters to find more hotels',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[500],
                        height: 1.4,
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => controller.resetFilters(),
                        icon: const Icon(Icons.clear_all, size: 20),
                        label: const Text('Reset Filters'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorManager.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: () {

                        },
                        icon: const Icon(Icons.refresh, size: 20),
                        label: const Text('Refresh'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorManager.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuickFilters(HotelsControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: ColorManager.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // أزرار الترتيب وحفظ الفلاتر
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // زر الترتيب
                PopupMenuButton<SortOption>(
                  icon: const Icon(Icons.sort, size: 20),
                  tooltip: 'ترتيب النتائج',
                  onSelected: controller.sortHotels,
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: SortOption.priceAsc,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.arrow_upward,
                              size: 16,
                              color:
                                  controller.currentSort == SortOption.priceAsc
                                      ? ColorManager.primary
                                      : Colors.grey),
                          const SizedBox(width: 8),
                          const Text('السعر: من الأقل للأعلى'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: SortOption.priceDesc,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.arrow_downward,
                              size: 16,
                              color:
                                  controller.currentSort == SortOption.priceDesc
                                      ? ColorManager.primary
                                      : Colors.grey),
                          const SizedBox(width: 8),
                          const Text('السعر: من الأعلى للأقل'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: SortOption.rating,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.star,
                              size: 16,
                              color: controller.currentSort == SortOption.rating
                                  ? ColorManager.primary
                                  : Colors.grey),
                          const SizedBox(width: 8),
                          const Text('التقييم: الأعلى أولاً'),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(width: 8),

                // زر حفظ الفلاتر
                Obx(() => IconButton(
                      icon: Icon(
                        controller.hasSavedFilters
                            ? Icons.bookmark
                            : Icons.bookmark_border,
                        size: 20,
                        color: controller.hasSavedFilters
                            ? ColorManager.primary
                            : Colors.grey,
                      ),
                      tooltip: 'حفظ الفلاتر',
                      onPressed: controller.saveCurrentFilters,
                    )),

                // زر تحميل الفلاتر المحفوظة
                if (controller.hasSavedFilters)
                  IconButton(
                    icon: const Icon(Icons.restore, size: 20),
                    tooltip: 'تحميل الفلاتر المحفوظة',
                    onPressed: controller.loadSavedFilters,
                  ),

                const SizedBox(width: 16),

                const Text(
                  'Quick Filters',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(width: 8),

                // زر مسح الفلاتر
                Obx(() {
                  final hasActiveFilters =
                      controller.showFreeCancellationOnly ||
                          controller.showBreakfastIncludedOnly ||
                          controller.showWiFiFilter ||
                          controller.showPoolFilter ||
                          controller.selectedStarRating > 0 ||
                          controller.maxPriceFilter < 1000;

                  return AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    transitionBuilder:
                        (Widget child, Animation<double> animation) {
                      return SlideTransition(
                        position: animation.drive(
                          Tween(
                              begin: const Offset(1.0, 0.0), end: Offset.zero),
                        ),
                        child: FadeTransition(opacity: animation, child: child),
                      );
                    },
                    child: hasActiveFilters
                        ? Container(
                            key: const ValueKey('clear_button'),
                            child: TextButton.icon(
                              onPressed: controller.resetFilters,
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                backgroundColor:
                                    ColorManager.primary.withOpacity(0.1),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                              ),
                              icon: const Icon(
                                Icons.clear_all,
                                size: 18,
                                color: ColorManager.primary,
                              ),
                              label: const Text(
                                'Clear All',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: ColorManager.primary,
                                ),
                              ),
                            ),
                          )
                        : const SizedBox.shrink(key: ValueKey('empty')),
                  );
                }),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // الفلاتر الاختصارية
          SizedBox(
            height: 60, // ارتفاع متوازن
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // فلتر الإلغاء المجاني
                  Obx(() => _buildQuickFilterChip(
                        'hotelsScreen.freeCancellation'.tr,
                        Icons.cancel_outlined,
                        controller.showFreeCancellationOnly,
                        controller.toggleFreeCancellationFilter,
                      )),

                  const SizedBox(width: 8),

                  // فلتر الإفطار
                  Obx(() => _buildQuickFilterChip(
                        'hotelsScreen.breakfast'.tr,
                        Icons.restaurant,
                        controller.showBreakfastIncludedOnly,
                        controller.toggleBreakfastFilter,
                      )),

                  const SizedBox(width: 8),

                  // فلتر النجوم
                  Obx(() => _buildQuickFilterChip(
                        'hotelsScreen.fourPlusStars'.tr,
                        Icons.star,
                        controller.selectedStarRating >= 4,
                        () => controller.setStarRating(
                            controller.selectedStarRating >= 4 ? 0 : 4),
                      )),

                  const SizedBox(width: 8),

                  // فلتر شامل الضرائب والرسوم
                  // Removed tax toggle chip - always show final prices

                  const SizedBox(width: 8),

                  // فلتر السعر
                  Obx(() => _buildQuickFilterChip(
                        'hotelsScreen.under100'.tr.replaceAll('{symbol}',
                            CurrencySymbols.getSymbol(controller.currency)),
                        Icons.attach_money,
                        controller.maxPriceFilter <= 100,
                        () => controller.setPriceRange(
                            0,
                            controller.maxPriceFilter <= 100
                                ? controller.dynamicMaxPrice
                                : 100),
                      )),

                  const SizedBox(width: 8),

                  // فلتر WiFi
                  Obx(() => _buildQuickFilterChip(
                        'hotelsScreen.freeWiFi'.tr,
                        Icons.wifi,
                        controller.showWiFiFilter,
                        controller.toggleWiFiFilter,
                      )),

                  const SizedBox(width: 8),

                  // فلتر المسبح
                  Obx(() => _buildQuickFilterChip(
                        'hotelsScreen.pool'.tr,
                        Icons.pool,
                        controller.showPoolFilter,
                        controller.togglePoolFilter,
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilterChip(
    String label,
    IconData icon,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
          decoration: BoxDecoration(
            color: isSelected
                ? ColorManager.primary
                : ColorManager.lightGrey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: isSelected ? ColorManager.primary : ColorManager.grey,
              width: 1.5,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: ColorManager.primary.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : [],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  icon,
                  size: 18, // حجم متوسط ومتوازن
                  color:
                      isSelected ? ColorManager.white : ColorManager.darkGrey,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color:
                      isSelected ? ColorManager.white : ColorManager.darkGrey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSliverBody(HotelsControllerPlus controller) {
    final state = controller.state;

    if (state is HotelsInitialStatePlus) {
      return SliverFillRemaining(
        child: _buildInitialState(),
      );
    }

    if (state is HotelsLoadingStatePlus) {
      return SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) => _buildHotelLoadingSkeleton(),
          childCount: 6, // عرض 6 skeleton items
        ),
      );
    }

    if (state is HotelsErrorStatePlus) {
      return SliverFillRemaining(
        child: _buildErrorState(state.message, controller),
      );
    }

    if (state is HotelsLoadedStatePlus) {
      final hotels = state.filteredHotels;

      if (hotels.isEmpty) {
        return SliverFillRemaining(
          child: _buildEmptyState(controller),
        );
      }

      return SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index == 0) {
              // شريط المعلومات في الأعلى
              return _buildInfoBar(controller);
            }

            final hotelIndex = index - 1;
            if (hotelIndex < hotels.length) {
              final hotel = hotels[hotelIndex];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: _buildHotelCard(hotel),
              );
            }

            return null;
          },
          childCount: hotels.length + 1, // +1 for info bar
        ),
      );
    }

    return SliverFillRemaining(
      child: Center(
        child: Text('hotelsScreen.unknownState'.tr),
      ),
    );
  }

  Widget _buildInfoBar(HotelsControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: ColorManager.primaryLight.withOpacity(0.1),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: ColorManager.primary, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'hotelsScreen.hotelsFound'
                  .tr
                  .replaceAll('{count}', '${controller.hotels.length}'),
              style: const TextStyle(
                color: ColorManager.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (controller.totalHotels > controller.hotels.length)
            Text(
              'of ${controller.totalHotels} total',
              style: TextStyle(
                color: ColorManager.primaryLight,
                fontSize: 12,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHotelCard(HotelPlus hotel) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: ColorManager.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          print('Hotel tapped: ${hotel.name}');
          // Navigate to hotel details
          // HotelsNavigation.toHotelDetails(
          //   hotel: hotel,
          //   scrollToRooms: false,
          // );

          var request = HomeControllerPlus.to.getRequest;

          HotelsNavigation.toHotelDetails(
            hotel: hotel,
            scrollToRooms: false,
            additionalArguments: {
              "fromDeepLink": true,
              "checkInDate": request.stay?.checkIn,
              "checkOutDate": request.stay?.checkOut,
              "paxRooms": request.paxRooms, // Add paxRooms to arguments
              "searchWithAvailability": true, // Flag to trigger availability search
            },
          );

        },
        borderRadius: BorderRadius.circular(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الفندق
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                color: Colors.grey[300],
              ),
              child: hotel.images.isNotEmpty
                  ? ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                      child: Image.network(
                        hotel.images.first,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholderImage();
                        },
                      ),
                    )
                  : _buildPlaceholderImage(),
            ),

            // محتوى البطاقة
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم الفندق والتقييم
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              hotel.name,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: ColorManager.black,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            // تقييم النجوم
                            Row(
                              children: [
                                ...List.generate(
                                  hotel.starRating,
                                  (index) => const Icon(
                                    Icons.star,
                                    color: Colors.amber,
                                    size: 16,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${hotel.starRating} star hotel',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // السعر
                      if (hotel.minPrice > 0)
                        Builder(builder: (context) {
                          // Check if controller exists, if not create it
                          HotelsControllerPlus controller;
                          try {
                            controller = Get.find<HotelsControllerPlus>();
                          } catch (e) {
                            // Controller not found, create a new one
                            controller = Get.put(HotelsControllerPlus());
                          }

                          final price = hotel.minPrice; // السعر النهائي دائماً
                          final currency = controller.getCurrencyForHotel(
                              hotel); // العملة من PricingPlus

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                'hotelPricing.from'.tr,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                              Text(
                                price
                                    .toString()
                                    .toMoneyWithSymbol(currencyCode: currency),
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: ColorManager.primary,
                                ),
                              ),
                              Text(
                                _hotelHasSupplements(hotel)
                                    ? 'hotelPricing.startingFrom'.tr
                                    : 'hotelPricing.finalPriceInclusive'.tr,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: _hotelHasSupplements(hotel)
                                      ? Colors.grey[500]
                                      : Colors.green[600],
                                ),
                              ),
                              Text(
                                'hotelPricing.perStay'.tr,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          );
                        }),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // الموقع
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          '${hotel.city}, ${hotel.country}',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // العروض الترويجية
                  if (hotel.promotions.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'hotels.specialOffers'.tr,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: ColorManager.primary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Wrap(
                          spacing: 6,
                          runSpacing: 4,
                          children: hotel.promotions
                              .take(3)
                              .map(
                                (promo) => Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: ColorManager.orange.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                        color: ColorManager.orange
                                            .withOpacity(0.3)),
                                  ),
                                  child: Text(
                                    promo,
                                    style: const TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                      color: ColorManager.orange,
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                        const SizedBox(height: 8),
                      ],
                    ),

                  // المميزات
                  Wrap(
                    spacing: 6,
                    runSpacing: 4,
                    children: [
                      if (hotel.hasFreeCancellation)
                        _buildFeatureChip(
                          'hotelFeatures.freeCancellation'.tr,
                          Colors.green,
                          Icons.check_circle_outline,
                        ),
                      if (hotel.hasBreakfast)
                        _buildFeatureChip(
                          'hotelFeatures.breakfastIncluded'.tr,
                          ColorManager.orange,
                          Icons.restaurant,
                        ),
                      // إضافة المزيد من المميزات من facilities
                      if (hotel.facilities
                          .any((f) => f.toLowerCase().contains('wifi')))
                        _buildFeatureChip(
                          'hotelFeatures.freeWiFi'.tr,
                          ColorManager.primary,
                          Icons.wifi,
                        ),
                      if (hotel.facilities
                          .any((f) => f.toLowerCase().contains('pool')))
                        _buildFeatureChip(
                          'hotelFeatures.pool'.tr,
                          Colors.cyan,
                          Icons.pool,
                        ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Supplements status (extra charges or all inclusive)
                  _buildHotelSupplementsStatus(hotel),

                  // Hotel promotions (if any)
                  if (_hotelHasPromotions(hotel)) _buildHotelPromotions(hotel),

                  // زر الحجز
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Navigate to hotel details and auto-scroll to rooms
                        var request = HomeControllerPlus.to.getRequest;

                        HotelsNavigation.toHotelDetails(
                          hotel: hotel,
                          scrollToRooms: true,
                          additionalArguments: {
                            "fromDeepLink": true,
                            "checkInDate": request.stay?.checkIn,
                            "checkOutDate": request.stay?.checkOut,
                            "paxRooms": request.paxRooms, // Add paxRooms to arguments
                            "searchWithAvailability": true, // Flag to trigger availability search
                          },
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorManager.primary,
                        foregroundColor: ColorManager.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text(
                        'hotelPricing.seeAvailability'.tr,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.hotel,
              size: 48,
              color: Colors.grey[500],
            ),
            const SizedBox(height: 8),
            Text(
              'No Image',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureChip(String label, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label.tr,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showAdvancedFiltersBottomSheet(
      BuildContext context, HotelsControllerPlus controller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildAdvancedFiltersSheet(controller),
    );
  }

  void _showSortBottomSheet(
      BuildContext context, HotelsControllerPlus controller) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSortSheet(controller),
    );
  }

  Widget _buildAdvancedFiltersSheet(HotelsControllerPlus controller) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(Get.context!).size.height * 0.85,
      ),
      decoration: const BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'hotels.filters'.tr,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    controller.resetFilters();
                  },
                  child: Text('hotels.resetAll'.tr),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(Get.context!),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Filters Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Price Range
                  _buildFilterSection(
                    'hotels.priceRange'.tr,
                    Icons.attach_money,
                    [
                      _buildPriceRangeFilter(controller),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Star Rating
                  _buildFilterSection(
                    'hotels.starRating'.tr,
                    Icons.star,
                    [
                      _buildStarRatingFilter(controller),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Amenities
                  _buildFilterSection(
                    'hotels.amenities'.tr,
                    Icons.local_offer,
                    [
                      Obx(() => _buildSwitchTile(
                            'hotels.freeCancellation'.tr,
                            'hotels.flexibleBooking'.tr,
                            controller.showFreeCancellationOnly,
                            controller.toggleFreeCancellationFilter,
                          )),
                      Obx(() => _buildSwitchTile(
                            'hotels.breakfastIncluded'.tr,
                            'hotels.startDayRight'.tr,
                            controller.showBreakfastIncludedOnly,
                            controller.toggleBreakfastFilter,
                          )),
                      Obx(() => _buildSwitchTile(
                            'hotels.freeWifi'.tr,
                            'hotels.stayConnected'.tr,
                            controller.showWiFiFilter,
                            controller.toggleWiFiFilter,
                          )),
                      Obx(() => _buildSwitchTile(
                            'hotels.swimmingPool'.tr,
                            'hotels.relaxUnwind'.tr,
                            controller.showPoolFilter,
                            controller.togglePoolFilter,
                          )),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Price Information
                  _buildFilterSection(
                    'hotels.priceInfo'.tr,
                    Icons.info_outline,
                    [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green[200]!),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.green[600],
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'hotels.allPricesIncludeTax'.tr,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Apply Button
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: ColorManager.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(Get.context!),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorManager.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Obx(() => Text(
                      '${'hotels.show'.tr} ${controller.totalHotels} ${'hotels.hotels'.tr}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    )),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortSheet(HotelsControllerPlus controller) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(Get.context!).size.height * 0.7,
      ),
      decoration: const BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'hotels.sortBy'.tr,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(Get.context!),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Sort Options
          Flexible(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Obx(() => _buildSortOption(
                          'sorting.priceLowToHigh'.tr,
                          'sorting.priceLowToHighDesc'.tr,
                          controller.sortBy == 'price_low_to_high',
                          () => controller.setSortBy('price_low_to_high'),
                        )),
                    Obx(() => _buildSortOption(
                          'sorting.priceHighToLow'.tr,
                          'sorting.priceHighToLowDesc'.tr,
                          controller.sortBy == 'price_high_to_low',
                          () => controller.setSortBy('price_high_to_low'),
                        )),
                    Obx(() => _buildSortOption(
                          'sorting.starRating'.tr,
                          'sorting.starRatingDesc'.tr,
                          controller.sortBy == 'star_rating',
                          () => controller.setSortBy('star_rating'),
                        )),
                    Obx(() => _buildSortOption(
                          'sorting.hotelName'.tr,
                          'sorting.hotelNameDesc'.tr,
                          controller.sortBy == 'name',
                          () => controller.setSortBy('name'),
                        )),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for filters
  Widget _buildFilterSection(
      String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: ColorManager.primary),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildSwitchTile(
      String title, String subtitle, bool value, VoidCallback onChanged) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: (_) => onChanged(),
            activeColor: ColorManager.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRangeFilter(HotelsControllerPlus controller) {
    return Obx(() => Column(
          children: [
            Row(
              children: [
                Text('priceRange.title'
                    .tr
                    .replaceAll(
                        '{min}',
                        controller.minPriceFilter
                            .toInt()
                            .toDouble()
                            .toMoneyWithSymbol(
                                currencyCode: controller.currency))
                    .replaceAll(
                        '{max}',
                        controller.maxPriceFilter
                            .toInt()
                            .toDouble()
                            .toMoneyWithSymbol(
                                currencyCode: controller.currency))),
              ],
            ),
            const SizedBox(height: 16),
            RangeSlider(
              values: RangeValues(
                  controller.minPriceFilter, controller.maxPriceFilter),
              min: controller.dynamicMinPrice,
              max: controller.dynamicMaxPrice,
              divisions: 20,
              labels: RangeLabels(
                controller.minPriceFilter
                    .toInt()
                    .toDouble()
                    .toMoneyWithSymbol(currencyCode: controller.currency),
                controller.maxPriceFilter
                    .toInt()
                    .toDouble()
                    .toMoneyWithSymbol(currencyCode: controller.currency),
              ),
              onChanged: (RangeValues values) {
                controller.setPriceRange(values.start, values.end);
              },
            ),
          ],
        ));
  }

  Widget _buildStarRatingFilter(HotelsControllerPlus controller) {
    return Obx(() => Wrap(
          spacing: 8,
          children: [
            for (int i = 1; i <= 5; i++)
              FilterChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ...List.generate(
                        i,
                        (index) => const Icon(Icons.star,
                            size: 16, color: Colors.amber)),
                    if (i < 5) const Text(' & up'),
                  ],
                ),
                selected: controller.selectedStarRating == i,
                onSelected: (selected) {
                  controller.setStarRating(selected ? i : 0);
                },
              ),
          ],
        ));
  }

  Widget _buildSortOption(String title, String subtitle, bool isSelected,
      [VoidCallback? onTap]) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          onTap?.call();
          Navigator.pop(Get.context!);
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? ColorManager.primary : ColorManager.grey,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected
                ? ColorManager.primaryLight.withOpacity(0.1)
                : ColorManager.white,
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title.tr,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected
                            ? ColorManager.primary
                            : ColorManager.black,
                      ),
                    ),
                    Text(
                      subtitle.tr,
                      style: TextStyle(
                        fontSize: 12,
                        color: isSelected
                            ? ColorManager.primaryLight
                            : ColorManager.darkGrey,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                const Icon(
                  Icons.check_circle,
                  color: ColorManager.primary,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Check if hotel has any rooms with supplements
  bool _hotelHasSupplements(HotelPlus hotel) {
    if (hotel.availability?.rooms == null) return false;

    return hotel.availability!.rooms.any((room) =>
        room.supplements.isNotEmpty &&
        room.supplements.any((group) => group.isNotEmpty));
  }

  /// Build hotel supplements status widget
  Widget _buildHotelSupplementsStatus(HotelPlus hotel) {
    if (_hotelHasSupplements(hotel)) {
      // Get first supplement for price display
      final firstSupplement = _getFirstHotelSupplement(hotel);

      // Show orange warning for extra charges with price
      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange[50],
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.orange[200]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.monetization_on,
              color: Colors.orange[600],
              size: 14,
            ),
            const SizedBox(width: 4),
            if (firstSupplement != null) ...[
              Text(
                '${firstSupplement.currency} ${firstSupplement.price.toStringAsFixed(0)} ',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.orange[800],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
            Text(
              'hotels.extraCharges'.tr,
              style: TextStyle(
                fontSize: 11,
                color: Colors.orange[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    } else {
      // Show green confirmation for all inclusive
      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green[600],
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              'hotels.allInclusive'.tr,
              style: TextStyle(
                fontSize: 11,
                color: Colors.green[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }
  }

  /// Get first supplement from hotel for price display
  SupplementPlus? _getFirstHotelSupplement(HotelPlus hotel) {
    if (hotel.availability?.rooms == null) return null;

    for (final room in hotel.availability!.rooms) {
      for (final group in room.supplements) {
        if (group.isNotEmpty) {
          return group.first;
        }
      }
    }
    return null;
  }

  /// Check if hotel has any rooms with promotions
  bool _hotelHasPromotions(HotelPlus hotel) {
    if (hotel.availability?.rooms == null) return false;

    return hotel.availability!.rooms
        .any((room) => room.roomPromotions.isNotEmpty);
  }

  /// Build hotel promotions widget
  Widget _buildHotelPromotions(HotelPlus hotel) {
    // Get first room with promotions
    final roomWithPromotions = hotel.availability!.rooms
        .firstWhere((room) => room.roomPromotions.isNotEmpty);

    // Show only first promotion to keep it compact
    final firstPromotion = roomWithPromotions.roomPromotions.first;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.local_offer,
            color: Colors.green[600],
            size: 14,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              firstPromotion,
              style: TextStyle(
                fontSize: 10,
                color: Colors.green[700],
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
