import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import '../../../core/extensions/string.dart';
import '../../../core/theme/color_manager.dart';
import '../controllers/payment_controller_plus.dart';
import '../models/payment_models.dart';

class PaymentScreenPlus extends StatefulWidget {
  const PaymentScreenPlus({super.key});
  @override
  State<PaymentScreenPlus> createState() => _PaymentScreenPlusState();
}

class _PaymentScreenPlusState extends State<PaymentScreenPlus> {
  // Get controller instance
  PaymentControllerPlus get controller => Get.find<PaymentControllerPlus>();

  // Helper method to calculate nights
  int _calculateNights(DateTime checkIn, DateTime checkOut) {
    return checkOut.difference(checkIn).inDays;
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {}
      },
      child: Scaffold(
        backgroundColor: ColorManager.scaffold,
        appBar: AppBar(
          title: Text(
            'payment.title'.tr,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: ColorManager.white,
            ),
          ),
          backgroundColor: ColorManager.primary,
          foregroundColor: ColorManager.white,
          elevation: 0,
          centerTitle: true,
          systemOverlayStyle: ColorManager.statusBarPrimary,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: ColorManager.white),
            onPressed: controller.goBackToGuestDetails,
          ),
        ),
        body: GetBuilder<PaymentControllerPlus>(builder: (controller) {
          if (controller.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // Booking Summary Card
              _buildBookingSummaryCard(),

              // Secure Payment Status (if in progress)
              if (controller.isProcessingPayment ||
                  controller.isConfirmingPayment)
                _buildSecurePaymentStatusCard(),

              // Payment Methods and Form
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPaymentMethodsSection(),
                      const SizedBox(height: 24),
                      _buildPaymentFormSection(),
                      const SizedBox(height: 24),
                      _buildPriceBreakdownCard(),
                      const SizedBox(height: 100), // Space for bottom button
                    ],
                  ),
                ),
              ),
            ],
          );
        }),
        bottomNavigationBar: _buildBottomPaymentButton(),
      ),
    );
  }

  Widget _buildBookingSummaryCard() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ColorManager.primary.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Builder(builder: (context) {
        final hotel = controller.hotel;
        final room = controller.selectedRoom;
        final request = controller.bookingRequest;

        if (hotel == null || room == null || request == null) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hotel name with icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: ColorManager.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.hotel,
                    color: ColorManager.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    hotel.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: ColorManager.primary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Compact info in rows
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildCompactInfo(
                    Icons.calendar_today,
                    '${_formatDate(request.checkIn)} - ${_formatDate(request.checkOut)}',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 1,
                  child: _buildCompactInfo(
                    Icons.nights_stay,
                    '${_calculateNights(request.checkIn, request.checkOut)} ${'payment.nights'.tr}',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildCompactInfo(
                    Icons.people,
                    '${request.adults} ${'payment.adults'.tr}, ${request.children} ${'payment.children'.tr}',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 1,
                  child: _buildCompactInfo(
                    Icons.bed,
                    '${request.rooms} ${'payment.rooms'.tr}',
                  ),
                ),
              ],
            ),
          ],
        );
      }),
    );
  }

  Widget _buildCompactInfo(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: ColorManager.grey),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: ColorManager.darkGrey,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethodsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'payment.method'.tr,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ColorManager.primary,
          ),
        ),
        const SizedBox(height: 16),
        GetBuilder<PaymentControllerPlus>(
          builder: (controller) {
            // Filter payment methods based on platform
            final availableMethods = controller.paymentMethods.where((method) {
              // Show Apple Pay only on iOS
              if (method.type == 'apple_pay') {
                return GetPlatform.isIOS;
              }
              // Show Google Pay only on Android
              if (method.type == 'google_pay') {
                return GetPlatform.isAndroid;
              }
              // Show other methods on all platforms
              return true;
            }).toList();

            return Column(
              children: availableMethods.map((method) {
                return _buildPaymentMethodTile(method);
              }).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPaymentMethodTile(PaymentMethodPlus method) {
    return GetBuilder<PaymentControllerPlus>(builder: (controller) {
      final isSelected = controller.selectedPaymentMethod?.id == method.id;

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: ColorManager.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? ColorManager.primary : ColorManager.shadowGray,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: ColorManager.primary.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: ListTile(
          leading: _buildPaymentMethodIcon(method.type),
          title: Text(
            method.name,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: method.processingFee != null && method.processingFee! > 0
              ? Text(
                  'رسوم معالجة: ${method.processingFee} ${controller.currency}',
                )
              : null,
          trailing: Radio<String>(
            value: method.id,
            groupValue: controller.selectedPaymentMethod?.id,
            activeColor: ColorManager.primary,
            onChanged: (value) {
              controller.selectPaymentMethod(method);
            },
          ),
          onTap: () {
            controller.selectPaymentMethod(method);
          },
        ),
      );
    });
  }

  Widget _buildPaymentMethodIcon(String type) {
    IconData iconData;
    Color color;

    switch (type) {
      case 'credit_card':
        iconData = Icons.credit_card;
        color = ColorManager.primary;
        break;
      case 'debit_card':
        iconData = Icons.payment;
        color = ColorManager.green;
        break;
      case 'paypal':
        iconData = Icons.account_balance_wallet;
        color = ColorManager.orange;
        break;
      case 'apple_pay':
        iconData = Icons.apple;
        color = Colors.black;
        break;
      case 'google_pay':
        iconData = Icons.account_balance_wallet;
        color = const Color(0xFF4285F4);
        break;
      default:
        iconData = Icons.payment;
        color = ColorManager.grey;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(iconData, color: color, size: 24),
    );
  }

  // Build secure payment status card
  // Build secure payment status card
  Widget _buildSecurePaymentStatusCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorManager.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ColorManager.primary.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ColorManager.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.security,
                  color: ColorManager.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'payment.securePayment'.tr, // "Secure Payment"
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColorManager.primary,
                  ),
                ),
              ),
              if (controller.isProcessingPayment ||
                  controller.isConfirmingPayment)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(ColorManager.primary),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          GetBuilder<PaymentControllerPlus>(builder: (controller) {
            String statusText = 'payment.creatingPaymentIntent'.tr;

            if (controller.isConfirmingPayment) {
              statusText = 'payment.confirmingPaymentMethod'.tr;
            } else if (controller.paymentIntentStatus.isNotEmpty) {
              switch (controller.paymentIntentStatus.toLowerCase()) {
                case 'requires_payment_method':
                  statusText = 'payment.awaitingPaymentMethod'.tr;
                  break;
                case 'requires_confirmation':
                  statusText = 'payment.awaitingConfirmation'.tr;
                  break;
                case 'requires_action':
                  statusText = 'payment.requiresAdditionalAuth'.tr;
                  break;
                case 'processing':
                  statusText = 'payment.processing'.tr;
                  break;
                case 'requires_capture':
                  statusText = 'payment.capturingFunds'.tr;
                  break;
                default:
                  statusText =
                      '${'payment.status'.tr}: ${controller.paymentIntentStatus}';
              }
            }

            return Text(
              statusText,
              style: const TextStyle(
                fontSize: 14,
                color: ColorManager.grey,
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPaymentFormSection() {
    return GetBuilder<PaymentControllerPlus>(builder: (controller) {
      final selectedMethod = controller.selectedPaymentMethod;

      if (selectedMethod == null) {
        return const SizedBox.shrink();
      }

      if (selectedMethod.type == 'credit_card' ||
          selectedMethod.type == 'debit_card') {
        return _buildCreditCardForm();
      } else {
        return _buildAlternativePaymentInfo(selectedMethod);
      }
    });
  }

  Widget _buildCreditCardForm() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ColorManager.primary.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'payment.cardDetails'.tr,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: ColorManager.primary,
            ),
          ),
          const SizedBox(height: 20),

          // Stripe CardFormField
          CardFormField(
            controller: controller.cardFormController,
            style: CardFormStyle(
              borderColor: ColorManager.shadowGray,
              borderRadius: 8,
              borderWidth: 1,
              cursorColor: ColorManager.primary,
              fontSize: 16,
              placeholderColor: ColorManager.grey,
              textColor: ColorManager.black,
            ),
            onCardChanged: (card) {
              // Update UI when card details change
              controller.update();
            },
          ),

          const SizedBox(height: 16),

          // Card Holder Name (still needed for billing details)
          TextFormField(
            controller: controller.cardHolderController,
            textCapitalization: TextCapitalization.words,
            decoration: InputDecoration(
              labelText: 'payment.cardHolderNameLabel'.tr,
              hintText: 'payment.cardHolderNameHint'.tr,
              prefixIcon: const Icon(Icons.person),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Test Cards Buttons
          _buildTestCardButtons(),
        ],
      ),
    );
  }

  Widget _buildAlternativePaymentInfo(PaymentMethodPlus method) {
    IconData icon;
    Color iconColor;
    String titleKey;
    String descriptionKey;

    switch (method.type) {
      case 'apple_pay':
        icon = Icons.apple;
        iconColor = Colors.black;
        titleKey = 'payment.applePayTitle';
        descriptionKey = 'payment.applePayDescription';
        break;
      case 'google_pay':
        icon = Icons.account_balance_wallet;
        iconColor = const Color(0xFF4285F4);
        titleKey = 'payment.googlePayTitle';
        descriptionKey = 'payment.googlePayDescription';
        break;
      default:
        icon = Icons.info_outline;
        iconColor = Colors.blue[600]!;
        titleKey = 'payment.redirectTitle';
        descriptionKey = 'payment.redirectDescription';
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, size: 48, color: iconColor),
          const SizedBox(height: 16),
          Text(
            titleKey.tr.replaceAll('{name}', method.name),
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Text(
            descriptionKey.tr,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          if (method.type == 'apple_pay' || method.type == 'google_pay') ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.security, color: Colors.green[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'payment.secureAndEncrypted'.tr,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTestCardButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.science, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'payment.testCardsTitle'.tr,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'payment.testCardsDescription'.tr,
            style: TextStyle(fontSize: 12, color: Colors.blue[600]),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                flex: 3,
                child: ElevatedButton.icon(
                  onPressed: controller.showTestCards,
                  icon: const Icon(Icons.credit_card, size: 18),
                  label: Text(
                    'payment.selectTestCard'.tr,
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 1,
                child: ElevatedButton.icon(
                  onPressed: controller.clearCardData,
                  icon: const Icon(Icons.clear, size: 18),
                  label: Text(
                    'payment.clear'.tr,
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 4,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceBreakdownCard() {
    return GetBuilder<PaymentControllerPlus>(builder: (controller) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'payment.priceDetails'.tr,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // السعر الإجمالي
            _buildPriceRow(
              'payment.totalWithTax'.tr,
              controller.preBookingPricing?.converted.price ?? 0,
              controller.currency,
            ),

            // الضريبة والرسوم
            if ((controller.preBookingPricing?.converted.tax ?? 0) > 0)
              _buildTaxReferenceRow(
                'payment.taxIncluded'.tr,
                controller.preBookingPricing!.converted.tax,
                controller.currency,
              ),

            // رسوم المعالجة
            if (controller.processingFee > 0) ...[
              const SizedBox(height: 8),
              _buildPriceRow(
                'payment.processingFee'.tr,
                controller.processingFee,
                controller.currency,
              ),
            ],

            const Divider(height: 24),

            // المجموع النهائي
            _buildPriceRow(
              'payment.finalTotal'.tr,
              controller.finalAmount,
              controller.currency,
              isTotal: true,
            ),
          ],
        ),
      );
    });
  }

  Widget _buildPriceRow(
    String label,
    num amount,
    String currency, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.black : Colors.grey[600],
            ),
          ),
          Text(
            amount.toString().toMoneyWithSymbol(currencyCode: currency),
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: isTotal ? Colors.blue[600] : Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  /// Build tax reference row (for display purposes only)
  Widget _buildTaxReferenceRow(String label, num amount, String currency) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} $currency',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomPaymentButton() {
    return GetBuilder<PaymentControllerPlus>(builder: (controller) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ColorManager.white,
          boxShadow: [
            BoxShadow(
              color: ColorManager.primary.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: controller.canProceedToPayment &&
                      !controller.isProcessingPayment &&
                      !controller.isConfirmingPayment
                  ? controller.processPayment
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorManager.primary,
                foregroundColor: ColorManager.white,
                disabledBackgroundColor: ColorManager.disableBottom,
                disabledForegroundColor: ColorManager.grey,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: _buildButtonContent(),
            ),
          ),
        ),
      );
    });
  }

  // Build button content based on payment state
  Widget _buildButtonContent() {
    return GetBuilder<PaymentControllerPlus>(builder: (controller) {
      if (controller.isProcessingPayment || controller.isConfirmingPayment) {
        String statusText = 'payment.processing'.tr;

        if (controller.isConfirmingPayment) {
          statusText = 'payment.confirming'.tr;
        } else if (controller.paymentIntentStatus.isNotEmpty) {
          switch (controller.paymentIntentStatus.toLowerCase()) {
            case 'requires_action':
              statusText = 'payment.requiresAction'.tr;
              break;
            case 'requires_capture':
              statusText = 'payment.capturing'.tr;
              break;
            case 'processing':
              statusText = 'payment.processingPayment'.tr;
              break;
          }
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(ColorManager.white),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              statusText,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        );
      }

      return Text(
        '${'payment.payNow'.tr} ${controller.finalAmount.toStringAsFixed(2)} ${controller.currency}',
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      );
    });
  }
}
