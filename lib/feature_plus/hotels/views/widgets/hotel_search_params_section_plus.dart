import 'package:fandooq/feature_plus/home/<USER>/guests_search.dart';
import 'package:fandooq/feature_plus/settings/views/widgets/currency_selection_sheet.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../../../../core/models/hotels/hotels.dart';
import '../../../../feature/home/<USER>';
import '../../../home/<USER>/change_guests_info_screen.dart';
import '../../controllers/hotel_details_controller_plus.dart';
import 'enhanced_date_range_picker.dart';

class HotelSearchParamsSectionPlus extends StatelessWidget {
  const HotelSearchParamsSectionPlus({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HotelDetailsControllerPlus>();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 16,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  ColorManager.primary.withOpacity(0.1),
                  ColorManager.primary.withOpacity(0.05),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.search,
                  color: ColorManager.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'hotelSearchParams.searchParameters'.tr,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColorManager.primary,
                  ),
                ),
                const Spacer(),
                Obx(() => controller.isSearching
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(
                        Icons.tune,
                        color: ColorManager.primary,
                        size: 18,
                      )),
              ],
            ),
          ),

          // Search Parameters
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Dates Row
                _buildDatesRow(controller),

                const SizedBox(height: 16),

                // Guests Row
                _buildGuestsRow(controller),

                const SizedBox(height: 16),

                // Update Button
                _buildUpdateButton(controller),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatesRow(HotelDetailsControllerPlus controller) {
    return Obx(() {
      final checkIn = controller.searchCheckIn;
      final checkOut = controller.searchCheckOut;

      return Row(
        children: [
          // Check-in Date
          Expanded(
            child: _buildDateCard(
              title: 'hotelSearchParams.checkInDate'.tr,
              date: checkIn,
              icon: Icons.login,
              onTap: () => _selectDateRange(controller),
            ),
          ),

          const SizedBox(width: 12),

          // Nights indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: ColorManager.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              controller.numberOfNights == 1
                  ? 'hotelSearchParams.night'
                      .tr
                      .replaceAll('{count}', '${controller.numberOfNights}')
                  : 'hotelSearchParams.nights'
                      .tr
                      .replaceAll('{count}', '${controller.numberOfNights}'),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: ColorManager.primary,
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Check-out Date
          Expanded(
            child: _buildDateCard(
              title: 'hotelSearchParams.checkOutDate'.tr,
              date: checkOut,
              icon: Icons.logout,
              onTap: () => _selectDateRange(controller),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildDateCard({
    required String title,
    required DateTime? date,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              date != null
                  ? DateFormat('dd MMM yyyy', 'ar').format(date)
                  : 'selectDate'.tr,  // استخدم المفتاح للترجمة
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),

          ],
        ),
      ),
    );
  }

  Widget _buildGuestsRow(HotelDetailsControllerPlus controller) {
    return Obx(() {
      return GestureDetector(
        onTap: () => _showGuestSelector(controller),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(Icons.people, color: Colors.grey[600], size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'hotelSearch.guestsAndRooms'.tr,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatGuestsText(controller),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.edit, color: Colors.grey[400], size: 18),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildUpdateButton(HotelDetailsControllerPlus controller) {
    return Obx(() {
      final hasChanges = controller.hasSearchParamsChanged;

      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: double.infinity,
        height: hasChanges ? 48 : 0,
        child: hasChanges
            ? ElevatedButton.icon(
                onPressed: controller.isSearching
                    ? null
                    : () => controller.updateSearchParams(),
                icon: controller.isSearching
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.refresh, size: 18),
                label: Text(
                  controller.isSearching
                      ? 'hotelSearchParams.updating'.tr
                      : 'hotelSearchParams.updateSearch'.tr,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorManager.primary,
                  foregroundColor: Colors.white,
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              )
            : const SizedBox.shrink(),
      );
    });
  }

  String _formatGuestsText(HotelDetailsControllerPlus controller) {
    final adults = controller.searchAdults;
    final children = controller.searchChildren;
    final rooms = controller.searchRooms;

    String text = '$adults ${'hotelSearchParams.adult'.tr}';
    if (adults > 1) {
      text = '$adults ${'hotelSearchParams.adults'.tr}';
    }

    if (children > 0) {
      if (children == 1) {
        text += '، $children ${'hotelSearchParams.child'.tr}';
      } else {
        text += '، $children ${'hotelSearchParams.children'.tr}';
      }
    }

    if (rooms == 1) {
      text += '، $rooms ${'hotelSearchParams.room'.tr}';
    } else {
      text += '، $rooms ${'hotelSearchParams.rooms'.tr}';
    }

    return text;
  }

  Future<void> _selectDateRange(HotelDetailsControllerPlus controller) async {
    await EnhancedDateRangePicker.show(
      context: Get.context!,
      initialCheckIn: controller.searchCheckIn,
      initialCheckOut: controller.searchCheckOut,
      title: 'hotelDetails.selectStayDates'.tr, // استخدم الترجمة هنا
      onDateRangeSelected: (checkIn, checkOut) {
        controller.updateCheckInDate(checkIn);
        controller.updateCheckOutDate(checkOut);
      },
    );
  }

  void _showGuestSelector(HotelDetailsControllerPlus controller) async {
    // Convert PaxRoom to RoomRequestModel for ChangeGuestsInfoScreen
    List<RoomRequestModel> rooms = [];

    if (controller.searchPaxRooms.isNotEmpty) {
      rooms = controller.searchPaxRooms.map((paxRoom) {
        // Create adults list
        List<GuestDataRequest> adults = List.generate(
          paxRoom.adults ?? 2,
          (index) => GuestDataRequest.adult(),
        );

        // Create children list with ages
        List<GuestDataRequest> children = [];
        if (paxRoom.children != null && paxRoom.children! > 0) {
          for (int i = 0; i < paxRoom.children!; i++) {
            int age = 8; // Default age
            if (paxRoom.childrenAges != null &&
                i < paxRoom.childrenAges!.length) {
              age = paxRoom.childrenAges![i];
            }
            children.add(GuestDataRequest.child(age: age));
          }
        }

        return RoomRequestModel(
          uuid: '${DateTime.now().millisecondsSinceEpoch}_$rooms.length',
          rooms: 1,
          adults: adults,
          children: children,
        );
      }).toList();
    } else {
      // Default room with 2 adults
      rooms = [RoomRequestModel.defaultRoom()];
    }

    final result = await showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: rooms.length > 1 ? 0.9 : 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: ChangeGuestsInfoScreen(
            rooms: rooms,
            scrollController: scrollController,
          ),
        ),
      ),
    );

    if (result != null && result is List<RoomRequestModel>) {
      // Convert back to PaxRoom format
      List<PaxRoom> paxRooms = result.map((room) {
        List<int> childrenAges =
            room.children.map((child) => child.age ?? 8).toList();

        return PaxRoom(
          adults: room.adults.length,
          children: room.children.length,
          childrenAges: childrenAges,
        );
      }).toList();

      // Calculate totals
      final adults = paxRooms.fold(0, (sum, room) => sum + (room.adults ?? 0));
      final children =
          paxRooms.fold(0, (sum, room) => sum + (room.children ?? 0));
      final roomsCount = paxRooms.length;

      // Update controller
      controller.updateGuestParams(adults, children, roomsCount, paxRooms);
    }
  }
}
