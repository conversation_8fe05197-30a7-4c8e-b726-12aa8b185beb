import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../../controllers/hotel_details_controller_plus.dart';

class HotelAmenitiesSectionPlus extends StatelessWidget {
  const HotelAmenitiesSectionPlus({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HotelDetailsControllerPlus>();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Title with Huge Icon
          _buildSectionTitle(),

          const Divider(height: 1),

          // Key Features Section (Main highlights)
          _buildKeyFeaturesSection(controller),

          const Divider(height: 1),

          // Detailed Amenities Section
          _buildDetailedAmenitiesSection(controller),

          const Divider(height: 1),

          // Facilities Section
          _buildFacilitiesSection(controller),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildKeyFeaturesSection(HotelDetailsControllerPlus controller) {
    return Obx(() {
      final features = <Map<String, dynamic>>[];

      // Only show main highlights that are commonly important
      if (controller.hasFreeCancellation) {
        features.add({
          'icon': Icons.cancel_outlined,
          'text': 'hotelsPlus.freeCancellation'.tr,
          'color': Colors.green,
        });
      }

      if (controller.hasBreakfast) {
        features.add({
          'icon': Icons.free_breakfast_rounded,
          'text': 'hotelsPlus.freeBreakfast'.tr,
          'color': Colors.orange,
        });
      }

      if (controller.hasWiFi) {
        features.add({
          'icon': Icons.wifi_rounded,
          'text': 'hotelsPlus.freeWiFi'.tr,
          'color': Colors.blue,
        });
      }

      if (controller.hasPool) {
        features.add({
          'icon': Icons.pool_rounded,
          'text': 'hotelsPlus.pool'.tr,
          'color': Colors.cyan,
        });
      }

      if (features.isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Simple Header with Icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.star_rounded,
                    size: 20, // Smaller Icon
                    color: Colors.amber,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'المميزات الرئيسية',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Simple Features Wrap
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: features.map((feature) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: (feature['color'] as Color).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: (feature['color'] as Color).withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: IntrinsicWidth(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          feature['icon'] as IconData,
                          size: 16, // Smaller Icon
                          color: feature['color'] as Color,
                        ),
                        const SizedBox(width: 6),
                        Flexible(
                          child: Text(
                            feature['text'] as String,
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: feature['color'] as Color,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildDetailedAmenitiesSection(HotelDetailsControllerPlus controller) {
    return Obx(() {
      final amenities = controller.displayedAmenities;

      // Filter out basic features that are already shown in key features
      final detailedAmenities = amenities.where((amenity) {
        final name = amenity.toLowerCase();
        return !name.contains('free cancellation') &&
            !name.contains('breakfast') &&
            !name.contains('wifi') &&
            !name.contains('pool') &&
            !name.contains('إلغاء') &&
            !name.contains('إفطار') &&
            !name.contains('واي فاي') &&
            !name.contains('مسبح');
      }).toList();

      if (detailedAmenities.isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Simple Header with Show More Button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.purple.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.hotel_rounded,
                        size: 20, // Smaller Icon
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'hotelsPlus.amenities'.tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildAmenitiesList(detailedAmenities),
            if (controller.amenities.length > 6)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Center(
                  child: TextButton(
                    onPressed: controller.toggleShowAllAmenities,
                    child: Text(
                      controller.showAllAmenities
                          ? 'hotelsPlus.showLess'.tr
                          : 'hotelsPlus.showMore'.tr,
                      style: const TextStyle(
                        color: ColorManager.primary,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildFacilitiesSection(HotelDetailsControllerPlus controller) {
    return Obx(() {
      final facilities = controller.displayedFacilities;

      if (facilities.isEmpty) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Simple Header with Show More Button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.teal.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.business_center_rounded,
                        size: 20, // Smaller Icon
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'المرافق والخدمات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildFacilitiesList(facilities),
            if (controller.facilities.length > 6)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Center(
                  child: TextButton(
                    onPressed: controller.toggleShowAllFacilities,
                    child: Text(
                      controller.showAllFacilities ? 'عرض أقل' : 'عرض المزيد',
                      style: const TextStyle(
                        color: ColorManager.primary,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildAmenitiesList(List<String> amenities) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: amenities.map((amenity) {
        final amenityIcon = _getAmenityIcon(amenity);
        final amenityColor = _getAmenityColor(amenity);

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: amenityColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: amenityColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: IntrinsicWidth(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  amenityIcon,
                  color: amenityColor,
                  size: 16, // Smaller Icon
                ),
                const SizedBox(width: 6),
                Flexible(
                  child: Text(
                    _cleanAmenityText(amenity),
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: amenityColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildFacilitiesList(List<String> facilities) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: facilities.map((facility) {
        final facilityIcon = _getFacilityIcon(facility);
        final facilityColor = _getFacilityColor(facility);

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: facilityColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: facilityColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: IntrinsicWidth(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  facilityIcon,
                  color: facilityColor,
                  size: 16, // Smaller Icon
                ),
                const SizedBox(width: 6),
                Flexible(
                  child: Text(
                    _cleanFacilityText(facility),
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: facilityColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  IconData _getAmenityIcon(String amenity) {
    final lowerAmenity = amenity.toLowerCase();

    if (lowerAmenity.contains('wifi') || lowerAmenity.contains('internet')) {
      return Icons.wifi;
    } else if (lowerAmenity.contains('pool') ||
        lowerAmenity.contains('swimming')) {
      return Icons.pool;
    } else if (lowerAmenity.contains('gym') ||
        lowerAmenity.contains('fitness')) {
      return Icons.fitness_center;
    } else if (lowerAmenity.contains('spa')) {
      return Icons.spa;
    } else if (lowerAmenity.contains('restaurant') ||
        lowerAmenity.contains('dining')) {
      return Icons.restaurant;
    } else if (lowerAmenity.contains('bar') ||
        lowerAmenity.contains('lounge')) {
      return Icons.local_bar;
    } else if (lowerAmenity.contains('parking')) {
      return Icons.local_parking;
    } else if (lowerAmenity.contains('air') ||
        lowerAmenity.contains('conditioning')) {
      return Icons.ac_unit;
    } else if (lowerAmenity.contains('tv') ||
        lowerAmenity.contains('television')) {
      return Icons.tv;
    } else if (lowerAmenity.contains('room service')) {
      return Icons.room_service;
    } else {
      return Icons.check_circle_outline;
    }
  }

  Color _getAmenityColor(String amenity) {
    final lowerAmenity = amenity.toLowerCase();

    if (lowerAmenity.contains('wifi') || lowerAmenity.contains('internet')) {
      return Colors.blue;
    } else if (lowerAmenity.contains('breakfast') ||
        lowerAmenity.contains('food')) {
      return Colors.orange;
    } else if (lowerAmenity.contains('pool') ||
        lowerAmenity.contains('swimming')) {
      return Colors.cyan;
    } else if (lowerAmenity.contains('gym') ||
        lowerAmenity.contains('fitness')) {
      return Colors.red;
    } else if (lowerAmenity.contains('spa') ||
        lowerAmenity.contains('massage')) {
      return Colors.purple;
    } else if (lowerAmenity.contains('parking')) {
      return Colors.grey;
    } else if (lowerAmenity.contains('restaurant') ||
        lowerAmenity.contains('dining')) {
      return Colors.brown;
    } else if (lowerAmenity.contains('bar') || lowerAmenity.contains('drink')) {
      return Colors.amber;
    } else if (lowerAmenity.contains('air conditioning') ||
        lowerAmenity.contains('ac')) {
      return Colors.lightBlue;
    } else if (lowerAmenity.contains('tv') ||
        lowerAmenity.contains('television')) {
      return Colors.indigo;
    } else if (lowerAmenity.contains('safe') ||
        lowerAmenity.contains('security')) {
      return Colors.green;
    } else {
      return ColorManager.primary;
    }
  }

  IconData _getFacilityIcon(String facility) {
    final lowerFacility = facility.toLowerCase();

    if (lowerFacility.contains('wifi') || lowerFacility.contains('internet')) {
      return Icons.wifi;
    } else if (lowerFacility.contains('parking')) {
      return Icons.local_parking;
    } else if (lowerFacility.contains('restaurant')) {
      return Icons.restaurant;
    } else if (lowerFacility.contains('pool')) {
      return Icons.pool;
    } else if (lowerFacility.contains('gym') ||
        lowerFacility.contains('fitness')) {
      return Icons.fitness_center;
    } else if (lowerFacility.contains('spa')) {
      return Icons.spa;
    } else if (lowerFacility.contains('bar') ||
        lowerFacility.contains('lounge')) {
      return Icons.local_bar;
    } else if (lowerFacility.contains('laundry')) {
      return Icons.local_laundry_service;
    } else if (lowerFacility.contains('airport') ||
        lowerFacility.contains('shuttle')) {
      return Icons.airport_shuttle;
    } else if (lowerFacility.contains('front desk') ||
        lowerFacility.contains('reception')) {
      return Icons.desk;
    } else if (lowerFacility.contains('elevator') ||
        lowerFacility.contains('lift')) {
      return Icons.elevator;
    } else if (lowerFacility.contains('safe') ||
        lowerFacility.contains('security')) {
      return Icons.security;
    } else {
      return Icons.check_circle_outline;
    }
  }

  Color _getFacilityColor(String facility) {
    final lowerFacility = facility.toLowerCase();

    if (lowerFacility.contains('wifi') || lowerFacility.contains('internet')) {
      return Colors.blue;
    } else if (lowerFacility.contains('parking')) {
      return Colors.grey;
    } else if (lowerFacility.contains('restaurant') ||
        lowerFacility.contains('dining')) {
      return Colors.brown;
    } else if (lowerFacility.contains('pool') ||
        lowerFacility.contains('swimming')) {
      return Colors.cyan;
    } else if (lowerFacility.contains('gym') ||
        lowerFacility.contains('fitness')) {
      return Colors.red;
    } else if (lowerFacility.contains('spa') ||
        lowerFacility.contains('massage')) {
      return Colors.purple;
    } else if (lowerFacility.contains('bar') ||
        lowerFacility.contains('lounge')) {
      return Colors.amber;
    } else if (lowerFacility.contains('laundry')) {
      return Colors.lightBlue;
    } else if (lowerFacility.contains('airport') ||
        lowerFacility.contains('shuttle')) {
      return Colors.orange;
    } else if (lowerFacility.contains('front desk') ||
        lowerFacility.contains('reception')) {
      return Colors.indigo;
    } else if (lowerFacility.contains('elevator') ||
        lowerFacility.contains('lift')) {
      return Colors.teal;
    } else if (lowerFacility.contains('safe') ||
        lowerFacility.contains('security')) {
      return Colors.green;
    } else {
      return ColorManager.primary;
    }
  }

  String _cleanAmenityText(String amenity) {
    // Clean up amenity text
    return amenity
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll('&nbsp;', ' ')
        .trim();
  }

  String _cleanFacilityText(String facility) {
    // Clean up facility text
    return facility
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll('&nbsp;', ' ')
        .trim();
  }

  Widget _buildSectionTitle() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple[400]!,
            Colors.purple[600]!,
            Colors.purple[800]!,
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.hotel_rounded,
              size: 36, // Huge Icon
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'hotelsPlus.facilitiesAndServices'.tr,
                  style: const TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  'hotelsPlus.facilitiesDescription'.tr,
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.white70,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.star_rounded,
              size: 20,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }
}
