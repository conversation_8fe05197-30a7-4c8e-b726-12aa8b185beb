import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../../../../core/extensions/string.dart';
import '../../controllers/hotel_details_controller_plus.dart';
import 'hotel_map_widget_plus.dart';

class HotelInfoSectionPlus extends StatelessWidget {
  const HotelInfoSectionPlus({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HotelDetailsControllerPlus>();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hotel Header Info (Name, Stars, Price)
          _buildHotelHeader(controller),

          _buildElegantDivider(),

          // Hotel Location and Contact
          _buildHotelLocation(controller),

          _buildElegantDivider(),

          // Hotel Description (Only if not empty and not duplicated)
          _buildHotelDescription(controller),

          _buildElegantDivider(),

          // Quick Overview Section (Basic info only)
          _buildQuickOverview(controller),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildHotelHeader(HotelDetailsControllerPlus controller) {
    return Obx(() => Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.indigo[50]!,
                Colors.indigo[100]!,
                Colors.indigo[200]!,
              ],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hotel Name and Stars

              Text(
                controller.hotelName,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  letterSpacing: 0.5,
                ),
              ),

              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Star Rating
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < controller.starRating
                            ? Icons.star
                            : Icons.star_border,
                        color: Colors.amber,
                        size: 20,
                      );
                    }),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${controller.starRating} ${'hotelDetails.stars'.tr}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Hotel Address (Clickable)
              GestureDetector(
                onTap: () => _openGoogleMaps(controller),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Colors.blue[600],
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        controller.hotelAddress,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue[600],
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.open_in_new,
                      color: Colors.blue[600],
                      size: 14,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildHotelLocation(HotelDetailsControllerPlus controller) {
    return Obx(() {
      final hotel = controller.hotel;
      if (hotel == null) return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Location Header with Huge Icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.location_on_rounded,
                    size: 24, // Huge Icon
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'hotelDetails.locationContact'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Address with Huge Icon
            if (hotel.address.isNotEmpty) ...[
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.place_rounded,
                      size: 20, // Large Icon
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'hotelDetails.address'.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          hotel.address,
                          style: const TextStyle(
                            fontSize: 15,
                            color: Colors.black87,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // City with Huge Icon
            if (hotel.city.isNotEmpty) ...[
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.location_city_rounded,
                      size: 20, // Large Icon
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'hotelDetails.city'.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          hotel.city,
                          style: const TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],

            // Hotel Map Widget
            HotelMapWidgetPlus(
              hotelName: controller.hotelName,
              address: controller.hotelAddress,
              latitude: controller.latitude,
              longitude: controller.longitude,
            ),
          ],
        ),
      );
    });
  }

  Widget _buildHotelDescription(HotelDetailsControllerPlus controller) {
    return Obx(() {
      final description = controller.description;

      // Only show description if it's not empty and not too short (avoid duplicated basic info)
      if (description.isEmpty || description.length < 50) {
        return const SizedBox.shrink();
      }

      // Clean and check if description contains meaningful content
      final cleanedDescription = _cleanDescription(description);
      if (cleanedDescription.length < 50) {
        return const SizedBox.shrink();
      }

      return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Description Header with Huge Icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.description_rounded,
                    size: 24, // Huge Icon
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'hotelDetails.aboutHotel'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Description Content
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Text(
                cleanedDescription,
                style: TextStyle(
                  fontSize: 15,
                  color: Colors.grey[700],
                  height: 1.6,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildQuickOverview(HotelDetailsControllerPlus controller) {
    return Obx(() {
      final hotel = controller.hotel;
      if (hotel == null) return const SizedBox.shrink();

      return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Overview Header with Huge Icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.indigo.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.visibility_rounded,
                    size: 24, // Huge Icon
                    color: Colors.indigo,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'hotelDetails.quickOverview'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Quick Stats Grid
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  // Star Rating
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.amber.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.star_rounded,
                          size: 20, // Large Icon
                          color: Colors.amber,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '${hotel.starRating} ${'hotelDetails.rating'.tr}',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Room Count (if available)
                  if (controller.availableRooms.isNotEmpty) ...[
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.bed_rounded,
                            size: 20, // Large Icon
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          '${controller.availableRooms.length} ${'hotelDetails.roomTypesAvailable'.tr}',
                          style: const TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                  ],

                  // Price Range (if available)
                  if (controller.availableRooms.isNotEmpty) ...[
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.attach_money_rounded,
                            size: 20, // Large Icon
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          [
                            'hotelDetails.pricesStartFrom'.tr,
                            _getLowestPriceFormatted(controller)
                          ].join(" "),
                          style: const TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Note about features
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    size: 20, // Large Icon
                    color: Colors.blue[700],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'hotelDetails.featuresNote'.tr,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  String _getLowestPrice(HotelDetailsControllerPlus controller) {
    if (controller.availableRooms.isEmpty) return '0';

    num lowestPrice = double.infinity;
    for (final room in controller.availableRooms) {
      if (room.totalFare < lowestPrice) {
        lowestPrice = room.totalFare;
      }
    }

    return lowestPrice == double.infinity
        ? '0'
        : lowestPrice.toStringAsFixed(0);
  }

  String _getLowestPriceFormatted(HotelDetailsControllerPlus controller) {
    if (controller.availableRooms.isEmpty) return '0';

    num lowestPrice = double.infinity;
    for (final room in controller.availableRooms) {
      if (room.totalFare < lowestPrice) {
        lowestPrice = room.totalFare;
      }
    }

    if (lowestPrice == double.infinity) {
      return '0';
    }

    // Use currency from controller (which gets it from PricingPlus)
    final currency = controller.currency;

    return lowestPrice.toString().toMoneyWithSymbol(currencyCode: currency);
  }

  String _cleanDescription(String description) {
    // Remove HTML tags and clean up the description
    String cleaned = description
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .trim();

    // Limit to reasonable length
    if (cleaned.length > 500) {
      cleaned = '${cleaned.substring(0, 500)}...';
    }

    return cleaned;
  }

  Widget _buildElegantDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.transparent,
                    Colors.grey[200]!,
                    Colors.grey[300]!,
                    Colors.grey[200]!,
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Open Google Maps with hotel location
  Future<void> _openGoogleMaps(HotelDetailsControllerPlus controller) async {
    final latitude = controller.latitude;
    final longitude = controller.longitude;

    if (latitude == null || longitude == null) {
      print('❌ Cannot open Google Maps: Missing coordinates');
      return;
    }

    print('🗺️ Opening Google Maps for: $latitude, $longitude');

    // Try multiple URL formats for better compatibility
    final urls = [
      // Google Maps app URL (preferred)
      'comgooglemaps://?q=$latitude,$longitude&center=$latitude,$longitude&zoom=15',
      // Google Maps web URL (fallback)
      'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude',
      // Apple Maps URL (iOS fallback)
      'http://maps.apple.com/?q=$latitude,$longitude',
    ];

    bool launched = false;

    for (String url in urls) {
      try {
        final uri = Uri.parse(url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          print('✅ Successfully opened map with URL: $url');
          launched = true;
          break;
        }
      } catch (e) {
        print('⚠️ Failed to launch URL $url: $e');
        continue;
      }
    }

    if (!launched) {
      print('❌ Failed to open any map application');
    }
  }
}
