import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/extensions/string.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../../controllers/hotel_details_controller_plus.dart';

class HotelBookingBottomBarPlus extends StatelessWidget {
  const HotelBookingBottomBarPlus({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HotelDetailsControllerPlus>();

    return Obx(() => Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: Row(
              children: [
                // Price Information
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (controller.selectedRoom != null) ...[
                        Text(
                          controller.selectedRoomPrice
                              .toString()
                              .toMoneyWithSymbol(
                                  currencyCode: controller.getLocalCurrency),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: ColorManager.primary,
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _hasSelectedRoomSupplements(controller)
                                  ? 'Taxes included'
                                  : 'السعر النهائي',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            if (_hasSelectedRoomSupplements(controller)) ...[
                              Text(
                                _getSupplementsText(controller),
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.orange[600],
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ] else ...[
                              Text(
                                '(شامل جميع الرسوم)',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey[600],
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                        if (controller.selectedRoom!.isRefundable)
                          Row(
                            children: [
                              const Icon(
                                Icons.check_circle,
                                size: 14,
                                color: Colors.green,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'hotelFeatures.freeCancellation'.tr,
                                style: const TextStyle(
                                  fontSize: 11,
                                  color: Colors.green,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                      ] else ...[
                        Text(
                          '${'hotelPricing.from'.tr} ${controller.minPrice.toString().toMoneyWithSymbol(currencyCode: controller.getLocalCurrency)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: ColorManager.primary,
                          ),
                        ),
                        Text(
                          'hotelPricing.perStay'.tr,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Booking Button
                Expanded(
                  child: ElevatedButton(
                    onPressed: controller.selectedRoom != null
                        ? controller.bookHotel
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorManager.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.book_online,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          controller.selectedRoom != null
                              ? 'hotelsPlus.bookNow'.tr
                              : 'hotelsPlus.selectRoom'.tr,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  /// Check if selected room has supplements
  bool _hasSelectedRoomSupplements(HotelDetailsControllerPlus controller) {
    final selectedRoom = controller.selectedRoom;
    if (selectedRoom == null) return false;

    return selectedRoom.supplements.isNotEmpty &&
        selectedRoom.supplements.any((group) => group.isNotEmpty);
  }

  /// Get supplements text for bottom bar
  String _getSupplementsText(HotelDetailsControllerPlus controller) {
    final selectedRoom = controller.selectedRoom;
    if (selectedRoom == null) return '';

    // Get first supplement
    for (final group in selectedRoom.supplements) {
      if (group.isNotEmpty) {
        final supplement = group.first;
        return '${'hotelsPlus.additionalCharges'.tr} ${supplement.price.toStringAsFixed(0)} ${supplement.currency}';
      }
    }

    return 'hotelsPlus.additionalChargesMayApply'.tr;
  }
}
