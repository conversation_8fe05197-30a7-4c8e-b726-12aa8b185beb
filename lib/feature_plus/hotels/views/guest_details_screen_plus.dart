import 'package:fandooq/feature_plus/hotels/controllers/payment_controller_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fandooq/core/theme/color_manager.dart';
import '../../../core/components-state/pages/country/country_code_picker.dart';
import '../../../core/extensions/string.dart';
import '../controllers/guest_details_controller_plus.dart';
import '../models/hotel_models.dart';
import '../models/pre_booking_models.dart';


class GuestDetailsScreenPlus extends StatefulWidget {
  const GuestDetailsScreenPlus({super.key});
  @override
  State<GuestDetailsScreenPlus> createState() => _GuestDetailsScreenPlusState();
}

class _GuestDetailsScreenPlusState extends State<GuestDetailsScreenPlus> {

  final controller = Get.put(GuestDetailsControllerPlus());

  @override
  void dispose() {
    Get.delete<GuestDetailsControllerPlus>();
    Get.delete<PaymentControllerPlus>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'hotels.guestDetails'.tr,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: ColorManager.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Auto-fill button for testing
          IconButton(
            onPressed: controller.autoFillGuestData,
            icon: const Icon(Icons.auto_fix_high),
            tooltip: 'autoFillTooltip'.tr,
          ),
        ],
      ),
      body: Column(
        children: [
          // Booking summary header
          _buildBookingSummaryHeader(controller),

          // Main content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Guest forms
                  _buildGuestForms(controller),

                  const SizedBox(height: 24),

                  // Special requests
                  _buildSpecialRequests(controller),

                  const SizedBox(height: 24),

                  // Important notes
                  _buildImportantNotes(),
                ],
              ),
            ),
          ),

          // Bottom action bar
          _buildBottomActionBar(controller),
        ],
      ),
    );
  }

  Widget _buildBookingSummaryHeader(GuestDetailsControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.hotel,
                color: ColorManager.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  controller.hotelName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.bed,
                color: Colors.grey[600],
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  controller.roomType,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ),
              Text(
                controller.totalPrice
                    .toString().toMoneyWithSymbol(currencyCode: controller.currency),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ColorManager.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.people,
                color: Colors.grey[600],
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                '${controller.adults} ${'guest.adult'.trArgs([controller.adults.toString()])}'
                    '${controller.children > 0 ? '، ${controller.children} ${'guest.child'.trArgs([controller.children.toString()])}' : ''}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGuestForms(GuestDetailsControllerPlus controller) {
    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'guestDetailsTitle'.tr,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...List.generate(controller.guests.length, (index) {
              final guest = controller.guests[index];
              return _buildGuestForm(controller, index, guest);
            }),
          ],
        ));
  }

  Widget _buildGuestForm(GuestDetailsControllerPlus controller, int index,
      GuestDetailsPlus guest) {
    final controllers = controller.guestControllers[index];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: guest.isMainGuest ? ColorManager.primary : Colors.grey[300]!,
          width: guest.isMainGuest ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Guest header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: guest.isMainGuest ? ColorManager.primary : Colors.grey[400],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  guest.isMainGuest
                      ? 'guest.mainGuest'.tr
                      : '${guest.guestType == 'adult' ? 'guest.adult'.tr : 'guest.child'.tr} ${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Title dropdown
          _buildDropdownField(
            controller: controllers['title']!,
            label: 'guest.title'.tr,
            hint: 'guest.selectTitle'.tr,
            items: controller.titleList,
            onChanged: (value) => controller.updateGuest(index, title: value),
          ),

          const SizedBox(height: 16),

          // Name fields
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  controller: controllers['firstName']!,
                  label: 'hotels.firstName'.tr,
                  hint: 'hotels.enterFirstName'.tr,
                  onChanged: (value) =>
                      controller.updateGuest(index, firstName: value),
                  validator: (value) =>
                      controller.validateField(value, 'hotels.firstName'.tr),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildTextField(
                  controller: controllers['lastName']!,
                  label: 'hotels.lastName'.tr,
                  hint: 'hotels.enterLastName'.tr,
                  onChanged: (value) =>
                      controller.updateGuest(index, lastName: value),
                  validator: (value) =>
                      controller.validateField(value, 'hotels.lastName'.tr),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Nationality only
          _buildDropdownField(
            controller: controllers['nationality']!,
            label: 'hotels.nationality'.tr,
            hint: 'hotels.selectNationality'.tr,
            items: controller.nationalityList,
            onChanged: (value) =>
                controller.updateGuest(index, nationality: value),
          ),

          // Contact info (only for main guest)
          if (guest.isMainGuest) ...[
            const SizedBox(height: 16),
            _buildTextField(
              controller: controllers['email']!,
              label: 'hotels.email'.tr,
              hint: 'hotels.enterEmail'.tr,
              keyboardType: TextInputType.emailAddress,
              onChanged: (value) => controller.updateGuest(index, email: value),
              validator: (value) =>
                  controller.validateField(value, 'hotels.email'.tr),
            ),
            const SizedBox(height: 16),
            _buildPhoneField(
              controller: controllers['phone']!,
              label: 'hotels.phoneNumber'.tr,
              onChanged: (value) => controller.updateGuest(index, phone: value),
              validator: (value) =>
                  controller.validateField(value, 'hotels.phoneNumber'.tr),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    Function(String)? onChanged,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          onChanged: onChanged,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[400]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  const BorderSide(color: ColorManager.primary, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required List<String> items,
    Function(String?)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: controller.text.isEmpty ? null : controller.text,
          hint: Text(hint, style: TextStyle(color: Colors.grey[400])),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: (String? value) {
            if (value != null) {
              controller.text = value;
              onChanged?.call(value);
            }
          },
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  const BorderSide(color: ColorManager.primary, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneField({
    required TextEditingController controller,
    required String label,
    Function(String)? onChanged,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              // Country code picker
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: CountryCodePicker(
                  options: CountryCodeOptions(
                    favoriteElements: [
                      CountryCode.fromCountryCode("SA"),
                      CountryCode.fromCountryCode("EG"),
                      CountryCode.fromCountryCode("AE"),
                    ],
                  ),
                  initialSelection: "SA",
                  onChanged: (CountryCode countryCode) {
                    // Update the phone number with country code
                    final currentPhone = controller.text;
                    if (currentPhone.isNotEmpty) {
                      final fullPhone = countryCode.dialCode! + currentPhone;
                      onChanged?.call(fullPhone);
                    }
                  },
                  showFlag: true,
                  showCountryOnly: false,
                  showOnlyCountryWhenClosed: false,
                  alignLeft: false,
                  textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  boxDecoration: const BoxDecoration(),
                  flagWidth: 24,
                ),
              ),

              // Divider
              Container(
                height: 30,
                width: 1,
                color: Colors.grey[300],
              ),

              // Phone number input
              Expanded(
                child: TextFormField(
                  controller: controller,
                  keyboardType: TextInputType.phone,
                  onChanged: onChanged,
                  validator: validator,
                  decoration: const InputDecoration(
                    hintText: '50 123 4567',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSpecialRequests(GuestDetailsControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.note_add,
                color: ColorManager.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'guests.specialRequests'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextFormField(
            maxLines: 4,
            onChanged: controller.updateSpecialRequests,
            decoration: InputDecoration(
              hintText: 'guests.addSpecialRequests'.tr,
              hintStyle: TextStyle(color: Colors.grey[400]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: ColorManager.primary, width: 2),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImportantNotes() {
    final notes = [
      'guest.importantNotes.note1'.tr,
      'guest.importantNotes.note2'.tr,
      'guest.importantNotes.note3'.tr,
      'guest.importantNotes.note4'.tr,
      'guest.importantNotes.note5'.tr,
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
           Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.orange,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'guest.importantNotes.title'.tr,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...notes.map((note) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 4,
                      height: 4,
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        note,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.orange,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar(GuestDetailsControllerPlus controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Obx(() => Row(
              children: [
                // Back Button
                Expanded(
                  child: OutlinedButton(
                    onPressed: controller.goBackToPreBooking,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: Colors.grey[400]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'back'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Continue Button
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: controller.isLoading
                        ? null
                        : controller.proceedToPayment,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorManager.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: controller.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'guests.continueToPayment'.tr,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            )),
      ),
    );
  }


}
