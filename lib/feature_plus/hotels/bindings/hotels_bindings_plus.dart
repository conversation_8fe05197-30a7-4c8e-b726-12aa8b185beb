import 'package:get/get.dart';
import '../controllers/hotels_controller_plus.dart';
import '../controllers/hotel_details_controller_plus.dart';
import '../controllers/pre_booking_controller_plus.dart';
import '../controllers/guest_details_controller_plus.dart';
import '../controllers/payment_controller_plus.dart';

/// Binding for Hotels List Screen
class HotelsBindingPlus extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<HotelsControllerPlus>(() => HotelsControllerPlus());
  }
}

/// Binding for Hotel Details Screen
class HotelDetailsBindingPlus extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<HotelDetailsControllerPlus>(() => HotelDetailsControllerPlus());
  }
}

/// Binding for Pre-booking Screen
class PreBookingBindingPlus extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<PreBookingControllerPlus>(() => PreBookingControllerPlus());
  }
}

/// Binding for Guest Details Screen
class GuestDetailsBindingPlus extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<GuestDetailsControllerPlus>(() => GuestDetailsControllerPlus());
  }
}

/// Binding for Payment Screen
class PaymentBindingPlus extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<PaymentControllerPlus>(() => PaymentControllerPlus());
  }
}

/// Binding for Booking Confirmation Screen
class BookingConfirmationBindingPlus extends Bindings {
  @override
  void dependencies() {
    // No specific controller needed for confirmation screen
  }
}
