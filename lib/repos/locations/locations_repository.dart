import 'package:dartz/dartz.dart' as dartz;
import 'package:fandooq/core/models/locations/location_search_models.dart';
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/core/utils/base_classes/base_repository.dart';
import 'package:fandooq/core/utils/check_internet.dart';
import 'package:get/get.dart';

/// Abstract repository for locations API
abstract class LocationsRepository {
  static LocationsRepository get to => Get.find();

  Future<dartz.Either<BasicFailure, LocationSearchResponse>> searchPlaces(
      LocationSearchParams params);
}

/// Remote data source for locations API
abstract class LocationsRemoteDataSource {
  Future<NetworkResponse<LocationSearchResponse>> searchPlaces(
      LocationSearchParams params);
}

/// Implementation of locations remote data source
class LocationsRemoteDataSourceImpl implements LocationsRemoteDataSource {
  final NetworkHandler networkHandler = NetworkHandler();

  @override
  Future<NetworkResponse<LocationSearchResponse>> searchPlaces(
      LocationSearchParams params) {
    final queryParams = params.toQueryParams();
    final queryString = Uri(queryParameters: queryParams).query;

    return networkHandler.get(
      LocationSearchResponse(),
      "${AppApis.autocompletePlaces}?$queryString",
      dioType: DioType.api,
    );
  }
}

/// Local data source for locations (for caching if needed)
abstract class LocationsLocalDataSource {
  // Future methods for local caching can be added here
}

/// Implementation of locations local data source
class LocationsLocalDataSourceImpl implements LocationsLocalDataSource {
  // Local caching implementation can be added here
}

/// Implementation of locations repository
class LocationsRepositoryImpl
    extends BaseRepository<LocationsRemoteDataSource, LocationsLocalDataSource>
    implements LocationsRepository {
  @override
  Future<dartz.Either<BasicFailure, LocationSearchResponse>> searchPlaces(
      LocationSearchParams params) async {
    if (await checkConnectionInternet()) {
      try {
        final NetworkResponse<LocationSearchResponse> response =
            await remoteDataSource.searchPlaces(params);

        if (response.isRequestSuccess) {
          return dartz.Right(response.body!);
        } else {
          return dartz.Left(BasicFailure.fromNetworkResponse(response));
        }
      } catch (e) {
        return dartz.Left(BasicFailure(errorMassage: e.toString()));
      }
    } else {
      return dartz.Left(BasicFailure.noInternet());
    }
  }
}

/// Binding for locations repository
class LocationsRepoBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<LocationsRemoteDataSource>(LocationsRemoteDataSourceImpl());
    Get.put<LocationsLocalDataSource>(LocationsLocalDataSourceImpl());
    Get.put<LocationsRepository>(LocationsRepositoryImpl());
  }
}
