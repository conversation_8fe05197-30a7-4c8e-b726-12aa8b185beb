// Export all new repositories for easy access
export 'locations/locations_repository.dart';
export 'booking/booking_repository.dart';

import 'package:fandooq/repos/hotels/data/hotels.dart';
import 'package:get/get.dart';
import 'locations/locations_repository.dart';
import 'booking/booking_repository.dart';

/// Centralized access to all new repositories
class NewRepos {
  static LocationsRepository get locations => Get.find<LocationsRepository>();
  static BookingRepository get booking => Get.find<BookingRepository>();
  static HotelsRepo get hotels => Get.find<HotelsRepo>();
}

/// Binding for all new repositories
class NewReposBinding extends Bindings {
  @override
  void dependencies() {
    LocationsRepoBinding().dependencies();
    BookingRepoBinding().dependencies();
  }
}
