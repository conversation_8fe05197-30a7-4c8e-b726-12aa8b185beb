import 'package:dartz/dartz.dart' as dartz;
import 'package:fandooq/core/models/booking/booking_models.dart';
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/core/utils/base_classes/base_repository.dart';
import 'package:fandooq/core/utils/check_internet.dart';
import 'package:get/get.dart';

/// Abstract repository for booking APIs
abstract class BookingRepository {
  static BookingRepository get to => Get.find();

  Future<dartz.Either<BasicFailure, PreBookResponse>> preBookHotel(
      PreBookRequest request);

  Future<dartz.Either<BasicFailure, CompleteBookingResponse>> completeBooking(
      CompleteBookingRequest request);

  Future<dartz.Either<BasicFailure, MyBookingsResponse>> getMyBookings(
      MyBookingsRequest request);

  Future<dartz.Either<BasicFailure, CompleteBookingResponse>> getBookingDetails(
      String bookingId);

  // Future<dartz.Either<BasicFailure, Map<String, dynamic>>> cancelBooking(
  //   CancelBookingRequest request
  // );
}

/// Remote data source for booking APIs
abstract class BookingRemoteDataSource {
  Future<NetworkResponse<PreBookResponse>> preBookHotel(PreBookRequest request);

  Future<NetworkResponse<CompleteBookingResponse>> completeBooking(
      CompleteBookingRequest request);

  Future<NetworkResponse<MyBookingsResponse>> getMyBookings(
      MyBookingsRequest request);

  Future<NetworkResponse<CompleteBookingResponse>> getBookingDetails(
      String bookingId);

  // Future<NetworkResponse<Map<String, dynamic>>> cancelBooking(
  //   CancelBookingRequest request
  // );
}

/// Implementation of booking remote data source
class BookingRemoteDataSourceImpl implements BookingRemoteDataSource {
  final NetworkHandler networkHandler = NetworkHandler();

  @override
  Future<NetworkResponse<PreBookResponse>> preBookHotel(
      PreBookRequest request) {
    return networkHandler.post(
      PreBookResponse(),
      AppApis.preBookHotel,
      body: request.toJson(),
      dioType: DioType.api,
    );
  }

  @override
  Future<NetworkResponse<CompleteBookingResponse>> completeBooking(
      CompleteBookingRequest request) {
    return networkHandler.post(
      CompleteBookingResponse(),
      AppApis.bookHotel,
      body: request.toJson(),
      dioType: DioType.api,
    );
  }

  @override
  Future<NetworkResponse<MyBookingsResponse>> getMyBookings(
      MyBookingsRequest request) {
    final queryParams = request.toQueryParams();
    final queryString = Uri(queryParameters: queryParams).query;

    return networkHandler.get(
      MyBookingsResponse(),
      "${AppApis.myBookings}?$queryString",
      dioType: DioType.api,
    );
  }

  @override
  Future<NetworkResponse<CompleteBookingResponse>> getBookingDetails(
      String bookingId) {
    return networkHandler.get(
      CompleteBookingResponse(),
      "${AppApis.bookingDetails}/$bookingId",
      dioType: DioType.api,
    );
  }

  // @override
  // Future<NetworkResponse<Map<String, dynamic>>> cancelBooking(
  //   CancelBookingRequest request
  // ) {
  //   return networkHandler.post(
  //     {},
  //     'ok9 i8j7u6y5tre'
  //     AppApis.cancelBooking,
  //     body: request.toJson(),
  //     dioType: DioType.api,
  //   ).then((response) => NetworkResponse<Map<String, dynamic>>(
  //     isRequestSuccess: response.isRequestSuccess,
  //     body: response.body is Map<String, dynamic>
  //         ? response.body as Map<String, dynamic>
  //         : {'success': response.isRequestSuccess},
  //     failure: response.failure,
  //     statusCode: response.statusCode,
  //   ));
  // }
  //
}

/// Local data source for booking (for caching if needed)
abstract class BookingLocalDataSource {
  // Future methods for local caching can be added here
}

/// Implementation of booking local data source
class BookingLocalDataSourceImpl implements BookingLocalDataSource {
  // Local caching implementation can be added here
}

/// Implementation of booking repository
class BookingRepositoryImpl
    extends BaseRepository<BookingRemoteDataSource, BookingLocalDataSource>
    implements BookingRepository {
  @override
  Future<dartz.Either<BasicFailure, PreBookResponse>> preBookHotel(
      PreBookRequest request) async {
    if (await checkConnectionInternet()) {
      try {
        final NetworkResponse<PreBookResponse> response =
            await remoteDataSource.preBookHotel(request);

        if (response.isRequestSuccess) {
          return dartz.Right(response.body!);
        } else {
          return dartz.Left(BasicFailure.fromNetworkResponse(response));
        }
      } catch (e) {
        return dartz.Left(BasicFailure(errorMassage: e.toString()));
      }
    } else {
      return dartz.Left(BasicFailure.noInternet());
    }
  }

  @override
  Future<dartz.Either<BasicFailure, CompleteBookingResponse>> completeBooking(
      CompleteBookingRequest request) async {
    if (await checkConnectionInternet()) {
      try {
        final NetworkResponse<CompleteBookingResponse> response =
            await remoteDataSource.completeBooking(request);

        if (response.isRequestSuccess) {
          return dartz.Right(response.body!);
        } else {
          return dartz.Left(BasicFailure.fromNetworkResponse(response));
        }
      } catch (e) {
        return dartz.Left(BasicFailure(errorMassage: e.toString()));
      }
    } else {
      return dartz.Left(BasicFailure.noInternet());
    }
  }

  @override
  Future<dartz.Either<BasicFailure, MyBookingsResponse>> getMyBookings(
      MyBookingsRequest request) async {
    if (await checkConnectionInternet()) {
      try {
        final NetworkResponse<MyBookingsResponse> response =
            await remoteDataSource.getMyBookings(request);

        if (response.isRequestSuccess) {
          return dartz.Right(response.body!);
        } else {
          return dartz.Left(BasicFailure.fromNetworkResponse(response));
        }
      } catch (e) {
        return dartz.Left(BasicFailure(errorMassage: e.toString()));
      }
    } else {
      return dartz.Left(BasicFailure.noInternet());
    }
  }

  @override
  Future<dartz.Either<BasicFailure, CompleteBookingResponse>> getBookingDetails(
      String bookingId) async {
    if (await checkConnectionInternet()) {
      try {
        final NetworkResponse<CompleteBookingResponse> response =
            await remoteDataSource.getBookingDetails(bookingId);

        if (response.isRequestSuccess) {
          return dartz.Right(response.body!);
        } else {
          return dartz.Left(BasicFailure.fromNetworkResponse(response));
        }
      } catch (e) {
        return dartz.Left(BasicFailure(errorMassage: e.toString()));
      }
    } else {
      return dartz.Left(BasicFailure.noInternet());
    }
  }

  // @override
  // Future<dartz.Either<BasicFailure, Map<String, dynamic>>> cancelBooking(
  //   CancelBookingRequest request
  // ) async {
  //   if (await checkConnectionInternet()) {
  //     try {
  //       final NetworkResponse<Map<String, dynamic>> response =
  //           await remoteDataSource.cancelBooking(request);
  //
  //       if (response.isRequestSuccess) {
  //         return dartz.Right(response.body!);
  //       } else {
  //         return dartz.Left(BasicFailure.fromNetworkResponse(response));
  //       }
  //     } catch (e) {
  //       return dartz.Left(BasicFailure(errorMassage: e.toString()));
  //     }
  //   } else {
  //     return dartz.Left(BasicFailure.noInternet());
  //   }
  // }
}

/// Binding for booking repository
class BookingRepoBinding extends Bindings {
  @override
  void dependencies() {
    Get.put<BookingRemoteDataSource>(BookingRemoteDataSourceImpl());
    Get.put<BookingLocalDataSource>(BookingLocalDataSourceImpl());
    Get.put<BookingRepository>(BookingRepositoryImpl());
  }
}
