
import 'dart:convert';
import 'dart:developer';
import 'package:fandooq/core/controllers/user-app/user_controller.dart';
import 'package:fandooq/core/data_base/hive_helpers/tables.dart';
import 'package:fandooq/core/freezed/profile/update_profile_request.dart';
import 'package:fandooq/core/freezed/profile/update_profile_response.dart';
import 'package:fandooq/core/freezed/profile/update_profile_result.dart';
import 'package:fandooq/core/models/app/settings_app.dart';
import 'package:fandooq/core/models/availability/request/hotel_request.dart';
import 'package:fandooq/core/models/availability/response/availability_response.dart';
import 'package:fandooq/core/models/availability/response/hotels-list-content.dart';
import 'package:fandooq/core/models/hotels/src/hotel_data.dart';
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/core/utils/base_classes/base_repository.dart';
import 'package:fandooq/core/utils/check_internet.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:fandooq/main.dart';
import 'package:fandooq/repos/repos.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dartz/dartz.dart' as dartz;
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../core/cache/preference_manager.dart';
import '../../core/freezed/failure.dart';
import '../../feature_plus/auth/models/user_model.dart';


part 'data/local/local_data_source.dart';
part 'data/local/local_data_source_imp.dart';

part 'data/remote/remote_data_source.dart';

part 'repo/user_repo.dart';

part 'binding.dart';


