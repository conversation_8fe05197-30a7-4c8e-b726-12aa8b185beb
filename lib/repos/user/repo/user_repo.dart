part of '../user.dart';

abstract class UserRepo{
  static UserRepo get to => Get.find();
  static _LocalDataSource get local => Get.find();
  static _RemoteDataSource get network => Get.find();
  Future<void> addFavorite(HotelData hotelContent);
  Future<void> removeFavorite(String code);
  Future<HotelsListResponse> getFavorite();
  bool favExist(String code);
  Future<UpdateProfileResult> updateProfile(UpdateProfileRequest updateProfileRequest);
  Future<NetworkResponse<ObjectResponse>> deleteAccount();
}


class UserRepoImp extends BaseRepository<_RemoteDataSource, _LocalDataSource> implements UserRepo {

  NetworkHandler networkHandler = NetworkHandler();

  @override
  Future<void> addFavorite(HotelData hotelContent) async {
    localDataSource.addFavorite(hotelContent: hotelContent);
    bool added = await remoteDataSource.addFavorite(hotelContent: hotelContent);
    return;
  }

  @override
  Future<HotelsListResponse> getFavorite() async {

    // if(PreferenceManager.instance.isFavoriteHasRequestedOneTime()){
    //   var fav = localDataSource.getFavorites();
    //   return HotelsListResponse(
    //     hotels: fav,
    //     from: 1,
    //     to: fav.length,
    //     total: fav.length
    //   );
    // }

    var favorite = await remoteDataSource.getFavorites();

    if(favorite.isRequestSuccess){
      PreferenceManager.instance.saveFavoriteState(true);
      localDataSource.addAllFavorite(hotels: favorite.body!.hotels ?? []);
    }

    return favorite.body!;
  }

  @override
  Future<void> removeFavorite(String code) async {
    localDataSource.removeFavorite(hotelCode: code);
    bool removed = await remoteDataSource.removeFavorite(hotelCode: code);
    return;
  }

  @override
  bool favExist(String code) {
    return localDataSource.favExist(code);
  }



  @override
  Future<NetworkResponse<ObjectResponse>> deleteAccount() {
    // TODO: implement deleteAccount
    return remoteDataSource.deleteAccount();
  }

  @override
  Future<UpdateProfileResult> updateProfile(UpdateProfileRequest updateProfileRequest) async {
    final NetworkResponse<UpdateProfileResponse> response = await remoteDataSource.updateProfile(updateProfileRequest);
    if (response.isRequestSuccess) {
      return UpdateProfileResult.success(response.body!);
    } else {
      return UpdateProfileResult.failure(response.failure ?? Failure.unexpected(""));
    }
  }

}
