part of '../../user.dart';

abstract class _RemoteDataSource {
  Future<bool> addFavorite({required HotelData hotelContent});
  Future<bool> removeFavorite({required String hotelCode});
  Future<NetworkResponse<HotelsListResponse>> getFavorites();
  Future<NetworkResponse<ObjectResponse>> createSetupIntentOnBackend(
      UserModel user);
  Future<NetworkResponse<ObjectResponse>> chargeCardOffSession(UserModel user);

  Future<NetworkResponse<ObjectResponse>> getPaymentMethod(
      {required String paymentMethodId});

  Future<NetworkResponse<ObjectResponse>> attachPaymentMethod(
      {required String paymentMethodId, required String customerId});
  Future<NetworkResponse<ObjectResponse>> deleteAccount();
  Future<NetworkResponse<UpdateProfileResponse>> updateProfile(
      UpdateProfileRequest updateProfileRequest);
}

class _RemoteDataSourceImp implements _RemoteDataSource {
  NetworkHandler networkHandler = NetworkHandler();

  @override
  Future<NetworkResponse<HotelsListResponse>> getFavorites() {
    return networkHandler.get(HotelsListResponse(), AppApis.favorites,
        dioType: DioType.api);
  }



  @override
  Future<NetworkResponse<ObjectResponse>> createSetupIntentOnBackend(
      UserModel user) {
    return networkHandler
        .post(ObjectResponse(), AppApis.create_setup_intent,
            body: json.encode({
              'phone': user.phone,
              'countryCode': user.dialCode,
              'payment_method_types': ["card"]
            }),
            dioType: DioType.api)
        .then((value) => NetworkResponse(
            isRequestSuccess: value.statusCode == 200,
            body: value.body,
            statusCode: value.statusCode));
  }

  @override
  Future<NetworkResponse<ObjectResponse>> chargeCardOffSession(UserModel user) {
    return networkHandler
        .post(
            ObjectResponse(),
            body: json.encode({
              'phone': user.phone,
              'countryCode': user.dialCode,
            }),
            AppApis.charge_card_off_session,
            dioType: DioType.api)
        .then((value) => NetworkResponse(
            isRequestSuccess: value.statusCode == 200,
            body: value.body,
            statusCode: value.statusCode));
  }



  @override
  Future<NetworkResponse<ObjectResponse>> getPaymentMethod(
      {required String paymentMethodId}) {
    return networkHandler
        .get(ObjectResponse(), "${AppApis.get_payment_method}/$paymentMethodId",
            dioType: DioType.api)
        .then((value) => NetworkResponse(
            isRequestSuccess: value.statusCode == 200,
            body: value.body,
            statusCode: value.statusCode));
  }

  @override
  Future<NetworkResponse<ObjectResponse>> attachPaymentMethod(
      {required String paymentMethodId, required String customerId}) {
    return networkHandler
        .post(
            ObjectResponse(),
            body: {
              "customerId": customerId,
              "paymentMethodId": paymentMethodId,
            },
            AppApis.attach_payment_method,
            dioType: DioType.api)
        .then((value) => NetworkResponse(
            isRequestSuccess: value.statusCode == 200,
            body: value.body,
            statusCode: value.statusCode));
  }

  @override
  Future<NetworkResponse<ObjectResponse>> deleteAccount() {
    return networkHandler
        .delete(ObjectResponse(), AppApis.requestToDeleteAccount,
            dioType: DioType.api)
        .then((value) => NetworkResponse(
            isRequestSuccess: value.statusCode == 200,
            body: value.body,
            statusCode: value.statusCode));
  }

  @override
  Future<NetworkResponse<UpdateProfileResponse>> updateProfile(
      UpdateProfileRequest updateProfileRequest) {
    return networkHandler
        .post(
            UpdateProfileResponse(),
            body: updateProfileRequest.toJson(),
            AppApis.profileUpdate,
            dioType: DioType.api)
        .then((value) => NetworkResponse(
            isRequestSuccess: value.statusCode == 200,
            body: value.body,
            statusCode: value.statusCode));
  }

  @override
  Future<bool> addFavorite({required HotelData hotelContent}) {
    // TODO: implement addFavorite
    throw UnimplementedError();
  }

  @override
  Future<bool> removeFavorite({required String hotelCode}) {
    // TODO: implement removeFavorite
    throw UnimplementedError();
  }
}
