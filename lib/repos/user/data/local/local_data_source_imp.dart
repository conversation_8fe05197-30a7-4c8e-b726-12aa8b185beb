part of '../../user.dart';


class _LocalDataSourceImp implements _LocalDataSource{

  // facilities assets to get name of Facility
  final Box<Map> _box = Hive.box(HiveBoxTablesNames.FAVORITES_BOX);
  final Box<Map> _paymentBox = Hive.box(HiveBoxTablesNames.PAYMENT_METHOD);

  @override
  List<HotelData> getFavorites() {
    return _box.values.map((e) => HotelData.fromJson(e)).toList();
  }

  @override
  bool favExist(String code) {
    return _box.containsKey(code);
  }

  @override
  bool addFavorite({required HotelData hotelContent}) {
    try {
      _box.put(hotelContent.code, hotelContent.toJson());
      return true;
    } catch (e, s) {
      debugPrint("CATCH_ERROR  $e , $s");
      return false;
    }
  }

  @override
  bool addAllFavorite({required List<HotelData> hotels}) {
    for (var element in hotels) {
      addFavorite(hotelContent: element);
    }
    return true;
  }

  @override
  bool removeFavorite({required String hotelCode}) {
    try {
      _box.delete(hotelCode);
      return true;
    } catch (e, s) {
      debugPrint("Catch_error $e , $s");
      return false;
    }
  }


  @override
  Future<bool> removePayment({required String? id}) async {
    try {
      await _paymentBox.delete(id.toString());
      return true;
    } catch (e, s) {
      debugPrint("Catch_error $e , $s");
      return false;
    }
  }


}
