part of '../../hotels.dart';


class HotelsLocalDataSourceImp implements HotelsLocalDataSource{

  // facilities assets to get name of Facility
  @override
  Future<List<String>> facilities() async {
    // var jsonText = await rootBundle.loadString('assets/json/hotels.json');
    // final Map<String, dynamic> response = jsonDecode(jsonText);
    // return (response["facilities"] as List).map((e) => e).toList();
    return [];
  }



  @override
  Future<HotelData> getHotelTest() async {
    var jsonText = await rootBundle.loadString('assets/json/hotels.json');
    final Map<String, dynamic> response = jsonDecode(jsonText);
    throw UnimplementedError();
  }

}