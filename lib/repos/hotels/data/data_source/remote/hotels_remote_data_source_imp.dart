part of '../../hotels.dart';

class HotelsRemoteDataSourceImp implements HotelsRemoteDataSource {
  NetworkHandler networkHandler = NetworkHandler();


  @override
  Future<NetworkResponse<HotelsListResponse>> getHotelsByCodes(
      {List<int>? codes}) {
    var queryParams = Uri(queryParameters: {
      "fields": "all",
      "codes": (codes ?? []).join(","),
      "language": "ENG",
      "useSecondaryLanguage": "True",
      // "countryCode": "EG",
      "from": "1",
      "to": (codes?.length ?? 1000).toString(),
    }).query;
    return networkHandler.get(
        HotelsListResponse(), "${AppApis.hotelsContent}?$queryParams",
        dioType: DioType.api);
  }

  @override
  Future<NetworkResponse<HotelsListResponse>> getHotelsByCodesFilter(
      {List<int>? codes}) {
    var queryParams = Uri(queryParameters: {
      "codes": (codes ?? []).join(","),
    }).query;
    return networkHandler.get(
        HotelsListResponse(), "${AppApis.hotelsContentFilter}?$queryParams",
        dioType: DioType.api);
  }


  @override
  Future<NetworkResponse<HotelsListResponse>> getHotelDetails(
      {required int code}) {
    var queryParams = Uri(queryParameters: {
      "fields": "all",
      "language": "ENG",
      "useSecondaryLanguage": "True",
    }).query;
    return networkHandler.get(HotelsListResponse(),
        "${AppApis.hotels_contents}/hotels/$code/details?$queryParams",
        dioType: DioType.api);
  }

}
