part of '../hotels.dart';

abstract class HotelsRepo {
  static HotelsRepo get to => Get.find();

  Future<dartz.Either<BasicFailure, HotelsListResponse>> getHotelsByCodes(
      {List<int>? codes});

  Future<dartz.Either<BasicFailure, HotelsListResponse>> getHotelsByCodesFilter(
      {List<int>? codes});

  Future<dartz.Either<BasicFailure, HotelsListResponse>> getHotelDetails(
      {required int code});


  Future<NetworkResponse<HotelsListResponse>> fetchTestHotelsContent(
      {List<int>? codes});


}
