part of '../hotels.dart';

class HotelsRepoImp
    extends BaseRepository<HotelsRemoteDataSource, HotelsLocalDataSource>
    implements HotelsRepo {
  NetworkHandler networkHandler = NetworkHandler();

  // @override
  // Future<HotelsResult> getHotels(HotelRequest request) async {
  //   try {
  //     final NetworkResponse<HotelsSearchResponse> response = await remoteDataSource.getHotels(request);
  //
  //     if (response.isRequestSuccess && response.body != null) {
  //       return HotelsResult.success(response.body!);
  //     } else {
  //       return HotelsResult.failure(Failure.fromNetworkResponse(response));
  //     }
  //
  //   } catch (e, stackTrace) {
  //     // يمكنك طباعة stackTrace أثناء التطوير لمعرفة مكان الخطأ بسهولة
  //     debugPrint('Unexpected Error: $e');
  //     debugPrintStack(stackTrace: stackTrace);
  //
  //     return HotelsResult.failure(
  //       Failure.unexpected(message: e.toString()),
  //     );
  //   }
  // }



  @override
  Future<dartz.Either<BasicFailure, HotelsListResponse>> getHotelsByCodes(
      {List<int>? codes}) async {
    try {
      final NetworkResponse<HotelsListResponse> response =
          await remoteDataSource.getHotelsByCodes(codes: codes);
      if (response.isRequestSuccess) {
        return dartz.right(response.body!);
      } else {
        return dartz.left(BasicFailure.fromNetworkResponse(response));
      }
    } catch (e) {
      return dartz.left(BasicFailure(errorMassage: e.toString()));
    }
  }

  @override
  Future<dartz.Either<BasicFailure, HotelsListResponse>> getHotelsByCodesFilter(
      {List<int>? codes}) async {
    try {
      final NetworkResponse<HotelsListResponse> response =
          await remoteDataSource.getHotelsByCodesFilter(codes: codes);
      if (response.isRequestSuccess) {
        return dartz.right(response.body!);
      } else {
        return dartz.left(BasicFailure.fromNetworkResponse(response));
      }
    } catch (e) {
      return dartz.left(BasicFailure(errorMassage: e.toString()));
    }
  }


  // -------------------- TEST SECTION ------------------------------------------

  @override
  Future<NetworkResponse<HotelsListResponse>> fetchTestHotelsContent(
      {List<int>? codes}) async {
    var jsonText = await rootBundle.loadString('assets/json/hotels2.json');
    return NetworkResponse(
        body: HotelsListResponse.fromJson2(jsonDecode(jsonText)),
        isRequestSuccess: true);
  }


  @override
  Future<dartz.Either<BasicFailure, HotelsListResponse>> getHotelDetails(
      {required int code}) async {
    try {
      final NetworkResponse<HotelsListResponse> response =
          await remoteDataSource.getHotelDetails(code: code);
      if (response.isRequestSuccess) {
        return dartz.right(response.body!);
      } else {
        return dartz.left(BasicFailure.fromNetworkResponse(response));
      }
    } catch (e) {
      return dartz.left(BasicFailure(errorMassage: e.toString()));
    }
  }




}
