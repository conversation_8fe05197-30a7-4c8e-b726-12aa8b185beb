import 'dart:convert';
import 'package:fandooq/core/freezed/checkrate/checkrate.dart';
import 'package:fandooq/core/freezed/failure.dart';
import 'package:fandooq/core/models/availability/response/checkRateResponse.dart';
import 'package:fandooq/core/models/availability/response/hotels-list-content.dart';

import 'package:fandooq/core/models/hotels/hotels.dart';
import 'package:fandooq/core/models/hotels/city_availability_models.dart';
import 'package:fandooq/core/models/hotels/geolocation_hotel_search_models.dart';
import 'package:fandooq/core/models/hotels/geolocation_availability_models.dart'
    as geo_availability;
import 'package:fandooq/core/network_provider/networking.dart';
import 'package:fandooq/core/utils/base_classes/base_repository.dart';
import 'package:fandooq/feature_plus/hotels/models/hotel_data.dart';
import 'package:flutter/services.dart';
import 'package:dartz/dartz.dart' as dartz;
import 'package:get/get.dart';

part 'data_source/local/hotels_local_data_source.dart';
part 'data_source/local/hotels_local_data_source_imp.dart';

part 'data_source/remote/hotels_remote_data_source.dart';
part 'data_source/remote/hotels_remote_data_source_imp.dart';

part 'repo/hotels_repo.dart';
part 'repo/hotels_repo_imp.dart';

part 'binding.dart';
