import 'package:fandooq/core/components/free_place_search/lib/place_search.dart';
import 'package:fandooq/feature_plus/hotels/controllers/hotels_controller_plus.dart';
import 'package:fandooq/feature_plus/hotels/models/hotels_data_search.dart';
import 'package:fandooq/repos/hotels/data/hotels.dart';
import 'package:fandooq/repos/user/user.dart';
import 'package:get/get.dart';
import 'core/components-state/pages/country/country_code_picker.dart';
import 'package:fandooq/core/components/currency/lib/currency.dart';
import 'core/data_base/features/db_binding.dart';
import 'core/data_base/features/favorites/favorites_db_imp.dart';
import 'core/network_provider/networking.dart';
import 'core/setting_app/setting_app.dart';
import 'feature_plus/home/<USER>/guests_controller.dart';
import 'feature_plus/hotels/hotels_plus.dart';
import 'feature_plus/hotels/services/deep_link_service.dart';
import 'feature_plus/location_search/services/location_search_service.dart';
import 'feature_plus/settings/controllers/settings_controller.dart';
import 'feature_plus/user/controllers/user_controller_plus.dart';
import 'feature_plus/bookings/integration/booking_integration.dart';
import 'feature_plus/bookings/services/cancel_booking_service.dart';
import 'core/services/app_link_handler.dart';
import 'feature_plus/hotels/services/deep_link_service.dart';
import 'feature_plus/favorites/services/favorites_service_plus.dart';
import 'feature_plus/favorites/controllers/favorites_controller_plus.dart';
import 'repos/user/user.dart';
// Terms imports temporarily disabled
// import 'feature_plus/terms/services/terms_api_service.dart';
// import 'feature_plus/terms/services/terms_cache_service.dart';
// import 'feature_plus/terms/repositories/terms_repository.dart';
// import 'feature_plus/terms/controllers/terms_controller.dart';

import 'feature_plus/hotels/data/hotels_data_plus.dart';
import 'repos/new_repos.dart';

class InitBinding extends Bindings {
  @override
  void dependencies() {
    // TODO: implement dependencies

    /// Binding for DioClient
    Get.put<DioClient>(DioClient());

    /// Binding for DB
    DBBinding().dependencies();

    Get.put(HotelsDataPlus());

    Get.put(HotelsDataSearch());

    // Settings controller from feature_plus (priority over SettingsAppController)
    Get.put(SettingsController());

    // User controller from feature_plus (central user management)
    Get.put(UserControllerPlus(), permanent: true);

    Get.put(CurrencyController());

    Get.put(FavoriteDBImp());

    Get.put(ChangeLanguageController());

    Get.put(CountryCodeController()..init());

    // Get.put(SettingsRepoBinding());

    // New repositories for updated APIs
    NewReposBinding().dependencies();

    // Deep link service for hotel sharing
    Get.put(DeepLinkService());

    // App link handler for platform-specific deep links
    Get.put(AppLinkHandler());

    // Location search service
    Get.put(LocationSearchService());

    // Favorites service and controller (new API)
    Get.put(FavoritesServicePlus());
    Get.put(FavoritesControllerPlus());

    // Guests controller (needed for home page)
    Get.put(GuestsController());

    // User repository (needed for user services)
    Get.put<UserRepo>(UserRepoImp());

    // Hotels repository (needed for home page)
    Get.put<HotelsRepo>(HotelsRepoImp());

    // Booking cancellation service (shared across all booking features)
    Get.put(CancelBookingService());

    // Terms, Privacy & About services (new API) - Disabled temporarily
    // Get.lazyPut(() => TermsCacheService());
    // Get.lazyPut(() => TermsApiService());
    // Get.lazyPut(() => TermsRepository());
    // Get.lazyPut(() => TermsController());
  }
}
