import 'dart:io';
import 'package:fandooq/core/router/app_pages.dart';
import 'package:fandooq/feature_plus/notifications/integration/notification_integration_plus.dart';

void onAppReady() async {
  // await Repos.settings.getFacilities();
  // await Repos.settings.getIp();

  // تهيئة نظام الإشعارات
  await NotificationIntegrationPlus.initialize();
}

void onSettings(settingsResult) {
  if (settingsResult.isRequestSuccess) {
    var settings = settingsResult.body;

    if (settings.maintainAndroid && Platform.isAndroid) {
      Routes.offAllNamed(page: Routes.MAINTAIN_PAGE);
    }

    if (settings.maintainIos && Platform.isIOS) {
      Routes.offAllNamed(page: Routes.MAINTAIN_PAGE);
    }
  }
}
